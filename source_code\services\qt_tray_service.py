"""
基于PyQt6 QSystemTrayIcon的系统托盘服务模块
支持双击显示窗口功能
"""

import os
from typing import Optional, Callable, Dict
from PyQt6.QtWidgets import QSystemTrayIcon, QMenu, QApplication
from PyQt6.QtGui import QIcon, QPixmap, QAction
from PyQt6.QtCore import QObject, pyqtSignal

from core.constants import APP_ICON_FILENAME
from utils.system import resource_path, get_icon_path


class QtTrayService(QObject):
    """基于PyQt6 QSystemTrayIcon的系统托盘服务类"""
    
    # 信号定义
    showWindow = pyqtSignal()
    runCheck = pyqtSignal()
    quitApp = pyqtSignal()
    
    def __init__(self, app_name: str = "服务器检查工具", parent=None):
        super().__init__(parent)
        self.app_name = app_name
        self.tray_icon = None
        self.tray_menu = None
        self.is_visible = False
        self._callbacks = {}
        
        # 检查系统是否支持托盘图标
        if not QSystemTrayIcon.isSystemTrayAvailable():
            print("系统不支持托盘图标")
            return
        
        self.setup_tray_icon()
    
    def setup_tray_icon(self):
        """设置托盘图标"""
        try:
            # 创建托盘图标
            self.tray_icon = QSystemTrayIcon(self)
            
            # 加载图标
            icon = self.load_icon()
            if icon:
                self.tray_icon.setIcon(icon)
            
            # 设置提示文本
            self.tray_icon.setToolTip(self.app_name)
            
            # 创建右键菜单
            self.create_context_menu()
            
            # 连接双击信号
            self.tray_icon.activated.connect(self.tray_icon_activated)
            
            print("PyQt6托盘图标设置完成")
            
        except Exception as e:
            print(f"设置托盘图标时出错: {e}")
    
    def load_icon(self) -> Optional[QIcon]:
        """加载托盘图标"""
        try:
            # 首先尝试使用新的资源路径函数
            icon_path = get_icon_path(APP_ICON_FILENAME)
            if icon_path:
                try:
                    icon = QIcon(icon_path)
                    if not icon.isNull():
                        print(f"✅ 托盘图标加载成功: {icon_path}")
                        return icon
                except Exception as e:
                    print(f"❌ 加载图标失败: {icon_path}, 错误: {e}")

            # 如果新方法失败，回退到原有的多路径尝试方法
            print("回退到传统图标加载方法...")
            
            # 获取当前脚本目录
            current_dir = os.path.dirname(os.path.abspath(__file__))
            project_root = os.path.dirname(os.path.dirname(current_dir))

            # 可能的图标路径（按优先级排序）
            possible_paths = [
                # 使用resource_path处理的路径
                resource_path(APP_ICON_FILENAME),
                resource_path("app_icon.ico"),
                resource_path("app_icon.png"),
                # 项目根目录（最高优先级）
                os.path.join(project_root, APP_ICON_FILENAME),
                # 当前工作目录
                os.path.join(os.getcwd(), APP_ICON_FILENAME),
                # 项目根目录下的assets文件夹
                os.path.join(project_root, "source_code", "assets", APP_ICON_FILENAME),
                os.path.join(project_root, "source_code", "assets", "app_icon.ico"),
                os.path.join(project_root, "source_code", "assets", "app_icon.png"),
                # 当前目录相对路径
                os.path.join(current_dir, "..", "assets", APP_ICON_FILENAME),
                os.path.join(current_dir, "..", "assets", "app_icon.ico"),
                os.path.join(current_dir, "..", "assets", "app_icon.png"),
                # 直接文件名（当前目录）
                APP_ICON_FILENAME,
                "app_icon.ico",
                "app_icon.png"
            ]

            for icon_path in possible_paths:
                icon_path = os.path.normpath(icon_path)
                print(f"尝试加载图标: {icon_path}")
                if os.path.exists(icon_path):
                    try:
                        icon = QIcon(icon_path)
                        if not icon.isNull():
                            print(f"✅ 托盘图标加载成功: {icon_path}")
                            return icon
                        else:
                            print(f"❌ 图标文件无效: {icon_path}")
                    except Exception as e:
                        print(f"❌ 加载图标失败: {icon_path}, 错误: {e}")
                        continue
                else:
                    print(f"❌ 图标文件不存在: {icon_path}")

            print("⚠️ 未找到有效的图标文件，使用默认图标")
            # 如果没有找到图标文件，创建默认图标
            return self.create_default_icon()

        except Exception as e:
            print(f"加载托盘图标时出错: {e}")
            return self.create_default_icon()
    
    def create_default_icon(self) -> QIcon:
        """创建默认图标"""
        try:
            # 创建一个简单的默认图标
            pixmap = QPixmap(32, 32)
            pixmap.fill()  # 填充为白色
            
            # 可以在这里绘制一些简单的图形
            from PyQt6.QtGui import QPainter, QBrush, QColor
            painter = QPainter(pixmap)
            painter.setBrush(QBrush(QColor(0, 120, 215)))  # 蓝色
            painter.drawEllipse(4, 4, 24, 24)
            painter.end()
            
            return QIcon(pixmap)
            
        except Exception as e:
            print(f"创建默认图标时出错: {e}")
            return QIcon()
    
    def create_context_menu(self):
        """创建右键菜单"""
        try:
            self.tray_menu = QMenu()
            
            # 显示主窗口
            show_action = QAction("显示主窗口", self)
            show_action.triggered.connect(self.on_show_window)
            self.tray_menu.addAction(show_action)
            
            # 执行检查
            check_action = QAction("执行检查", self)
            check_action.triggered.connect(self.on_run_check)
            self.tray_menu.addAction(check_action)
            
            # 分隔线
            self.tray_menu.addSeparator()
            
            # 退出程序
            quit_action = QAction("退出程序", self)
            quit_action.triggered.connect(self.on_quit_app)
            self.tray_menu.addAction(quit_action)
            
            # 设置菜单
            self.tray_icon.setContextMenu(self.tray_menu)
            
            print("托盘右键菜单创建完成")
            
        except Exception as e:
            print(f"创建右键菜单时出错: {e}")
    
    def tray_icon_activated(self, reason):
        """托盘图标激活事件处理"""
        try:
            if reason == QSystemTrayIcon.ActivationReason.DoubleClick:
                print("检测到双击托盘图标")
                self.on_show_window()
            elif reason == QSystemTrayIcon.ActivationReason.Trigger:
                # 单击事件（可选）
                pass
            elif reason == QSystemTrayIcon.ActivationReason.Context:
                # 右键事件（由菜单自动处理）
                pass
                
        except Exception as e:
            print(f"处理托盘图标激活事件时出错: {e}")
    
    def on_show_window(self):
        """显示窗口"""
        print("托盘：显示窗口")
        self.showWindow.emit()
        if 'show_window' in self._callbacks:
            self._callbacks['show_window']()
    
    def on_run_check(self):
        """执行检查"""
        print("托盘：执行检查")
        self.runCheck.emit()
        if 'run_check' in self._callbacks:
            self._callbacks['run_check']()
    
    def on_quit_app(self):
        """退出应用"""
        print("托盘：退出应用")
        self.quitApp.emit()
        if 'quit_app' in self._callbacks:
            self._callbacks['quit_app']()
    
    def set_callback(self, event: str, callback: Callable):
        """设置事件回调"""
        self._callbacks[event] = callback
    
    def show(self) -> bool:
        """显示托盘图标"""
        try:
            if self.tray_icon and QSystemTrayIcon.isSystemTrayAvailable():
                self.tray_icon.show()
                self.is_visible = True
                print("PyQt6托盘图标已显示")
                return True
            else:
                print("无法显示托盘图标：系统不支持或图标未创建")
                return False
                
        except Exception as e:
            print(f"显示托盘图标时出错: {e}")
            return False
    
    def hide(self):
        """隐藏托盘图标"""
        try:
            if self.tray_icon:
                self.tray_icon.hide()
                self.is_visible = False
                print("PyQt6托盘图标已隐藏")
                
        except Exception as e:
            print(f"隐藏托盘图标时出错: {e}")
    
    def update_tooltip(self, tooltip: str):
        """更新托盘图标提示文本"""
        try:
            if self.tray_icon:
                self.tray_icon.setToolTip(tooltip)
                
        except Exception as e:
            print(f"更新提示文本时出错: {e}")
    
    def update_icon(self, icon: QIcon):
        """更新托盘图标图像"""
        try:
            if self.tray_icon:
                self.tray_icon.setIcon(icon)
                
        except Exception as e:
            print(f"更新图标时出错: {e}")
    
    def is_icon_visible(self) -> bool:
        """检查托盘图标是否可见"""
        return self.is_visible and self.tray_icon is not None
    
    def show_message(self, title: str, message: str, icon=QSystemTrayIcon.MessageIcon.Information, timeout: int = 5000):
        """显示托盘通知消息"""
        try:
            if self.tray_icon and self.is_visible:
                self.tray_icon.showMessage(title, message, icon, timeout)
                
        except Exception as e:
            print(f"显示托盘消息时出错: {e}")
    
    def cleanup(self):
        """清理资源"""
        try:
            # 1. 首先隐藏托盘图标
            self.hide()
            
            # 2. 断开所有信号连接
            try:
                if self.tray_icon:
                    self.tray_icon.activated.disconnect()
            except Exception:
                pass  # 信号可能已经断开
            
            # 3. 清理托盘图标
            if self.tray_icon:
                try:
                    # 确保托盘图标完全隐藏
                    self.tray_icon.hide()
                    # 设置为None以避免引用
                    self.tray_icon.setContextMenu(None)
                    # 延迟删除
                    self.tray_icon.deleteLater()
                    self.tray_icon = None
                except Exception as e:
                    print(f"清理托盘图标时出错: {e}")
            
            # 4. 清理右键菜单
            if self.tray_menu:
                try:
                    # 清除所有动作
                    self.tray_menu.clear()
                    # 延迟删除
                    self.tray_menu.deleteLater()
                    self.tray_menu = None
                except Exception as e:
                    print(f"清理托盘菜单时出错: {e}")
            
            # 5. 清理回调函数
            self._callbacks.clear()
            
            # 6. 重置状态
            self.is_visible = False
            
            # 7. 强制处理删除事件
            try:
                from PyQt6.QtWidgets import QApplication
                app = QApplication.instance()
                if app:
                    app.processEvents()
            except Exception:
                pass
            
            print("PyQt6托盘服务清理完成")
            
        except Exception as e:
            print(f"清理托盘服务时出错: {e}")
    
    def __del__(self):
        """析构函数"""
        self.cleanup()


# 兼容性别名，保持与原TrayService的接口一致
class TrayService(QtTrayService):
    """兼容性别名"""
    pass
