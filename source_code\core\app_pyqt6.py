"""
主应用程序模块 - PyQt6版本（重构后）
整合所有功能模块，提供统一的应用程序入口
"""

import os
import sys
import threading
import time
from typing import Dict, List, Optional, Any
from concurrent.futures import ThreadPoolExecutor, as_completed

from PyQt6.QtWidgets import Q<PERSON><PERSON><PERSON><PERSON><PERSON>, QWidget, QApplication, QMessageBox, QFrame, QLabel
from PyQt6.QtCore import Qt, QTimer, QThread, pyqtSignal, QObject
from PyQt6.QtGui import QIcon, QCloseEvent

# 内部模块导入
from core.config import AppConfig
from core.constants import WINDOW_TITLE
from core.app_helpers import AppHelpers
from core.file_manager import FileManager
from core.schedule_manager import ScheduleManager
from core.upload_manager import UploadManager
from core.ui_coordinator import UICoordinator
from core.event_handler import EventHandler
from core.startup_settings_manager import StartupSettingsManager

from services.unified_network_service import UnifiedNetworkService as NetworkService
from services.optimized_file_service import OptimizedFileService as FileService
from services.qt_scheduler_service import QtSchedulerService as SchedulerService
from services.qt_tray_service import QtTrayService as TrayService
from services.configuration_service import ConfigurationService
from utils.pyqt6_threading import PyQt6ThreadManager, SafeUIUpdater, ThreadSafeLogger
from utils.sequential_executor import SequentialExecutor, StepStatus
from utils.performance_optimizer import memory_manager, performance_monitor
from services.file_processing_service import FileProcessingService
from utils.startup_optimization import StartupOptimizationImplementer


class StartupManager(QObject):
    """
    启动管理器，负责分阶段、异步加载应用程序资源，以优化启动速度。
    """
    initialization_progress = pyqtSignal(int, str)
    initialization_complete = pyqtSignal()
    services_ready = pyqtSignal(dict)

    def __init__(self, parent=None):
        super().__init__(parent)
        self.services = {}

    def run_initialization(self):
        """开始执行异步初始化流程"""
        try:
            self.initialization_progress.emit(10, "正在加载核心服务...")
            
            # 异步加载服务模块
            self.services['NetworkService'] = NetworkService
            self.services['FileService'] = FileService
            self.services['SchedulerService'] = SchedulerService
            self.services['TrayService'] = TrayService
            self.services['ConfigurationService'] = ConfigurationService
            
            self.initialization_progress.emit(40, "核心服务加载完毕。")
            time.sleep(0.1) # 模拟IO延迟

            self.initialization_progress.emit(50, "正在加载工具模块...")
            self.services['PyQt6ThreadManager'] = PyQt6ThreadManager
            
            self.initialization_progress.emit(70, "工具模块加载完毕。")
            time.sleep(0.1)

            self.initialization_progress.emit(80, "正在加载辅助模块...")
            self.services['AppHelpers'] = AppHelpers
            
            self.initialization_progress.emit(90, "所有模块加载完成。")
            
            # 发射信号，通知主线程服务已准备好，但尚未实例化
            self.services_ready.emit(self.services)
            
            # 最终完成信号
            self.initialization_complete.emit()
            
        except Exception as e:
            print(f"初始化过程中发生错误: {e}")
            self.initialization_progress.emit(100, f"初始化失败: {e}")


class ServerCheckerApp(QMainWindow):
    """服务器检查工具主应用程序类 - 重构版"""
    
    def __init__(self):
        super().__init__()
        
        # --- 阶段1: 立即执行的最小化设置 ---
        self.setWindowTitle(WINDOW_TITLE)
        self.config = AppConfig() # 配置加载器必须先初始化
        
        # 初始化管理器组件
        self.ui_coordinator = UICoordinator(self)
        self.file_manager = FileManager(self)
        self.schedule_manager = ScheduleManager(self)
        self.upload_manager = UploadManager(self)
        self.event_handler = EventHandler(self)
        self.startup_settings_manager = StartupSettingsManager(self)
        
        # 初始化其他组件
        self.sequential_executor = SequentialExecutor(self)
        
        # UI和服务组件引用（将在初始化后设置）
        self.services = {}
        self.network_service = None
        self.file_service = None
        self.file_processing_service = None  # 新的文件处理服务
        self.configuration_service = None    # 新的配置管理服务
        self.scheduler_service = None
        self.tray_service = None
        self.thread_manager = None
        self.gameclient_controller = None
        self.helpers = None
        
        # 操作状态
        self.cancel_event = None
        self.current_thread = None
        
        # 设置缩放和基本窗口
        self.ui_coordinator.setup_scaling()
        self.ui_coordinator.setup_basic_window()
        
        # 显示加载界面
        self.ui_coordinator.show_loading_screen("正在初始化...")
        
        # --- 阶段2: 启动异步初始化 ---
        self.startup_thread = QThread()
        self.startup_manager = StartupManager()
        self.startup_manager.moveToThread(self.startup_thread)

        # 连接信号
        self.startup_manager.initialization_progress.connect(self.ui_coordinator.update_loading_status)
        self.startup_manager.services_ready.connect(self.on_services_ready)
        self.startup_manager.initialization_complete.connect(self.on_initialization_complete)
        self.startup_thread.started.connect(self.startup_manager.run_initialization)
        
        # 启动后台初始化
        self.startup_thread.start()

    def on_services_ready(self, services):
        """当服务模块加载完成后，在主线程中实例化它们"""
        self.services = services
        
        # 实例化核心服务
        self.network_service = self.services['NetworkService'](parent=self)
        self.file_service = self.services['FileService']()
        self.scheduler_service = self.services['SchedulerService'](parent=self)
        self.tray_service = self.services['TrayService'](parent=self)
        self.thread_manager = self.services['PyQt6ThreadManager'](self)
        
        # 实例化新的文件处理服务和配置管理服务
        self.file_processing_service = FileProcessingService(self)
        self.configuration_service = self.services['ConfigurationService'](self)
        
        # 连接文件处理服务的信号
        self.file_processing_service.progress_updated.connect(self._on_file_processing_progress)
        self.file_processing_service.processing_completed.connect(self.check_completed)
        
        # 连接配置管理服务的信号
        self.configuration_service.config_loaded.connect(self._on_config_loaded)
        self.configuration_service.state_restored.connect(self._on_state_restored)
        
        # 实例化控制器和辅助类
        # 注意：log方法此时还未完全可用，需要一个临时的logger
        temp_logger = lambda msg, level="INFO": print(f"[{level}] {msg}")
        from utils.process_control import GameClientController
        self.gameclient_controller = GameClientController(temp_logger)
        self.helpers = self.services['AppHelpers'](self)
        
        # 连接信号
        self.network_service.dataReceived.connect(self.event_handler.handle_network_data)
        self.network_service.errorOccurred.connect(self.event_handler.handle_network_error)
        self.scheduler_service.jobExecuted.connect(self.event_handler.handle_job_executed)
        self.scheduler_service.jobError.connect(self.event_handler.handle_job_error)

        self.ui_coordinator.update_loading_status(95, "正在构建主界面...")

    def on_initialization_complete(self):
        """所有后台初始化完成后的收尾工作"""
        self.startup_thread.quit()
        self.startup_thread.wait()

        # --- 阶段3: 构建完整UI并加载配置 ---
        self.ui_coordinator.init_ui_variables()
        self.ui_coordinator.create_main_ui()
        
        # 替换临时logger
        self.gameclient_controller.log_func = self.log
        
        # --- 阶段4: 延迟加载和启动 ---
        QTimer.singleShot(200, self.post_startup_setup)

    def post_startup_setup(self):
        """在主窗口显示后执行的非关键性任务"""
        self.log("执行启动后任务...", "INFO")
        
        # 设置窗口图标
        self.ui_coordinator.setup_window_icon()
        
        # 启动服务
        self.scheduler_service.start()
        self.log("调度器服务已启动", "INFO")
        
        # 系统集成
        self.event_handler.setup_system_integration()
        
        # 使用配置管理服务加载配置和状态
        self.configuration_service.load_application_config()
        self.configuration_service.restore_application_state()
        self.event_handler.load_system_integration_settings()
        
        # 初始化GameClient调度
        self.schedule_manager.initialize_gameclient_schedules()

        # 异步刷新磁盘列表
        self.thread_manager.start_thread(target=self.file_manager.refresh_disk_list)
        
        # 设置顺序执行器信号
        self.event_handler.setup_sequential_executor_signals()
        
        self.log("启动流程优化完成！", "SUCCESS")

    # ==================== 配置管理信号处理 ====================
    
    def _on_config_loaded(self, config_data: Dict[str, Any]):
        """配置加载完成的信号处理"""
        self.log(f"配置数据已加载，包含 {len(config_data)} 个配置项", "DEBUG")
    
    def _on_state_restored(self):
        """状态恢复完成的信号处理"""
        self.log("应用程序状态恢复完成", "DEBUG")

    # ==================== 日志方法委托 ====================
    
    def log(self, message: str, level: str = "INFO"):
        """日志记录委托到UI协调器"""
        self.ui_coordinator.log(message, level)

    # ==================== UI状态管理委托 ====================
    
    def update_check_button_state(self):
        """更新检查按钮状态委托"""
        self.ui_coordinator.update_check_button_state()

    def update_buttons_state(self, searching: bool = False, has_files: bool = False):
        """更新按钮状态委托"""
        self.ui_coordinator.update_buttons_state(searching, has_files)

    # ==================== 文件处理功能委托 ====================

    def start_check(self):
        """开始检查和更新（简化版本）"""
        if self.ui_coordinator.is_checking:
            return

        selected_files = self.file_manager.get_selected_files()
        if not selected_files:
            QMessageBox.warning(self, "警告", "请选择至少一个文件进行检查")
            return

        self.log(f"开始检查 {len(selected_files)} 个文件", "INFO")

        # 设置取消事件
        self.cancel_event = threading.Event()
        self.file_service.set_cancel_event(self.cancel_event)
        self.file_processing_service.set_cancel_event(self.cancel_event)

        # 更新UI状态
        self.ui_coordinator.is_checking = True
        self.ui_coordinator.is_cancelled = False
        self.update_buttons_state(searching=True, has_files=True)
        self.ui_coordinator.status_bar.showMessage("正在检查文件...")
        self.ui_coordinator.progress_bar.setVisible(True)

        # 使用文件处理服务
        self.current_thread = self.thread_manager.start_thread(
            target=self.file_processing_service.process_files,
            args=(selected_files,),
            callback=self.check_completed
        )

    def _on_file_processing_progress(self, progress: int, status_msg: str):
        """处理文件处理进度更新"""
        SafeUIUpdater.call_in_main_thread(self.ui_coordinator.progress_bar.setValue, progress)
        SafeUIUpdater.call_in_main_thread(self.ui_coordinator.status_bar.showMessage, status_msg)

    def check_completed(self, results):
        """检查完成回调（简化版本）"""
        self.ui_coordinator.is_checking = False

        if results:
            success_count = sum(1 for r in results if r.get("success", False))
            updated_count = sum(1 for r in results if r.get("success", False) and r.get("updated", False))
            total_count = len(results)

            # 生成简洁的结果摘要
            self._generate_completion_summary(success_count, updated_count, total_count)
            self._log_failed_files(results)
        else:
            self.log("检查完成，但没有结果", "WARNING")

        # 重置UI状态
        self._reset_ui_after_completion()

    def _process_single_file(self, file_path: str):
        """
        处理单个文件的委托方法

        这个方法是为了兼容性而添加的，实际处理委托给文件处理服务

        Args:
            file_path: 文件路径

        Returns:
            处理结果字典
        """
        if not hasattr(self, 'file_processing_service') or not self.file_processing_service:
            self.log("文件处理服务未初始化", "ERROR")
            return {"file": file_path, "success": False, "error": "文件处理服务未初始化"}

        try:
            return self.file_processing_service._process_single_file(file_path)
        except Exception as e:
            self.log(f"处理文件 {file_path} 时出错: {e}", "ERROR")
            return {"file": file_path, "success": False, "error": str(e)}

    def _generate_completion_summary(self, success_count: int, updated_count: int, total_count: int):
        """生成完成摘要"""
        if success_count == total_count:
            if updated_count > 0:
                self.log(f"✅ 检查完成：{updated_count} 个文件已更新", "SUCCESS")
            else:
                self.log(f"✅ 检查完成：所有文件已是最新", "SUCCESS")
        else:
            self.log(f"⚠️ 检查完成：成功 {success_count}/{total_count}，更新 {updated_count} 个", "WARNING")

    def _log_failed_files(self, results: List[Dict]):
        """记录失败的文件"""
        for result in results:
            if not result.get("success"):
                file_name = os.path.basename(result.get("file", "未知文件"))
                error_msg = result.get("error", "未知错误")
                self.log(f"  ✗ {file_name}: {error_msg}", "ERROR")

    def _reset_ui_after_completion(self):
        """完成后重置UI状态"""
        self.update_buttons_state(searching=False, has_files=len(self.file_manager.file_checkboxes) > 0)
        self.ui_coordinator.status_bar.showMessage("就绪")
        self.ui_coordinator.progress_bar.setVisible(False)
        self.ui_coordinator.progress_bar.setValue(0)

    def cancel_operation(self):
        """取消当前操作（委托实现）"""
        # 检查是否有顺序执行正在进行
        if self.sequential_executor.is_execution_running():
            self.log("正在取消顺序执行...", "WARNING")
            self.sequential_executor.cancel_execution()
            return

        # 检查是否有其他操作正在进行
        if self.cancel_event:
            self.log("正在发送取消信号...", "WARNING")
            self.cancel_event.set()
        else:
            self.log("没有正在进行的操作可以取消。", "INFO")

    # ==================== GameClient控制功能委托 ====================

    def control_gameclient(self, file_path: str, action: str):
        """控制GameClient.exe进程（委托实现）"""
        try:
            return self.gameclient_controller.control_gameclient(file_path, action)
        except Exception as e:
            self.log(f"控制GameClient时出错: {e}", "ERROR")
            return False

    def control_gameclient_threaded(self, file_path: str, action: str):
        """在线程中控制GameClient.exe进程（委托实现）"""
        try:
            self.gameclient_controller.control_gameclient_threaded(file_path, action)
        except Exception as e:
            self.log(f"创建GameClient控制线程时出错: {e}", "ERROR")

    # ==================== 配置管理委托 ====================

    def load_application_config(self):
        """加载应用程序配置（委托给配置服务）"""
        return self.configuration_service.load_application_config()

    def restore_ui_state(self):
        """恢复UI状态（委托给配置服务）"""
        return self.configuration_service.restore_ui_state()

    # ==================== 系统设置委托 ====================
    
    def open_system_settings(self):
        """打开系统设置对话框 - 委托给事件处理器"""
        self.event_handler.open_system_settings()

    # ==================== 兼容性属性和委托 ====================
    
    # 这些属性提供向后兼容性，让原有代码能正常工作
    @property
    def QLabel(self):
        """向后兼容性属性"""
        from PyQt6.QtWidgets import QLabel
        return QLabel
        
    @property
    def QFrame(self):
        """向后兼容性属性"""
        from PyQt6.QtWidgets import QFrame
        return QFrame

    # UI属性委托
    @property 
    def log_text(self):
        return self.ui_coordinator.log_text
    
    @property
    def status_bar(self):
        return self.ui_coordinator.status_bar
    
    @property
    def progress_bar(self):
        return self.ui_coordinator.progress_bar
    
    @property
    def check_button(self):
        return self.ui_coordinator.check_button
    
    @property
    def cancel_button(self):
        return self.ui_coordinator.cancel_button
        
    @property
    def scale_factor(self):
        return self.ui_coordinator.scale_factor
    
    @property
    def initial_width(self):
        return self.ui_coordinator.initial_width
    
    @property
    def initial_height(self):
        return self.ui_coordinator.initial_height

    # 状态属性委托
    @property
    def is_checking(self):
        return self.ui_coordinator.is_checking
    
    @is_checking.setter
    def is_checking(self, value):
        self.ui_coordinator.is_checking = value

    @property
    def is_cancelled(self):
        return self.ui_coordinator.is_cancelled
    
    @is_cancelled.setter
    def is_cancelled(self, value):
        self.ui_coordinator.is_cancelled = value

    # ==================== 窗口事件处理委托 ====================

    def closeEvent(self, event: QCloseEvent):
        """处理窗口关闭事件（委托给事件处理器）"""
        self.event_handler.closeEvent(event)