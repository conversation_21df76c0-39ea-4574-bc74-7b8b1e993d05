"""
崩溃修复工具
专门修复"立即执行"按钮导致的崩溃问题
"""

import threading
from typing import Callable, Any
from PyQt6.QtCore import QObject, pyqtSignal, QTimer, QThread
from PyQt6.QtWidgets import QApplication


class CrashSafeExecutor(QObject):
    """崩溃安全执行器"""
    
    # 信号定义
    progress_updated = pyqtSignal(str)  # 进度消息
    step_completed = pyqtSignal(str, bool, str)  # 步骤名, 成功, 消息
    execution_finished = pyqtSignal(bool, str)  # 成功, 总结消息
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self._is_running = False
        self._current_thread = None
    
    def is_running(self) -> bool:
        """检查是否正在运行"""
        return self._is_running
    
    def execute_steps_safely(self, steps: list, app_instance):
        """安全执行步骤列表"""
        if self._is_running:
            return False
        
        self._is_running = True
        
        # 在新线程中执行步骤
        self._current_thread = CrashSafeWorkerThread(steps, app_instance)
        self._current_thread.progress_updated.connect(self.progress_updated.emit)
        self._current_thread.step_completed.connect(self.step_completed.emit)
        self._current_thread.execution_finished.connect(self._on_execution_finished)
        self._current_thread.start()
        
        return True
    
    def _on_execution_finished(self, success: bool, message: str):
        """执行完成处理"""
        self._is_running = False
        self._current_thread = None
        self.execution_finished.emit(success, message)
    
    def cancel_execution(self):
        """取消执行"""
        if self._current_thread and self._current_thread.isRunning():
            self._current_thread.requestInterruption()
            self._current_thread.wait(5000)  # 等待5秒
        self._is_running = False


class CrashSafeWorkerThread(QThread):
    """崩溃安全工作线程"""
    
    progress_updated = pyqtSignal(str)
    step_completed = pyqtSignal(str, bool, str)
    execution_finished = pyqtSignal(bool, str)
    
    def __init__(self, steps: list, app_instance):
        super().__init__()
        self.steps = steps
        self.app_instance = app_instance
        self.success_count = 0
        self.total_steps = len(steps)
    
    def run(self):
        """运行步骤"""
        try:
            for i, step in enumerate(self.steps):
                if self.isInterruptionRequested():
                    break
                
                step_name = step.get('name', f'步骤{i+1}')
                step_description = step.get('description', f'执行步骤{i+1}')
                step_func = step.get('func')
                step_args = step.get('args', ())
                
                # 发送步骤开始信号
                self.progress_updated.emit(f"开始执行: {step_description}")
                
                try:
                    # 执行步骤
                    if step_args:
                        result = step_func(*step_args)
                    else:
                        result = step_func()
                    
                    if result:
                        self.success_count += 1
                        self.step_completed.emit(step_name, True, "步骤完成")
                    else:
                        self.step_completed.emit(step_name, False, "步骤失败")
                        
                except Exception as e:
                    error_msg = f"步骤异常: {str(e)}"
                    self.step_completed.emit(step_name, False, error_msg)
                    print(f"步骤执行异常: {e}")
                
                # 短暂延迟
                self.msleep(100)
            
            # 发送完成信号
            if self.success_count == self.total_steps:
                self.execution_finished.emit(True, f"所有步骤完成 ({self.success_count}/{self.total_steps})")
            else:
                self.execution_finished.emit(False, f"部分步骤失败 ({self.success_count}/{self.total_steps})")
                
        except Exception as e:
            self.execution_finished.emit(False, f"执行异常: {str(e)}")


class SafeProgressReporter:
    """安全进度报告器"""
    
    def __init__(self, log_func: Callable):
        self.log_func = log_func
        self._last_report_time = 0
        self._report_interval = 1.0  # 1秒间隔
    
    def report_progress(self, message: str):
        """报告进度（限制频率）"""
        import time
        current_time = time.time()
        
        if current_time - self._last_report_time >= self._report_interval:
            self._last_report_time = current_time
            try:
                # 使用QTimer.singleShot确保在主线程中执行
                QTimer.singleShot(0, lambda: self._safe_log(message))
            except Exception as e:
                print(f"进度报告异常: {e}")
    
    def _safe_log(self, message: str):
        """安全记录日志"""
        try:
            if self.log_func:
                self.log_func(message, "INFO")
        except Exception as e:
            print(f"日志记录异常: {e}")


def create_safe_immediate_execution(app_instance):
    """创建安全的立即执行功能"""
    
    def safe_sync_system_time():
        """安全的系统时间同步"""
        try:
            app_instance.log("步骤1：开始同步系统时间...", "INFO")
            from utils.system import sync_system_time
            success = sync_system_time()
            
            if success:
                app_instance.log("系统时间同步成功", "SUCCESS")
            else:
                app_instance.log("系统时间同步失败，但继续执行", "WARNING")
            
            return True  # 总是返回True，即使失败也继续
            
        except Exception as e:
            app_instance.log(f"同步系统时间异常: {e}", "ERROR")
            return True  # 即使异常也继续执行
    
    def safe_main_check(selected_files):
        """安全的主要检查功能"""
        try:
            app_instance.log(f"步骤2：开始检查 {len(selected_files)} 个文件...", "INFO")

            # 检查文件处理服务是否可用
            if not hasattr(app_instance, 'file_processing_service') or not app_instance.file_processing_service:
                app_instance.log("文件处理服务未初始化，尝试使用备用方法", "WARNING")
                # 如果文件处理服务不可用，尝试使用标准的start_check方法
                try:
                    app_instance.start_check()
                    return True
                except Exception as e:
                    app_instance.log(f"备用方法也失败: {e}", "ERROR")
                    return False

            # 简化的文件处理，避免复杂的线程操作
            results = []
            for i, file_path in enumerate(selected_files):
                try:
                    # 修复：使用正确的文件处理服务方法
                    result = app_instance.file_processing_service._process_single_file(file_path)
                    results.append(result)

                    # 简单的进度报告
                    progress = (i + 1) / len(selected_files) * 100
                    if i % 5 == 0:  # 每5个文件报告一次进度
                        app_instance.log(f"处理进度: {int(progress)}%", "INFO")

                except Exception as e:
                    app_instance.log(f"处理文件 {file_path} 时出错: {e}", "ERROR")
                    results.append({"file": file_path, "success": False, "error": str(e)})

            success_count = sum(1 for r in results if r.get("success", False))
            app_instance.log(f"文件检查完成，成功: {success_count}/{len(results)}", "SUCCESS")

            return success_count > 0

        except Exception as e:
            app_instance.log(f"执行文件检查时出错: {e}", "ERROR")
            return False
    
    def safe_gameclient_control(selected_files):
        """安全的GameClient控制功能"""
        try:
            app_instance.log("步骤3：开始控制GameClient功能...", "INFO")
            
            success_count = 0
            for file_path in selected_files:
                try:
                    # 获取文件配置
                    file_config = app_instance.config.get_file_config(file_path)
                    advanced_settings = file_config.get("advanced_settings", {})
                    
                    # 检查是否启用了GameClient定时任务
                    if not advanced_settings.get("schedule_gameclient_enabled", False):
                        continue
                    
                    # 获取配置的操作
                    action = advanced_settings.get("schedule_gameclient_action", "打开")
                    
                    # 执行GameClient控制
                    success = app_instance.gameclient_controller.control_gameclient(file_path, action)
                    
                    if success:
                        success_count += 1
                        app_instance.log(f"GameClient控制成功: {file_path} ({action})", "SUCCESS")
                    else:
                        app_instance.log(f"GameClient控制失败: {file_path}", "ERROR")
                        
                except Exception as e:
                    app_instance.log(f"控制GameClient时出错 ({file_path}): {e}", "ERROR")
            
            app_instance.log(f"GameClient控制完成，成功: {success_count}/{len(selected_files)}", "SUCCESS")
            return success_count > 0
            
        except Exception as e:
            app_instance.log(f"执行GameClient控制时出错: {e}", "ERROR")
            return False
    
    # 创建安全执行器
    safe_executor = CrashSafeExecutor()
    
    def execute_immediate_check_safely():
        """安全执行立即检查"""
        if safe_executor.is_running():
            app_instance.log("已有任务正在执行中，请等待完成", "WARNING")
            return
        
        # 获取选中的文件 - 修复：使用 file_manager
        selected_files = app_instance.file_manager.get_selected_files()
        if not selected_files and app_instance.file_manager.file_checkboxes:
            # 自动选择所有文件
            for item in app_instance.file_manager.file_checkboxes:
                item["checkbox"].setChecked(True)
            selected_files = app_instance.file_manager.get_selected_files()
        
        if not selected_files:
            app_instance.log("没有可检查的文件，请先添加文件", "WARNING")
            return
        
        app_instance.log("开始安全版立即执行：将按顺序执行三个步骤", "INFO")
        
        # 准备步骤
        steps = [
            {
                'name': 'sync_time',
                'description': '同步系统时间',
                'func': safe_sync_system_time
            },
            {
                'name': 'main_check',
                'description': '执行文件检查和更新',
                'func': safe_main_check,
                'args': (selected_files,)
            },
            {
                'name': 'gameclient_control',
                'description': '控制GameClient功能',
                'func': safe_gameclient_control,
                'args': (selected_files,)
            }
        ]
        
        # 更新UI状态 - 修复：通过 schedule_manager 访问按钮
        app_instance.schedule_manager.run_now_button.setEnabled(False)
        app_instance.schedule_manager.run_now_button.setText("执行中...")
        
        # 连接信号
        safe_executor.progress_updated.connect(
            lambda msg: app_instance.log(msg, "INFO")
        )
        safe_executor.step_completed.connect(
            lambda name, success, msg: app_instance.log(
                f"{'✓' if success else '✗'} {msg}", 
                "SUCCESS" if success else "ERROR"
            )
        )
        safe_executor.execution_finished.connect(
            lambda success, msg: _on_safe_execution_finished(app_instance, success, msg)
        )
        
        # 开始执行
        success = safe_executor.execute_steps_safely(steps, app_instance)
        if not success:
            app_instance.log("启动安全执行失败", "ERROR")
            _reset_ui_state(app_instance)
    
    return execute_immediate_check_safely, safe_executor


def _on_safe_execution_finished(app_instance, success: bool, message: str):
    """安全执行完成处理"""
    if success:
        app_instance.log(f"🎉 立即执行完成: {message}", "SUCCESS")
    else:
        app_instance.log(f"⚠ 立即执行结束: {message}", "WARNING")
    
    _reset_ui_state(app_instance)


def _reset_ui_state(app_instance):
    """重置UI状态"""
    try:
        # 修复：通过 schedule_manager 访问按钮
        app_instance.schedule_manager.run_now_button.setEnabled(True)
        app_instance.schedule_manager.run_now_button.setText("立即执行")
    except Exception as e:
        print(f"重置UI状态时出错: {e}")


def apply_crash_fix(app_instance):
    """应用崩溃修复"""
    safe_func, safe_executor = create_safe_immediate_execution(app_instance)
    
    # 替换原有的立即执行方法
    app_instance.run_immediate_check_enhanced_safe = safe_func
    app_instance.safe_executor = safe_executor
    
    print("崩溃修复已应用：立即执行功能已替换为安全版本")
