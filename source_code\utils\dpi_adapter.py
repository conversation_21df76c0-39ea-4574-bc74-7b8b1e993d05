"""
DPI适配工具模块
专门处理不同分辨率和DPI缩放下的界面适配问题
"""

import sys
import ctypes
from typing import Tuple, Optional
from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import QSize
from PyQt6.QtGui import QFont


class DPIAdapter:
    """DPI适配器类"""
    
    def __init__(self):
        self._scale_factor = None
        self._screen_info = None
        self._font_scale = None
    
    @property
    def scale_factor(self) -> float:
        """获取缩放因子"""
        if self._scale_factor is None:
            self._scale_factor = self._calculate_scale_factor()
        return self._scale_factor
    
    @property
    def font_scale(self) -> float:
        """获取字体缩放因子"""
        if self._font_scale is None:
            self._font_scale = self._calculate_font_scale()
        return self._font_scale
    
    def _calculate_scale_factor(self) -> float:
        """计算界面缩放因子"""
        try:
            # 获取系统DPI
            if sys.platform == 'win32':
                system_dpi = ctypes.windll.user32.GetDpiForSystem()
                raw_scale = system_dpi / 96.0
            else:
                # 非Windows系统使用Qt的方法
                app = QApplication.instance()
                if app:
                    screen = app.primaryScreen()
                    if screen:
                        raw_scale = screen.logicalDotsPerInch() / 96.0
                    else:
                        raw_scale = 1.0
                else:
                    raw_scale = 1.0
            
            print(f"Raw DPI scale factor: {raw_scale:.2f}")
            
            # 智能缩放策略
            if raw_scale <= 1.0:
                # 100%或更小，保持1.0
                return 1.0
            elif raw_scale <= 1.25:
                # 125%，保持原样
                return raw_scale
            elif raw_scale <= 1.5:
                # 150%，稍微减小
                return raw_scale * 0.95
            elif raw_scale <= 2.0:
                # 200%，适度减小
                return raw_scale * 0.85
            else:
                # 更高缩放，限制最大值
                return 1.6
                
        except Exception as e:
            print(f"Error calculating scale factor: {e}")
            return 1.0
    
    def _calculate_font_scale(self) -> float:
        """计算字体缩放因子"""
        base_scale = self.scale_factor
        
        # 字体缩放通常比界面缩放稍小
        if base_scale <= 1.0:
            return 1.0
        elif base_scale <= 1.25:
            return base_scale
        else:
            # 高DPI下字体缩放更保守
            return min(base_scale * 0.9, 1.4)
    
    def get_screen_info(self) -> dict:
        """获取屏幕信息"""
        if self._screen_info is None:
            self._screen_info = self._collect_screen_info()
        return self._screen_info
    
    def _collect_screen_info(self) -> dict:
        """收集屏幕信息"""
        info = {
            'width': 1920,
            'height': 1080,
            'available_width': 1920,
            'available_height': 1040,
            'dpi': 96,
            'scale_factor': 1.0
        }
        
        try:
            app = QApplication.instance()
            if app:
                screen = app.primaryScreen()
                if screen:
                    geometry = screen.geometry()
                    available = screen.availableGeometry()
                    
                    info.update({
                        'width': geometry.width(),
                        'height': geometry.height(),
                        'available_width': available.width(),
                        'available_height': available.height(),
                        'dpi': screen.logicalDotsPerInch(),
                        'scale_factor': self.scale_factor
                    })
        except Exception as e:
            print(f"Error collecting screen info: {e}")
        
        return info
    
    def scale_size(self, width: int, height: int) -> Tuple[int, int]:
        """缩放尺寸"""
        scaled_width = int(width * self.scale_factor)
        scaled_height = int(height * self.scale_factor)
        return scaled_width, scaled_height
    
    def scale_font_size(self, base_size: int) -> int:
        """缩放字体大小"""
        scaled_size = int(base_size * self.font_scale)
        return max(8, min(scaled_size, 24))  # 限制字体大小范围
    
    def get_optimal_window_size(self, base_width: int, base_height: int) -> Tuple[int, int]:
        """获取最优窗口尺寸"""
        screen_info = self.get_screen_info()
        
        # 缩放基础尺寸
        scaled_width, scaled_height = self.scale_size(base_width, base_height)
        
        # 确保不超过屏幕可用区域的90%
        max_width = int(screen_info['available_width'] * 0.9)
        max_height = int(screen_info['available_height'] * 0.9)
        
        optimal_width = min(scaled_width, max_width)
        optimal_height = min(scaled_height, max_height)
        
        return optimal_width, optimal_height
    
    def get_optimal_font(self, family: str = "Arial", base_size: int = 9, bold: bool = False) -> QFont:
        """获取最优字体"""
        font_size = self.scale_font_size(base_size)
        font = QFont(family, font_size)
        font.setHintingPreference(QFont.HintingPreference.PreferFullHinting)
        font.setStyleStrategy(QFont.StyleStrategy.PreferAntialias)

        # 高DPI下增强字体渲染
        if self.is_high_dpi():
            font.setStyleStrategy(QFont.StyleStrategy.PreferAntialias | QFont.StyleStrategy.PreferQuality)
            # 4K显示器下字体稍微加粗以提高可读性
            if self.scale_factor >= 2.0:
                font.setWeight(QFont.Weight.Medium)

        if bold:
            font.setBold(True)

        return font
    
    def is_high_dpi(self) -> bool:
        """判断是否为高DPI环境"""
        return self.scale_factor > 1.25
    
    def get_button_size(self, base_width: int, base_height: int) -> QSize:
        """获取按钮尺寸"""
        width, height = self.scale_size(base_width, base_height)
        return QSize(width, height)

    def get_button_font(self, family: str = "Microsoft YaHei UI", base_size: int = 9) -> QFont:
        """获取按钮专用字体，针对高DPI优化"""
        font_size = self.scale_font_size(base_size)
        font = QFont(family, font_size)

        # 高DPI环境下的特殊优化
        if self.is_high_dpi():
            # 4K显示器下字体加粗以提高可读性
            if self.scale_factor >= 2.0:
                font.setWeight(QFont.Weight.DemiBold)
                # 稍微增大字体以确保清晰度
                font.setPointSize(font_size + 1)
            else:
                font.setWeight(QFont.Weight.Medium)

            # 启用最佳渲染质量
            font.setHintingPreference(QFont.HintingPreference.PreferFullHinting)
            font.setStyleStrategy(QFont.StyleStrategy.PreferAntialias | QFont.StyleStrategy.PreferQuality)
        else:
            # 普通DPI下的标准设置
            font.setHintingPreference(QFont.HintingPreference.PreferFullHinting)
            font.setStyleStrategy(QFont.StyleStrategy.PreferAntialias)

        return font
    
    def print_debug_info(self):
        """打印调试信息"""
        screen_info = self.get_screen_info()
        print("=== DPI Adapter Debug Info ===")
        print(f"Scale Factor: {self.scale_factor:.2f}")
        print(f"Font Scale: {self.font_scale:.2f}")
        print(f"Screen Size: {screen_info['width']}x{screen_info['height']}")
        print(f"Available Size: {screen_info['available_width']}x{screen_info['available_height']}")
        print(f"DPI: {screen_info['dpi']}")
        print(f"High DPI: {self.is_high_dpi()}")
        print("==============================")


# 全局DPI适配器实例
dpi_adapter = DPIAdapter()


def get_dpi_adapter() -> DPIAdapter:
    """获取DPI适配器实例"""
    return dpi_adapter


def setup_high_dpi_support():
    """设置高DPI支持"""
    try:
        # 在创建QApplication之前设置
        from PyQt6.QtCore import Qt

        # 启用高DPI缩放
        QApplication.setAttribute(Qt.ApplicationAttribute.AA_EnableHighDpiScaling, True)
        QApplication.setAttribute(Qt.ApplicationAttribute.AA_UseHighDpiPixmaps, True)

        print("High DPI support enabled")

    except Exception as e:
        print(f"Error setting up high DPI support: {e}")


def apply_button_dpi_fix(button, text: str = ""):
    """为按钮应用DPI适配修复"""
    try:
        adapter = get_dpi_adapter()

        # 设置按钮字体
        button_font = adapter.get_button_font()
        button.setFont(button_font)

        # 如果提供了文本，设置文本
        if text:
            button.setText(text)

        # 高DPI下调整按钮尺寸
        if adapter.is_high_dpi():
            # 获取当前尺寸信息
            min_height = button.minimumHeight()
            max_width = button.maximumWidth()

            # 调整最小高度
            if min_height > 0:
                new_min_height = int(min_height * adapter.scale_factor)
                button.setMinimumHeight(new_min_height)

            # 调整最大宽度（如果设置了的话）
            if max_width < 16777215:  # Qt的默认最大值
                new_max_width = int(max_width * adapter.scale_factor)
                button.setMaximumWidth(new_max_width)

        print(f"Applied DPI fix to button: {button.text()}")

    except Exception as e:
        print(f"Error applying button DPI fix: {e}")
