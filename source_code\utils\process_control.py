#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
进程控制工具模块
提供GameClient.exe进程的启动、停止和重启控制功能
"""

import os
import sys
import time
import subprocess
import threading
from pathlib import Path
from typing import List, Optional, Dict, Any

try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False
    print("Warning: psutil not available, process control functionality will be limited")

# 全局进程操作锁，防止并行操作冲突
_process_operation_lock = threading.Lock()


class ProcessController:
    """进程控制器类"""
    
    def __init__(self):
        self.logger = None
    
    def set_logger(self, logger_func):
        """设置日志记录函数"""
        self.logger = logger_func
    
    def log(self, message: str, level: str = "INFO"):
        """记录日志"""
        if self.logger:
            self.logger(message, level)
        else:
            print(f"[{level}] {message}")
    
    def find_processes_by_name(self, process_name: str) -> List[Dict[str, Any]]:
        """
        根据进程名查找进程（增强版，包含工作目录信息）

        Args:
            process_name: 进程名称（如 "GameClient.exe"）

        Returns:
            进程信息列表
        """
        if not PSUTIL_AVAILABLE:
            self.log("psutil不可用，无法查找进程", "WARNING")
            return []

        processes = []
        try:
            for proc in psutil.process_iter(['pid', 'name', 'exe', 'cwd', 'create_time']):
                try:
                    if proc.info['name'] and proc.info['name'].lower() == process_name.lower():
                        # 获取工作目录，如果失败则设为None
                        cwd = None
                        try:
                            cwd = proc.info['cwd']
                        except (psutil.AccessDenied, psutil.NoSuchProcess):
                            pass

                        processes.append({
                            'pid': proc.info['pid'],
                            'name': proc.info['name'],
                            'exe': proc.info['exe'],
                            'cwd': cwd,
                            'create_time': proc.info['create_time'],
                            'process': proc
                        })
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                    continue
        except Exception as e:
            self.log(f"查找进程时出错: {e}", "ERROR")

        return processes

    def find_process_by_working_directory(self, process_name: str, target_dir: str) -> List[Dict[str, Any]]:
        """
        根据进程名和工作目录查找进程

        Args:
            process_name: 进程名称（如 "GameClient.exe"）
            target_dir: 目标工作目录

        Returns:
            匹配的进程信息列表
        """
        if not PSUTIL_AVAILABLE:
            self.log("psutil不可用，无法查找进程", "WARNING")
            return []

        target_dir_normalized = os.path.normpath(target_dir.lower())
        matching_processes = []

        try:
            all_processes = self.find_processes_by_name(process_name)

            for proc_info in all_processes:
                proc_cwd = proc_info.get('cwd')
                if proc_cwd:
                    proc_cwd_normalized = os.path.normpath(proc_cwd.lower())
                    if proc_cwd_normalized == target_dir_normalized:
                        matching_processes.append(proc_info)
                        self.log(f"找到匹配的进程: PID={proc_info['pid']}, 工作目录={proc_cwd}")

            self.log(f"在目录 {target_dir} 中找到 {len(matching_processes)} 个 {process_name} 进程")

        except Exception as e:
            self.log(f"根据工作目录查找进程时出错: {e}", "ERROR")

        return matching_processes

    def start_process(self, exe_path: str, working_dir: Optional[str] = None) -> Optional[subprocess.Popen]:
        """
        启动进程
        
        Args:
            exe_path: 可执行文件路径
            working_dir: 工作目录
            
        Returns:
            进程对象或None
        """
        try:
            if not os.path.exists(exe_path):
                self.log(f"可执行文件不存在: {exe_path}", "ERROR")
                return None
            
            if working_dir is None:
                working_dir = os.path.dirname(exe_path)
            
            self.log(f"正在启动进程: {exe_path}", "INFO")
            process = subprocess.Popen([exe_path], cwd=working_dir)
            self.log(f"进程启动成功，PID: {process.pid}", "SUCCESS")
            return process
            
        except Exception as e:
            self.log(f"启动进程失败: {e}", "ERROR")
            return None
    
    def terminate_process(self, process_info: Dict[str, Any], timeout: int = 3) -> bool:
        """
        终止进程
        
        Args:
            process_info: 进程信息字典
            timeout: 等待超时时间（秒）
            
        Returns:
            是否成功终止
        """
        if not PSUTIL_AVAILABLE:
            self.log("psutil不可用，无法终止进程", "WARNING")
            return False
        
        try:
            proc = process_info.get('process')
            pid = process_info.get('pid')
            
            if not proc:
                self.log(f"无效的进程对象，PID: {pid}", "ERROR")
                return False
            
            self.log(f"正在终止进程 PID: {pid}", "INFO")
            proc.terminate()
            
            try:
                proc.wait(timeout=timeout)
                self.log(f"进程 PID: {pid} 已成功终止", "SUCCESS")
                return True
            except psutil.TimeoutExpired:
                self.log(f"进程 PID: {pid} 未在{timeout}秒内终止，强制结束", "WARNING")
                proc.kill()
                proc.wait(timeout=1)
                self.log(f"进程 PID: {pid} 已强制结束", "SUCCESS")
                return True
                
        except psutil.NoSuchProcess:
            self.log(f"进程 PID: {pid} 已不存在", "INFO")
            return True
        except (psutil.AccessDenied, OSError) as e:
            self.log(f"终止进程 PID: {pid} 时权限不足: {e}", "ERROR")
            return False
        except Exception as e:
            self.log(f"终止进程 PID: {pid} 时出错: {e}", "ERROR")
            return False
    
    def terminate_processes_by_path(self, exe_path: str, timeout: int = 3) -> int:
        """
        根据可执行文件路径终止所有匹配的进程
        
        Args:
            exe_path: 可执行文件路径
            timeout: 等待超时时间（秒）
            
        Returns:
            成功终止的进程数量
        """
        if not PSUTIL_AVAILABLE:
            self.log("psutil不可用，无法终止进程", "WARNING")
            return 0
        
        exe_path_lower = exe_path.lower()
        terminated_count = 0
        
        try:
            processes = self.find_processes_by_name(os.path.basename(exe_path))
            
            for proc_info in processes:
                try:
                    proc_exe = proc_info.get('exe', '')
                    if proc_exe and proc_exe.lower() == exe_path_lower:
                        if self.terminate_process(proc_info, timeout):
                            terminated_count += 1
                except Exception as e:
                    self.log(f"处理进程时出错: {e}", "WARNING")
                    continue
            
            self.log(f"共终止了 {terminated_count} 个匹配的进程", "INFO")
            
        except Exception as e:
            self.log(f"批量终止进程时出错: {e}", "ERROR")
        
        return terminated_count
    
    def is_process_running(self, exe_path: str) -> bool:
        """
        检查指定路径的进程是否正在运行
        
        Args:
            exe_path: 可执行文件路径
            
        Returns:
            是否正在运行
        """
        if not PSUTIL_AVAILABLE:
            return False
        
        try:
            processes = self.find_processes_by_name(os.path.basename(exe_path))
            exe_path_lower = exe_path.lower()
            
            for proc_info in processes:
                proc_exe = proc_info.get('exe', '')
                if proc_exe and proc_exe.lower() == exe_path_lower:
                    return True
            
            return False
            
        except Exception as e:
            self.log(f"检查进程状态时出错: {e}", "ERROR")
            return False


class GameClientController:
    """GameClient.exe专用控制器"""
    
    def __init__(self, logger_func=None):
        self.process_controller = ProcessController()
        if logger_func:
            self.process_controller.set_logger(logger_func)
    
    def log(self, message: str, level: str = "INFO"):
        """记录日志"""
        self.process_controller.log(f"[GameClient控制] {message}", level)
    
    def get_gameclient_path(self, server_ini_path: str) -> Optional[str]:
        """
        根据server.ini路径计算GameClient.exe路径
        
        Args:
            server_ini_path: server.ini文件路径
            
        Returns:
            GameClient.exe路径或None
        """
        try:
            server_ini_path = Path(server_ini_path)
            game_client_dir = server_ini_path.parent.parent
            game_client_exe_path = game_client_dir / "GameClient.exe"
            
            if game_client_exe_path.is_file():
                return str(game_client_exe_path.resolve())
            else:
                self.log(f"GameClient.exe不存在于计算的路径: {game_client_exe_path}", "ERROR")
                return None
                
        except Exception as e:
            self.log(f"计算GameClient.exe路径时出错: {e}", "ERROR")
            return None
    
    def control_gameclient(self, server_ini_path: str, action: str) -> bool:
        """
        控制GameClient.exe进程（智能化版本）

        Args:
            server_ini_path: server.ini文件路径
            action: 操作类型（"打开"、"关闭"、"重启"）

        Returns:
            操作是否成功
        """
        start_time = time.time()
        self.log(f"开始执行操作: 文件={os.path.basename(server_ini_path)}, 操作={action}")

        # 使用全局锁防止并行操作冲突
        with _process_operation_lock:
            try:
                # 获取GameClient.exe路径和工作目录
                game_client_exe_path = self.get_gameclient_path(server_ini_path)
                if not game_client_exe_path:
                    return False

                # 计算工作目录（GameClient.exe所在目录）
                working_directory = os.path.dirname(game_client_exe_path)
                self.log(f"GameClient路径: {game_client_exe_path}")
                self.log(f"工作目录: {working_directory}")

                # 执行操作
                success = False

                if action == "打开":
                    # 检查是否已有对应的进程在运行
                    existing_processes = self.process_controller.find_process_by_working_directory("GameClient.exe", working_directory)

                    if existing_processes:
                        self.log(f"GameClient已在运行（PID: {[p['pid'] for p in existing_processes]}），不需要启动")
                        success = True
                    else:
                        process = self.process_controller.start_process(game_client_exe_path, working_directory)
                        success = process is not None

                elif action == "关闭" or action == "重启":
                    # 智能化进程查找：优先使用工作目录匹配
                    target_processes = self.process_controller.find_process_by_working_directory("GameClient.exe", working_directory)

                    if not target_processes:
                        self.log(f"在工作目录 {working_directory} 中没有找到GameClient进程")
                        # 尝试精确路径匹配作为备选
                        all_processes = self.process_controller.find_processes_by_name("GameClient.exe")
                        for proc_info in all_processes:
                            proc_exe = proc_info.get('exe', '')
                            if proc_exe and proc_exe.lower() == game_client_exe_path.lower():
                                target_processes.append(proc_info)
                                self.log(f"通过路径匹配找到进程: PID={proc_info['pid']}")

                    if not target_processes:
                        self.log("没有找到需要终止的GameClient进程")
                        success = True
                    else:
                        # 只终止匹配的进程
                        terminated_count = 0
                        for proc_info in target_processes:
                            try:
                                if self.process_controller.terminate_process(proc_info):
                                    terminated_count += 1
                                    self.log(f"已终止进程: PID={proc_info['pid']}")
                            except Exception as e:
                                self.log(f"终止进程 PID={proc_info['pid']} 时出错: {e}", "WARNING")

                        success = terminated_count > 0
                        self.log(f"共终止了 {terminated_count} 个匹配的GameClient进程")

                    # 如果是重启，还需要启动
                    if action == "重启" and success:
                        self.log("等待2秒以确保进程完全退出...")
                        time.sleep(2)

                        process = self.process_controller.start_process(game_client_exe_path, working_directory)
                        success = process is not None

                else:
                    self.log(f"未知的操作: {action}", "WARNING")
                    success = False

                end_time = time.time()
                self.log(f"操作 {action} {'成功' if success else '失败'}，耗时 {end_time - start_time:.2f}秒")

                return success

            except Exception as e:
                self.log(f"执行过程中发生错误: {e}", "ERROR")
                import traceback
                self.log(f"错误堆栈: {traceback.format_exc()}", "ERROR")
                return False

    # 注意：已移除 _terminate_all_gameclient_processes 方法
    # 新的智能化进程管理不再需要无差别终止所有进程

    def control_gameclient_threaded(self, server_ini_path: str, action: str):
        """
        在单独线程中控制GameClient.exe
        
        Args:
            server_ini_path: server.ini文件路径
            action: 操作类型
        """
        try:
            thread = threading.Thread(
                target=self.control_gameclient,
                args=(server_ini_path, action),
                daemon=True
            )
            thread.name = f"GC_Control_{os.path.basename(server_ini_path)}_{action}_{int(time.time())}"
            thread.start()
            self.log(f"已启动控制线程 (线程名: {thread.name})")
            
        except Exception as e:
            self.log(f"创建控制线程时出错: {e}", "ERROR")
            import traceback
            self.log(f"错误堆栈: {traceback.format_exc()}", "ERROR")

    def restart_gameclient(self, server_ini_path: str) -> bool:
        """
        重启GameClient.exe进程（便捷方法）

        Args:
            server_ini_path: server.ini文件路径

        Returns:
            操作是否成功
        """
        return self.control_gameclient(server_ini_path, "重启")
