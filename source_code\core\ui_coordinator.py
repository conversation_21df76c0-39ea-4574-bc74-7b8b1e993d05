"""
UI协调器模块
负责UI状态管理、更新协调、界面同步等功能
从 app_pyqt6.py 中提取的UI协调相关功能
"""

import os
import threading
import time
from typing import Dict, Any, Optional, List
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QStatusBar, QProgressBar,
    QTextEdit, QFrame, QSplitter, QMessageBox
)
from PyQt6.QtCore import Qt, QTime, QTimer
from PyQt6.QtGui import QIcon, QFont, QPixmap

from gui.ui_factory import UIFactory
from gui.ui_constants import Sizes, Spacing, Colors, Messages
from utils.pyqt6_threading import SafeUIUpdater
from utils.performance_optimizer import cached_icon, monitor_performance


class UICoordinator:
    """UI协调器类 - 负责UI状态管理和协调"""
    
    def __init__(self, parent_app):
        """
        初始化UI协调器
        
        Args:
            parent_app: 主应用程序实例
        """
        self.app = parent_app
        
        # UI状态管理
        self.is_checking = False
        self.is_cancelled = False
        
        # UI组件引用
        self.central_widget = None
        self.main_layout = None
        self.log_text = None
        self.status_bar = None
        self.progress_bar = None
        self.check_button = None
        self.cancel_button = None
        
        # DPI和缩放相关
        self.scale_factor = 1.0
        self.initial_width = Sizes.WINDOW_DEFAULT_WIDTH
        self.initial_height = Sizes.WINDOW_DEFAULT_HEIGHT
        
        # 调试模式
        self.debug_mode = os.getenv('DEBUG', '').lower() in ('true', '1', 'yes')
    
    def setup_scaling(self):
        """设置DPI缩放"""
        try:
            # 使用新的DPI适配器
            from utils.dpi_adapter import get_dpi_adapter
            self.app.dpi_adapter = get_dpi_adapter()
            self.scale_factor = self.app.dpi_adapter.scale_factor

            print(f"Using DPI adapter scale factor: {self.scale_factor:.2f}")

            # 获取最优窗口尺寸
            self.initial_width, self.initial_height = self.app.dpi_adapter.get_optimal_window_size(
                Sizes.WINDOW_DEFAULT_WIDTH,
                Sizes.WINDOW_DEFAULT_HEIGHT
            )

            print(f"Optimal window size: {self.initial_width}x{self.initial_height}")

        except Exception as e:
            print(f"Error in scaling setup: {e}")
            self.scale_factor = 1.0
            self.initial_width = Sizes.WINDOW_DEFAULT_WIDTH
            self.initial_height = Sizes.WINDOW_DEFAULT_HEIGHT

    def setup_window(self):
        """设置窗口属性"""
        # 使用DPI适配器计算最小尺寸
        min_width, min_height = self.app.dpi_adapter.scale_size(
            Sizes.WINDOW_MIN_WIDTH,
            Sizes.WINDOW_MIN_HEIGHT
        )

        # Set window size and constraints
        self.app.setMinimumSize(min_width, min_height)
        self.app.resize(self.initial_width, self.initial_height)

        # Set window properties
        self.app.setWindowFlags(Qt.WindowType.Window)

        # 设置窗口居中显示
        self.center_window()

        # Apply styles
        from gui.styles_pyqt6 import StyleManager
        from PyQt6.QtWidgets import QApplication
        StyleManager.apply_style(QApplication.instance())

    def center_window(self):
        """将窗口居中显示"""
        try:
            from PyQt6.QtWidgets import QApplication
            if QApplication.instance():
                screen = QApplication.primaryScreen()
                if screen:
                    screen_geometry = screen.availableGeometry()
                    window_geometry = self.app.frameGeometry()
                    center_point = screen_geometry.center()
                    window_geometry.moveCenter(center_point)
                    self.app.move(window_geometry.topLeft())
        except Exception as e:
            print(f"Error centering window: {e}")

    def create_main_ui(self):
        """创建并显示主UI界面"""
        # 应用完整样式
        from gui.styles_pyqt6 import StyleManager
        from PyQt6.QtWidgets import QApplication
        StyleManager.apply_style(QApplication.instance())
        
        self.central_widget = QWidget()
        self.app.setCentralWidget(self.central_widget)
        self.main_layout = QVBoxLayout(self.central_widget)
        
        self.create_widgets()
        self.app.log("主界面构建完成", "INFO")

    def create_widgets(self):
        """创建主界面组件"""
        # Create main sections
        self.create_top_section()
        self.app.schedule_manager.create_schedule_section(self.main_layout)
        self.app.upload_manager.create_upload_section(self.main_layout)
        self.create_bottom_section()

    def create_top_section(self):
        """创建顶部区域（文件列表和日志）"""
        # Create horizontal splitter for file list and log
        top_splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # File section
        self.app.file_manager.create_file_section(top_splitter)
        
        # Log section
        self.create_log_section(top_splitter)
        
        # Set splitter proportions (60% file, 40% log)
        top_splitter.setSizes([int(self.initial_width * 0.6), int(self.initial_width * 0.4)])
        
        self.main_layout.addWidget(top_splitter)

    def create_log_section(self, parent):
        """创建日志区域"""
        log_frame = QFrame()
        log_layout = QVBoxLayout(log_frame)
        
        # Log title
        log_title = self.app.QLabel("操作日志")
        log_title.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        log_layout.addWidget(log_title)
        
        # Log text area
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setMinimumHeight(400)
        self.log_text.setFont(QFont("Consolas", 9))
        log_layout.addWidget(self.log_text)
        
        parent.addWidget(log_frame)

    def create_bottom_section(self):
        """创建底部区域（状态栏和操作按钮）"""
        # Status bar
        self.status_bar = QStatusBar()
        self.status_bar.showMessage("就绪")
        
        # Progress bar in status bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setMaximumWidth(200)
        self.status_bar.addPermanentWidget(self.progress_bar)
        
        self.app.setStatusBar(self.status_bar)
        
        # Action buttons
        self.create_action_buttons()

    def create_action_buttons(self):
        """创建操作按钮"""
        buttons_layout = UIFactory.create_horizontal_layout(spacing=Spacing.LAYOUT_SPACING)

        # Main action button
        self.check_button = UIFactory.create_button(
            text="检查并更新",
            style_type="primary",
            size_type="large",
            min_width=Sizes.PRIMARY_BUTTON_MIN_WIDTH,
            min_height=Sizes.ACTION_BUTTON_HEIGHT,
            callback=self.app.start_check,
            enabled=False,
            tooltip="开始检查选中的文件并进行更新"
        )
        buttons_layout.addWidget(self.check_button)

        # Cancel button
        self.cancel_button = UIFactory.create_button(
            text="取消操作",
            style_type="secondary",
            size_type="normal",
            min_width=Sizes.ACTION_BUTTON_MIN_WIDTH,
            min_height=Sizes.ACTION_BUTTON_HEIGHT,
            callback=self.app.cancel_operation,
            enabled=False,
            tooltip="取消当前正在进行的操作"
        )
        buttons_layout.addWidget(self.cancel_button)

        # Settings button
        settings_button = UIFactory.create_button(
            text="系统设置",
            style_type="default",
            size_type="normal",
            min_width=Sizes.ACTION_BUTTON_MIN_WIDTH,
            min_height=Sizes.ACTION_BUTTON_HEIGHT,
            callback=self.app.open_system_settings,
            tooltip="打开系统设置对话框"
        )
        buttons_layout.addWidget(settings_button)

        buttons_layout.addStretch()
        self.main_layout.addLayout(buttons_layout)

    def log(self, message: str, level: str = "INFO"):
        """添加日志消息（线程安全版本）"""
        if self.log_text:
            # 添加级别标识
            level_prefix = {
                "INFO": "ℹ️",
                "SUCCESS": "✅",
                "WARNING": "⚠️",
                "ERROR": "❌",
                "DEBUG": "🔍"
            }.get(level, "ℹ️")

            formatted_message = f"[{QTime.currentTime().toString('hh:mm:ss')}] {level_prefix} {message}"
            
            # 检查是否在主线程
            if threading.current_thread() == threading.main_thread():
                # 在主线程中，直接更新
                self.log_text.append(formatted_message)
                # Auto-scroll to bottom
                scrollbar = self.log_text.verticalScrollBar()
                scrollbar.setValue(scrollbar.maximum())
            else:
                # 在工作线程中，使用SafeUIUpdater
                SafeUIUpdater.call_in_main_thread(self._append_log_message, formatted_message)

    def _append_log_message(self, formatted_message: str):
        """内部方法：在主线程中添加日志消息"""
        if self.log_text:
            self.log_text.append(formatted_message)
            # Auto-scroll to bottom
            scrollbar = self.log_text.verticalScrollBar()
            scrollbar.setValue(scrollbar.maximum())

    def update_check_button_state(self):
        """更新检查按钮状态"""
        if not self.check_button:
            return
            
        has_selected_files = any(
            item['checkbox'].isChecked()
            for item in self.app.file_manager.file_checkboxes
        )
        self.check_button.setEnabled(has_selected_files and not self.is_checking)

    def update_buttons_state(self, searching: bool = False, has_files: bool = False):
        """更新按钮状态"""
        try:
            if searching:
                # 搜索或处理中
                if self.check_button:
                    self.check_button.setEnabled(False)
                if self.cancel_button:
                    self.cancel_button.setEnabled(True)
            else:
                # 空闲状态
                if self.check_button:
                    self.check_button.setEnabled(has_files)
                if self.cancel_button:
                    self.cancel_button.setEnabled(False)

        except Exception as e:
            self.app.log(f"更新按钮状态时出错: {e}", "ERROR")

    @cached_icon
    def _load_icon_from_path(self, icon_path: str) -> Optional[QIcon]:
        """从路径加载图标（带缓存）"""
        try:
            if os.path.exists(icon_path):
                icon = QIcon(icon_path)
                if not icon.isNull():
                    return icon
        except Exception as e:
            if self.debug_mode:
                self.app.log(f"加载图标失败 {icon_path}: {e}", "DEBUG")
        return None

    @monitor_performance("setup_window_icon")
    def setup_window_icon(self):
        """设置窗口图标"""
        try:
            # 使用新的资源路径函数
            from utils.system import get_icon_path
            from core.constants import APP_ICON_FILENAME
            
            icon_path = get_icon_path(APP_ICON_FILENAME)
            if icon_path:
                try:
                    icon = QIcon(icon_path)
                    if not icon.isNull():
                        self.app.setWindowIcon(icon)
                        # 同时设置应用程序图标
                        from PyQt6.QtWidgets import QApplication
                        app = QApplication.instance()
                        if app and hasattr(app, 'setWindowIcon'):
                            app.setWindowIcon(icon)

                        self.app.log(f"✅ 应用程序图标已加载: {icon_path}", "SUCCESS")
                        # 只在调试时输出详细信息
                        if os.getenv('DEBUG'):
                            print(f"✅ 窗口图标加载成功: {icon_path}")
                        return
                except Exception:
                    # 精简异常处理
                    pass
            
            # 如果新方法失败，回退到原有方法
            print("回退到传统窗口图标加载方法...")
            from utils.system import resource_path
            
            # 获取当前脚本目录
            current_dir = os.path.dirname(os.path.abspath(__file__))
            project_root = os.path.dirname(os.path.dirname(current_dir))

            # 尝试多个可能的图标路径（与托盘图标保持一致）
            possible_paths = [
                # 使用resource_path处理的路径
                resource_path(APP_ICON_FILENAME),
                resource_path("app_icon.ico"),
                resource_path("app_icon.png"),
                # 项目根目录（最高优先级）
                os.path.join(project_root, APP_ICON_FILENAME),
                # 当前工作目录
                os.path.join(os.getcwd(), APP_ICON_FILENAME),
                # 项目根目录下的assets文件夹
                os.path.join(project_root, "source_code", "assets", APP_ICON_FILENAME),
                os.path.join(project_root, "source_code", "assets", "app_icon.ico"),
                os.path.join(project_root, "source_code", "assets", "app_icon.png"),
                # 当前目录相对路径
                os.path.join(current_dir, "..", "assets", APP_ICON_FILENAME),
                os.path.join(current_dir, "..", "assets", "app_icon.ico"),
                os.path.join(current_dir, "..", "assets", "app_icon.png"),
                # 直接文件名（当前目录）
                APP_ICON_FILENAME,
                "app_icon.ico",
                "app_icon.png"
            ]

            icon_loaded = False
            for icon_path in possible_paths:
                icon_path = os.path.normpath(icon_path)
                print(f"尝试加载窗口图标: {icon_path}")

                # 使用缓存的图标加载方法
                icon = self._load_icon_from_path(icon_path)
                if icon:
                    self.app.setWindowIcon(icon)
                    # 同时设置应用程序图标
                    from PyQt6.QtWidgets import QApplication
                    app = QApplication.instance()
                    if app and hasattr(app, 'setWindowIcon'):
                        app.setWindowIcon(icon)

                    # 只在成功时记录日志
                    self.app.log(f"✅ 应用程序图标已加载: {icon_path}", "SUCCESS")
                    print(f"✅ 窗口图标加载成功: {icon_path}")
                    icon_loaded = True
                    break
                else:
                    print(f"❌ 窗口图标加载失败: {icon_path}")

            if not icon_loaded:
                self.app.log("⚠️ 未找到有效的图标文件，使用默认图标", "WARNING")
                print("⚠️ 未找到有效的窗口图标文件")

        except Exception as e:
            self.app.log(f"设置窗口图标失败: {e}", "ERROR")
            print(f"设置窗口图标失败: {e}")

    def show_loading_screen(self, text):
        """显示一个简单的加载界面"""
        self.loading_widget = QWidget()
        layout = QVBoxLayout(self.loading_widget)
        layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        self.loading_label = self.app.QLabel(text)
        self.loading_label.setFont(QFont("Arial", 14))
        
        self.loading_progress = QProgressBar()
        self.loading_progress.setRange(0, 100)
        self.loading_progress.setTextVisible(False)
        
        layout.addWidget(self.loading_label)
        layout.addWidget(self.loading_progress)
        
        self.app.setCentralWidget(self.loading_widget)

    def update_loading_status(self, progress, status):
        """更新加载界面的状态和进度条"""
        if hasattr(self, 'loading_label') and self.loading_label:
            self.loading_label.setText(status)
        if hasattr(self, 'loading_progress') and self.loading_progress:
            self.loading_progress.setValue(progress)

    def init_ui_variables(self):
        """初始化UI状态变量"""
        # Operation state
        self.is_checking = False
        self.is_cancelled = False

        # System integration
        self.start_on_boot_enabled = False
        self.remember_close_choice = False
        self.run_in_tray = False

        # Debug mode (can be enabled via environment variable)
        self.debug_mode = os.getenv('DEBUG', '').lower() in ('true', '1', 'yes')

    def setup_basic_window(self):
        """设置最基本的窗口属性，以便快速显示"""
        self.app.setMinimumSize(Sizes.WINDOW_MIN_WIDTH, Sizes.WINDOW_MIN_HEIGHT)
        self.app.resize(self.initial_width, self.initial_height)
        self.app.setWindowFlags(Qt.WindowType.Window)
        self.center_window()

    def log_threadsafe(self, message: str, level: str = "INFO"):
        """线程安全的日志记录方法（暂时简化）"""
        # 暂时简化处理，直接调用log方法
        self.log(message, level)

    def set_log_level_filter(self, levels: list):
        """设置日志级别过滤器
        
        Args:
            levels: 要显示的日志级别列表，如 ["INFO", "SUCCESS", "WARNING", "ERROR"]
        """
        self.log_level_filter = levels
        self.app.log(f"日志级别过滤已更新: {', '.join(levels)}", "INFO")
    
    def toggle_log_batch_update(self, enabled: bool):
        """切换日志批量更新模式
        
        Args:
            enabled: 是否启用批量更新
        """
        self.log_batch_update = enabled
        if not enabled:
            # 禁用时立即刷新缓冲区
            self._flush_log_batch()
        self.app.log(f"日志批量更新已{'启用' if enabled else '禁用'}", "INFO")

    def _flush_log_batch(self):
        """批量刷新日志缓冲区（暂时禁用）"""
        # 暂时禁用批量更新功能以解决性能问题
        pass