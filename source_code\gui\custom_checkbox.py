"""
自定义复选框组件
提供带有白色勾选标记的蓝色复选框
"""

from PyQt6.QtWidgets import QCheckBox, QWidget
from PyQt6.QtCore import Qt, QRect
from PyQt6.QtGui import QPainter, QPen, QBrush, QColor, QFont


class CustomCheckBox(QCheckBox):
    """自定义复选框，支持蓝底白勾样式"""
    
    def __init__(self, text: str = "", parent: QWidget = None):
        super().__init__(text, parent)
        self.setStyleSheet("""
            QCheckBox {
                spacing: 8px;
                font-weight: 500;
            }
            
            QCheckBox::indicator {
                width: 20px;
                height: 20px;
                border: 2px solid #DDDDDD;
                border-radius: 6px;
                background-color: white;
            }
            
            QCheckBox::indicator:hover {
                border-color: #007AFF;
                background-color: #F8FAFF;
            }
            
            QCheckBox::indicator:disabled {
                background-color: #F5F5F5;
                border-color: #E0E0E0;
            }
        """)
    
    def paintEvent(self, event):
        """重写绘制事件以添加自定义勾选标记"""
        # 先调用父类的绘制方法
        super().paintEvent(event)
        
        # 如果复选框被选中，绘制勾选标记
        if self.isChecked():
            painter = QPainter(self)
            painter.setRenderHint(QPainter.RenderHint.Antialiasing)
            
            # 获取指示器的位置和大小
            indicator_rect = self.get_indicator_rect()
            
            # 设置背景色
            if self.isEnabled():
                if self.underMouse():
                    bg_color = QColor("#0056CC")  # 悬停时的深蓝色
                else:
                    bg_color = QColor("#007AFF")  # 正常的蓝色
            else:
                bg_color = QColor("#CCCCCC")  # 禁用时的灰色
            
            # 绘制蓝色背景
            painter.setBrush(QBrush(bg_color))
            painter.setPen(QPen(bg_color, 2))
            painter.drawRoundedRect(indicator_rect, 6, 6)
            
            # 绘制白色勾选标记
            painter.setPen(QPen(QColor("white"), 2, Qt.PenStyle.SolidLine, 
                              Qt.PenCapStyle.RoundCap, Qt.PenJoinStyle.RoundJoin))
            
            # 计算勾选标记的位置
            center_x = indicator_rect.center().x()
            center_y = indicator_rect.center().y()
            
            # 绘制勾选标记（✓）
            # 第一条线：从左下到中间
            painter.drawLine(
                center_x - 4, center_y,
                center_x - 1, center_y + 3
            )
            # 第二条线：从中间到右上
            painter.drawLine(
                center_x - 1, center_y + 3,
                center_x + 4, center_y - 2
            )
    
    def get_indicator_rect(self) -> QRect:
        """获取指示器的矩形区域"""
        # 根据Qt的默认布局计算指示器位置
        indicator_size = 20
        margin = 2
        
        # 指示器通常在左侧
        x = margin
        y = (self.height() - indicator_size) // 2
        
        return QRect(x, y, indicator_size, indicator_size)
    
    def enterEvent(self, event):
        """鼠标进入事件"""
        super().enterEvent(event)
        self.update()  # 触发重绘以显示悬停效果
    
    def leaveEvent(self, event):
        """鼠标离开事件"""
        super().leaveEvent(event)
        self.update()  # 触发重绘以移除悬停效果


def create_custom_checkbox(
    text: str,
    checked: bool = False,
    enabled: bool = True,
    callback=None,
    tooltip: str = None
) -> CustomCheckBox:
    """
    创建自定义复选框的便捷函数
    
    Args:
        text: 复选框文本
        checked: 是否选中
        enabled: 是否启用
        callback: 状态变化回调函数
        tooltip: 工具提示
    
    Returns:
        CustomCheckBox实例
    """
    checkbox = CustomCheckBox(text)
    
    # 设置状态
    checkbox.setChecked(checked)
    checkbox.setEnabled(enabled)
    
    # 设置回调
    if callback:
        checkbox.toggled.connect(callback)
    
    # 设置工具提示
    if tooltip:
        checkbox.setToolTip(tooltip)
    
    return checkbox
