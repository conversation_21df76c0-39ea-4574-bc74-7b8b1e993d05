# -*- mode: python ; coding: utf-8 -*-

# 游戏服务器检查工具 - 优化版PyInstaller配置
# 针对体积优化的高级打包配置

import sys
import os
from pathlib import Path

# 项目根目录
project_root = Path(os.getcwd())
source_dir = project_root / "source_code"

# 数据文件配置 - 只包含必要的资源
datas = [
    # 图标资源
    (str(source_dir / "assets" / "icon.ico"), "assets"),
]

# 配置文件模板（如果存在）
if os.path.exists("app_config.json"):
    datas.append(("app_config.json", "."))

# 过滤掉None值（现在不需要了，但保留以防将来使用）
datas = [d for d in datas if d is not None]

# 核心隐藏导入 - 基于实际使用分析
hiddenimports = [
    # === 核心系统依赖 ===
    'psutil',
    'ntplib',
    'chardet',
    'requests',
    'schedule',  # 任务调度模块
    'keyword',   # Python关键字模块
    
    # === Windows特定依赖 ===
    'win32api',
    'win32con', 
    'win32gui',
    'win32process',
    'pywintypes',
    
    # === PyQt6核心模块 ===
    'PyQt6.QtCore',
    'PyQt6.QtGui',
    'PyQt6.QtWidgets',
    'PyQt6.QtNetwork',
    
    # === 项目核心模块 ===
    # 核心应用
    'core.app_pyqt6',
    'core.config',
    'core.constants',
    'core.event_handler',
    'core.file_manager',
    'core.schedule_manager',
    'core.ui_coordinator',
    'core.upload_manager',
    
    # GUI组件
    'gui.components_pyqt6',
    'gui.dialogs_pyqt6',
    'gui.styles_pyqt6',
    'gui.time_input_widget',
    'gui.ui_constants',
    'gui.ui_factory',
    'gui.custom_checkbox',
    
    # 服务模块
    'services.unified_network_service',
    'services.optimized_file_service',
    'services.qt_scheduler_service',
    'services.qt_tray_service',
    'services.configuration_service',
    'services.file_processing_service',  # 新增：文件处理服务（步骤2修复相关）
    
    # 工具模块
    'utils.pyqt6_threading',
    'utils.performance_optimizer',
    'utils.system',
    'utils.dpi_adapter',
    'utils.high_dpi_fix',
    'utils.sequential_executor',
    'utils.process_control',
    'utils.startup_optimization',
    'utils.ui_optimizer',
    'utils.lazy_import_manager',
    'utils.crash_fix',
    'utils.windows_startup',
    'utils.smart_logger',
]

# 排除模块 - 减少体积的关键配置
excludes = [
    # === 开发和调试工具 ===
    'pytest',
    'unittest',
    'doctest',
    'pdb',
    'debugpy',
    'IPython',
    'ipython',
    'jupyter',
    'notebook',
    
    # === 不需要的标准库模块 ===
    'tkinter',
    'tkinter.ttk',
    'turtle',
    'curses',
    'readline',
    'rlcompleter',
    'code',
    'codeop',
    'pydoc',
    'inspect',
    'profile',
    'cProfile',
    'pstats',
    'timeit',
    'trace',
    'dis',
    'pickletools',
    'py_compile',
    'compileall',
    # 'keyword',  # 注释掉，应用程序需要这个模块
    'tokenize',
    'ast',
    'parser',
    'symbol',
    'token',
    
    # === 不需要的网络和协议模块 ===
    'ftplib',
    'poplib',
    'imaplib',
    'nntplib',
    'smtplib',
    'telnetlib',
    'xmlrpc',
    'http.server',
    'wsgiref',
    'CGIHTTPServer',
    'SimpleHTTPServer',
    'BaseHTTPServer',
    
    # === 不需要的数据处理模块 ===
    'sqlite3',
    'dbm',
    # 'csv',  # 可能被某些模块需要，暂时不排除
    'xml.dom',
    'xml.sax',
    'xml.parsers',
    'xml.etree',
    'html.parser',
    'html.entities',
    'sgmllib',
    'htmllib',
    'formatter',
    'mailbox',
    'mimetypes',
    'base64',
    'binhex',
    'binascii',
    'uu',
    'quopri',
    
    # === 不需要的多媒体和图像处理 ===
    'audioop',
    'imageop',
    'rgbimg',
    'wave',
    'chunk',
    'sunau',
    'aifc',
    'sndhdr',
    'ossaudiodev',
    'linuxaudiodev',
    'winsound',
    
    # === 不需要的GUI框架 ===
    'tkinter',
    'Tkinter',
    'turtle',
    'turtledemo',
    
    # === 不需要的测试框架 ===
    'test',
    'tests',
    'testing',
    'nose',
    'mock',
    
    # === 不需要的文档和帮助系统 ===
    'pydoc',
    'pydoc_data',
    'help',
    'antigravity',
    'this',
    
    # === 排除OpenSSL冲突库 ===
    'ssl',
    '_ssl',
    'OpenSSL',
    'pyOpenSSL',
    'cryptography',
    
    # === 不需要的编译工具 ===
    'distutils',
    'setuptools',
    'pip',
    'wheel',
    'pkg_resources',
    'easy_install',
    
    # === 不需要的科学计算库 ===
    'numpy',
    'scipy',
    'matplotlib',
    'pandas',
    'sympy',
]

# 二进制文件排除 - 进一步减小体积
binaries_excludes = [
    # OpenSSL相关
    'libssl*',
    'libcrypto*',
    'openssl*',
    'ssleay*',
    'libeay*',
    
    # 不需要的Qt模块
    'Qt6WebEngine*',
    'Qt6WebEngineCore*',
    'Qt6WebEngineWidgets*',
    'Qt6WebChannel*',
    'Qt6Multimedia*',
    'Qt6MultimediaWidgets*',
    'Qt6Quick*',
    'Qt6Qml*',
    'Qt6QmlModels*',
    'Qt6QmlWorkerScript*',
    'Qt6Sql*',
    'Qt6Test*',
    'Qt6Designer*',
    'Qt6Help*',
    'Qt6PrintSupport*',
    'Qt6Svg*',
    'Qt6SvgWidgets*',
    'Qt6OpenGL*',
    'Qt6OpenGLWidgets*',
    'Qt6Concurrent*',
    'Qt6DBus*',
    'Qt6SerialPort*',
    'Qt6Bluetooth*',
    'Qt6Positioning*',
    'Qt6Sensors*',
    'Qt6RemoteObjects*',
    'Qt6Charts*',
    'Qt6DataVisualization*',
    'Qt6NetworkAuth*',
    'Qt6Pdf*',
    'Qt6PdfWidgets*',
    
    # 不需要的PyQt6模块
    'PyQt6.QtWebEngine*',
    'PyQt6.QtWebEngineCore*',
    'PyQt6.QtWebEngineWidgets*',
    'PyQt6.QtWebChannel*',
    'PyQt6.QtMultimedia*',
    'PyQt6.QtMultimediaWidgets*',
    'PyQt6.QtQuick*',
    'PyQt6.QtQml*',
    'PyQt6.QtSql*',
    'PyQt6.QtTest*',
    'PyQt6.QtDesigner*',
    'PyQt6.QtHelp*',
    'PyQt6.QtPrintSupport*',
    'PyQt6.QtSvg*',
    'PyQt6.QtSvgWidgets*',
    'PyQt6.QtOpenGL*',
    'PyQt6.QtOpenGLWidgets*',
    'PyQt6.QtConcurrent*',
    'PyQt6.QtDBus*',
    'PyQt6.QtSerialPort*',
    'PyQt6.QtBluetooth*',
    'PyQt6.QtPositioning*',
    'PyQt6.QtSensors*',
    'PyQt6.QtRemoteObjects*',
    'PyQt6.QtCharts*',
    'PyQt6.QtDataVisualization*',
    'PyQt6.QtNetworkAuth*',
    'PyQt6.QtPdf*',
    'PyQt6.QtPdfWidgets*',
    
    # 调试符号文件
    '*.pdb',
    '*.debug',
    
    # 不需要的语言包
    'translations/qt_*',
    'translations/qtbase_*',
    'translations/qtmultimedia_*',
    'translations/qtwebengine_*',
    
    # 不需要的平台插件
    'platforms/qoffscreen*',
    'platforms/qminimal*',
    'platforms/qlinuxfb*',
    'platforms/qvnc*',
    'platforms/qxcb*',
    'platforms/qwayland*',
    'platforms/qcocoa*',
    'platforms/qios*',
    'platforms/qandroid*',
    'platforms/qeglfs*',
    'platforms/qdirectfb*',
    
    # 不需要的图像格式插件
    'imageformats/qgif*',
    'imageformats/qicns*',
    'imageformats/qjp2*',
    'imageformats/qmng*',
    'imageformats/qsvg*',
    'imageformats/qtga*',
    'imageformats/qtiff*',
    'imageformats/qwbmp*',
    'imageformats/qwebp*',
]

# 创建Analysis对象
a = Analysis(
    # 入口脚本
    [str(source_dir / 'main_pyqt6.py')],
    pathex=[str(project_root)],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=excludes,
    noarchive=False,
    # 启用优化
    optimize=2,
    # 移除调试信息
    strip=True,
)

# 过滤二进制文件 - 移除不需要的库
original_binaries = a.binaries
filtered_binaries = []

for binary in original_binaries:
    binary_name = binary[0].lower()
    should_exclude = False
    
    for exclude_pattern in binaries_excludes:
        if exclude_pattern.replace('*', '').lower() in binary_name:
            should_exclude = True
            break
    
    if not should_exclude:
        filtered_binaries.append(binary)

a.binaries = filtered_binaries

# 创建PYZ存档
pyz = PYZ(
    a.pure,
    # 启用压缩
    compress=True,
    # 启用加密（可选）
    # cipher=None,
)

# 创建最终的可执行文件
exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='GameServerChecker',
    debug=False,
    bootloader_ignore_signals=False,
    strip=True,
    upx=True,
    upx_exclude=[
        # 排除UPX压缩的文件（避免兼容性问题）
        'python*.dll',
        'PyQt6*.dll',
        'Qt6*.dll',
    ],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=str(source_dir / 'assets' / 'icon.ico'),
    # 版本信息
    version_file=None,
    # 清单文件
    manifest=None,
    # 资源文件
    resources=[],
)

# 生成信息打印
print("=" * 60)
print("PyInstaller配置优化完成！")
print("=" * 60)
print(f"入口文件: {source_dir / 'main_pyqt6.py'}")
print(f"数据文件: {len(datas)} 个")
print(f"隐藏导入: {len(hiddenimports)} 个")
print(f"排除模块: {len(excludes)} 个")
print(f"二进制排除规则: {len(binaries_excludes)} 个")
print("=" * 60)
print("主要优化措施:")
print("- 启用代码优化 (optimize=2)")
print("- 启用符号剥离 (strip=True)")
print("- 启用UPX压缩 (upx=True)")
print("- 排除不必要的标准库模块")
print("- 排除不必要的Qt模块")
print("- 排除OpenSSL冲突库")
print("- 排除调试和测试工具")
print("=" * 60)
print("使用方法:")
print("pyinstaller GameServerChecker_Optimized.spec")
print("=" * 60)