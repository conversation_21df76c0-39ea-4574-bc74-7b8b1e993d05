"""
高DPI显示修复工具
专门解决4K显示器下按钮文字模糊、不清晰的问题
"""

import sys
import os
from typing import List, Optional
from PyQt6.QtWidgets import QApplication, QPushButton, QWidget, QLabel
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QFont


class HighDPIFixer:
    """高DPI显示修复器"""
    
    def __init__(self):
        self.scale_factor = self._detect_scale_factor()
        self.is_high_dpi = self.scale_factor > 1.25
        self.is_4k = self.scale_factor >= 2.0
        
    def _detect_scale_factor(self) -> float:
        """检测系统DPI缩放因子"""
        try:
            if sys.platform == 'win32':
                import ctypes
                system_dpi = ctypes.windll.user32.GetDpiForSystem()
                return system_dpi / 96.0
            else:
                app = QApplication.instance()
                if app:
                    screen = app.primaryScreen()
                    if screen:
                        return screen.logicalDotsPerInch() / 96.0
                return 1.0
        except Exception as e:
            print(f"Error detecting scale factor: {e}")
            return 1.0
    
    def get_optimal_font_for_button(self, base_size: int = 9) -> QFont:
        """获取按钮的最优字体"""
        # 根据DPI缩放调整字体大小
        if self.is_4k:
            # 4K显示器下字体需要更大且更粗
            font_size = int(base_size * 1.3)
            font = QFont("Microsoft YaHei UI", font_size)
            font.setWeight(QFont.Weight.DemiBold)
        elif self.is_high_dpi:
            # 高DPI下适度调整
            font_size = int(base_size * 1.1)
            font = QFont("Microsoft YaHei UI", font_size)
            font.setWeight(QFont.Weight.Medium)
        else:
            # 标准DPI
            font = QFont("Microsoft YaHei UI", base_size)
            font.setWeight(QFont.Weight.Normal)
        
        # 启用最佳渲染质量
        font.setHintingPreference(QFont.HintingPreference.PreferFullHinting)
        font.setStyleStrategy(QFont.StyleStrategy.PreferAntialias | QFont.StyleStrategy.PreferQuality)
        
        return font
    
    def fix_button_display(self, button: QPushButton):
        """修复按钮显示问题"""
        try:
            # 设置优化字体
            optimal_font = self.get_optimal_font_for_button()
            button.setFont(optimal_font)
            
            # 高DPI下调整按钮尺寸
            if self.is_high_dpi:
                current_min_height = button.minimumHeight()
                current_max_width = button.maximumWidth()
                
                if current_min_height > 0:
                    new_min_height = int(current_min_height * self.scale_factor)
                    button.setMinimumHeight(new_min_height)
                
                if current_max_width < 16777215:  # Qt默认最大值
                    new_max_width = int(current_max_width * self.scale_factor)
                    button.setMaximumWidth(new_max_width)
            
            # 4K显示器下的特殊处理
            if self.is_4k:
                # 增加按钮内边距以提高可读性
                current_style = button.styleSheet()
                padding_style = "QPushButton { padding: 6px 12px; }"
                if current_style:
                    button.setStyleSheet(current_style + " " + padding_style)
                else:
                    button.setStyleSheet(padding_style)
            
            print(f"Fixed button '{button.text()}' for DPI scale {self.scale_factor:.2f}")
            
        except Exception as e:
            print(f"Error fixing button '{button.text()}': {e}")
    
    def fix_widget_fonts(self, widget: QWidget):
        """递归修复widget及其子组件的字体"""
        try:
            # 修复当前widget
            if isinstance(widget, QPushButton):
                self.fix_button_display(widget)
            elif isinstance(widget, QLabel):
                self.fix_label_display(widget)
            
            # 递归处理子组件
            for child in widget.findChildren(QWidget):
                if isinstance(child, QPushButton):
                    self.fix_button_display(child)
                elif isinstance(child, QLabel):
                    self.fix_label_display(child)
                    
        except Exception as e:
            print(f"Error fixing widget fonts: {e}")
    
    def fix_label_display(self, label: QLabel):
        """修复标签显示问题"""
        try:
            current_font = label.font()
            base_size = current_font.pointSize() if current_font.pointSize() > 0 else 9
            
            if self.is_4k:
                font_size = int(base_size * 1.2)
                font = QFont("Microsoft YaHei UI", font_size)
                font.setWeight(QFont.Weight.Medium)
            elif self.is_high_dpi:
                font_size = int(base_size * 1.1)
                font = QFont("Microsoft YaHei UI", font_size)
                font.setWeight(QFont.Weight.Normal)
            else:
                return  # 标准DPI不需要修改
            
            font.setHintingPreference(QFont.HintingPreference.PreferFullHinting)
            font.setStyleStrategy(QFont.StyleStrategy.PreferAntialias)
            label.setFont(font)
            
        except Exception as e:
            print(f"Error fixing label '{label.text()}': {e}")
    
    def apply_global_fixes(self):
        """应用全局DPI修复"""
        try:
            app = QApplication.instance()
            if not app:
                return
            
            # 设置全局字体
            global_font = self.get_optimal_font_for_button()
            app.setFont(global_font)
            
            # 遍历所有顶级窗口
            for widget in app.topLevelWidgets():
                self.fix_widget_fonts(widget)
                
            print(f"Applied global DPI fixes (scale: {self.scale_factor:.2f})")
            
        except Exception as e:
            print(f"Error applying global fixes: {e}")
    
    def print_dpi_info(self):
        """打印DPI信息"""
        print("=== High DPI Fix Info ===")
        print(f"Scale Factor: {self.scale_factor:.2f}")
        print(f"High DPI: {self.is_high_dpi}")
        print(f"4K Display: {self.is_4k}")
        
        try:
            app = QApplication.instance()
            if app:
                screen = app.primaryScreen()
                if screen:
                    geometry = screen.geometry()
                    print(f"Screen Size: {geometry.width()}x{geometry.height()}")
                    print(f"DPI: {screen.logicalDotsPerInch()}")
        except Exception as e:
            print(f"Error getting screen info: {e}")
        
        print("========================")


# 全局实例
high_dpi_fixer = HighDPIFixer()


def fix_button_dpi(button: QPushButton):
    """修复单个按钮的DPI显示问题"""
    high_dpi_fixer.fix_button_display(button)


def fix_widget_dpi(widget: QWidget):
    """修复widget的DPI显示问题"""
    high_dpi_fixer.fix_widget_fonts(widget)


def apply_global_dpi_fixes():
    """应用全局DPI修复"""
    high_dpi_fixer.apply_global_fixes()


def setup_high_dpi_environment():
    """设置高DPI环境"""
    try:
        # 在创建QApplication之前调用
        if hasattr(Qt.ApplicationAttribute, 'AA_EnableHighDpiScaling'):
            QApplication.setAttribute(Qt.ApplicationAttribute.AA_EnableHighDpiScaling, True)
        if hasattr(Qt.ApplicationAttribute, 'AA_UseHighDpiPixmaps'):
            QApplication.setAttribute(Qt.ApplicationAttribute.AA_UseHighDpiPixmaps, True)
        
        print("High DPI environment setup completed")
        
    except Exception as e:
        print(f"Error setting up high DPI environment: {e}")


def get_dpi_info():
    """获取DPI信息"""
    high_dpi_fixer.print_dpi_info()
    return {
        'scale_factor': high_dpi_fixer.scale_factor,
        'is_high_dpi': high_dpi_fixer.is_high_dpi,
        'is_4k': high_dpi_fixer.is_4k
    }


if __name__ == "__main__":
    # 测试代码
    app = QApplication(sys.argv)
    
    # 打印DPI信息
    get_dpi_info()
    
    # 创建测试按钮
    test_button = QPushButton("刷新磁盘")
    test_button.setMinimumHeight(30)
    test_button.setMaximumWidth(80)
    
    print("Before fix:")
    print(f"Button font: {test_button.font().family()}, size: {test_button.font().pointSize()}")
    
    # 应用修复
    fix_button_dpi(test_button)
    
    print("After fix:")
    print(f"Button font: {test_button.font().family()}, size: {test_button.font().pointSize()}")
    
    test_button.show()
    sys.exit(app.exec())
