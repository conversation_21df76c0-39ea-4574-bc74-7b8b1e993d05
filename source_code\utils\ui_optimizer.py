"""
UI优化器模块
提供UI更新优化、批处理和性能监控功能
"""

import time
from typing import Callable, List, Dict, Any, Optional
from PyQt6.QtCore import QObject, QTimer, pyqtSignal
from PyQt6.QtWidgets import QApplication

from utils.performance_optimizer import performance_monitor, monitor_performance


class BatchUIUpdater(QObject):
    """批量UI更新器 - 减少UI更新频率"""
    
    def __init__(self, batch_size: int = 20, delay_ms: int = 16):  # 60 FPS
        super().__init__()
        self.batch_size = batch_size
        self.delay_ms = delay_ms
        self._update_queue = []
        self._timer = QTimer()
        self._timer.setSingleShot(True)
        self._timer.timeout.connect(self._process_updates)
        self._processing = False
    
    def add_update(self, update_func: Callable):
        """添加UI更新任务"""
        if self._processing:
            # 如果正在处理，直接执行
            try:
                update_func()
            except Exception as e:
                print(f"UI更新错误: {e}")
            return
        
        self._update_queue.append(update_func)
        
        # 如果队列满了或者定时器未激活，立即处理
        if len(self._update_queue) >= self.batch_size or not self._timer.isActive():
            self._process_updates()
        elif not self._timer.isActive():
            self._timer.start(self.delay_ms)
    
    def _process_updates(self):
        """处理批量更新"""
        if self._timer.isActive():
            self._timer.stop()
        
        if not self._update_queue:
            return
        
        self._processing = True
        updates = self._update_queue.copy()
        self._update_queue.clear()
        
        start_time = time.time()
        processed_count = 0
        
        try:
            for update_func in updates:
                try:
                    update_func()
                    processed_count += 1
                except Exception as e:
                    print(f"批量UI更新错误: {e}")
        finally:
            self._processing = False
            
            # 记录性能统计
            duration = time.time() - start_time
            performance_monitor.record_timing('ui_batch_update', duration)
            performance_monitor.increment_counter('ui_updates_processed')
            
            if duration > 0.016:  # 超过16ms (60 FPS)
                print(f"UI批量更新耗时较长: {duration:.3f}s, 处理了{processed_count}个更新")
    
    def flush(self):
        """立即处理所有待处理的更新"""
        if self._update_queue:
            self._process_updates()


class ProgressUpdateThrottler(QObject):
    """进度更新节流器 - 限制进度更新频率"""
    
    progress_updated = pyqtSignal(float, str)
    
    def __init__(self, min_interval_ms: int = 100):
        super().__init__()
        self.min_interval_ms = min_interval_ms
        self._last_update_time = 0
        self._pending_update = None
        self._timer = QTimer()
        self._timer.setSingleShot(True)
        self._timer.timeout.connect(self._emit_pending_update)
    
    def update_progress(self, value: float, message: str = ""):
        """更新进度（节流版本）"""
        current_time = time.time() * 1000  # 转换为毫秒
        
        # 如果距离上次更新时间足够长，立即更新
        if current_time - self._last_update_time >= self.min_interval_ms:
            self._last_update_time = current_time
            self.progress_updated.emit(value, message)
            
            # 取消待处理的更新
            if self._timer.isActive():
                self._timer.stop()
                self._pending_update = None
        else:
            # 否则，设置待处理的更新
            self._pending_update = (value, message)
            
            # 如果定时器未激活，启动它
            if not self._timer.isActive():
                remaining_time = self.min_interval_ms - (current_time - self._last_update_time)
                self._timer.start(max(1, int(remaining_time)))
    
    def _emit_pending_update(self):
        """发送待处理的更新"""
        if self._pending_update:
            value, message = self._pending_update
            self._pending_update = None
            self._last_update_time = time.time() * 1000
            self.progress_updated.emit(value, message)


class UIPerformanceMonitor(QObject):
    """UI性能监控器"""
    
    def __init__(self, sample_interval_ms: int = 1000):
        super().__init__()
        self.sample_interval_ms = sample_interval_ms
        self._timer = QTimer()
        self._timer.timeout.connect(self._collect_metrics)
        self._metrics = {
            'frame_time': [],
            'update_count': 0,
            'memory_usage': []
        }
        self._last_frame_time = time.time()
        
        # 连接到应用程序的事件循环
        app = QApplication.instance()
        if app:
            app.aboutToQuit.connect(self._on_app_quit)
    
    def start_monitoring(self):
        """开始监控"""
        self._timer.start(self.sample_interval_ms)
        print("UI性能监控已启动")
    
    def stop_monitoring(self):
        """停止监控"""
        self._timer.stop()
        print("UI性能监控已停止")
    
    def _collect_metrics(self):
        """收集性能指标"""
        current_time = time.time()
        frame_time = current_time - self._last_frame_time
        self._last_frame_time = current_time
        
        # 记录帧时间
        self._metrics['frame_time'].append(frame_time)
        if len(self._metrics['frame_time']) > 60:  # 保留最近60个样本
            self._metrics['frame_time'].pop(0)
        
        # 记录更新计数
        self._metrics['update_count'] += 1
        
        # 记录内存使用（如果可用）
        try:
            import psutil
            process = psutil.Process()
            memory_mb = process.memory_info().rss / 1024 / 1024
            self._metrics['memory_usage'].append(memory_mb)
            if len(self._metrics['memory_usage']) > 60:
                self._metrics['memory_usage'].pop(0)
        except ImportError:
            pass
        
        # 检查性能问题
        if frame_time > 0.1:  # 超过100ms
            print(f"检测到UI性能问题: 帧时间 {frame_time:.3f}s")
    
    def get_metrics(self) -> Dict[str, Any]:
        """获取性能指标"""
        metrics = {
            'update_count': self._metrics['update_count'],
            'avg_frame_time': sum(self._metrics['frame_time']) / max(len(self._metrics['frame_time']), 1),
            'max_frame_time': max(self._metrics['frame_time']) if self._metrics['frame_time'] else 0,
            'fps': 1.0 / max(sum(self._metrics['frame_time']) / max(len(self._metrics['frame_time']), 1), 0.001)
        }
        
        if self._metrics['memory_usage']:
            metrics.update({
                'avg_memory_mb': sum(self._metrics['memory_usage']) / len(self._metrics['memory_usage']),
                'max_memory_mb': max(self._metrics['memory_usage']),
                'current_memory_mb': self._metrics['memory_usage'][-1] if self._metrics['memory_usage'] else 0
            })
        
        return metrics
    
    def _on_app_quit(self):
        """应用程序退出时的清理"""
        self.stop_monitoring()
        
        # 输出最终统计
        metrics = self.get_metrics()
        print("UI性能监控最终统计:")
        for key, value in metrics.items():
            if isinstance(value, float):
                print(f"  {key}: {value:.3f}")
            else:
                print(f"  {key}: {value}")


# 全局实例
batch_ui_updater = BatchUIUpdater()
progress_throttler = ProgressUpdateThrottler()
ui_performance_monitor = UIPerformanceMonitor()


@monitor_performance('ui_update')
def optimized_ui_update(update_func: Callable):
    """优化的UI更新函数"""
    batch_ui_updater.add_update(update_func)


def start_ui_monitoring():
    """启动UI性能监控"""
    ui_performance_monitor.start_monitoring()


def stop_ui_monitoring():
    """停止UI性能监控"""
    ui_performance_monitor.stop_monitoring()


def get_ui_performance_stats() -> Dict[str, Any]:
    """获取UI性能统计"""
    return ui_performance_monitor.get_metrics()
