
This file lists modules PyInstaller was not able to find. This does not
necessarily mean this module is required for running your program. Python and
Python 3rd-party packages include a lot of conditional or optional modules. For
example the module 'ntpath' only exists on Windows, whereas the module
'posixpath' only exists on Posix systems.

Types if import:
* top-level: imported at the top-level - look at these first
* conditional: imported within an if-statement
* delayed: imported within a function
* optional: imported within a try-except-statement

IMPORTANT: Do NOT post this list to the issue-tracker. Use it as a basis for
            tracking down the missing module yourself. Thanks!

missing module named pwd - imported by posixpath (delayed, conditional, optional), subprocess (delayed, conditional, optional), shutil (delayed, optional), tarfile (optional), pathlib._local (optional), psutil (optional), getpass (delayed, optional), netrc (delayed, conditional)
missing module named grp - imported by subprocess (delayed, conditional, optional), shutil (delayed, optional), tarfile (optional), pathlib._local (optional)
missing module named 'collections.abc' - imported by tracemalloc (top-level), traceback (top-level), typing (top-level), selectors (top-level), logging (top-level), importlib.resources.readers (top-level), configparser (top-level), http.client (top-level), typing_extensions (top-level), asyncio.base_events (top-level), asyncio.coroutines (top-level), requests.compat (top-level), schedule (top-level)
excluded module named base64 - imported by email.base64mime (top-level), email.encoders (top-level), email._encoded_words (top-level), encodings.base64_codec (top-level), secrets (top-level), urllib3.util.request (top-level), urllib.request (top-level), requests.auth (top-level)
excluded module named quopri - imported by email.encoders (top-level), email.message (top-level), encodings.quopri_codec (top-level)
excluded module named binascii - imported by zipfile (top-level), email.header (top-level), email.base64mime (top-level), email.message (top-level), email._encoded_words (top-level), email.contentmanager (top-level), encodings.hex_codec (top-level), encodings.uu_codec (top-level), urllib3.filepost (top-level), urllib3.util.ssl_ (top-level)
excluded module named py_compile - imported by zipfile (delayed)
excluded module named inspect - imported by dataclasses (top-level), warnings (delayed, conditional), typing (delayed), importlib.resources._common (top-level), importlib.metadata (top-level), pkgutil (delayed, optional), psutil._common (delayed, conditional), typing_extensions (top-level), asyncio.coroutines (top-level), asyncio.format_helpers (top-level), asyncio.tasks (top-level)
excluded module named tokenize - imported by linecache (delayed, optional), importlib._bootstrap_external (delayed)
missing module named posix - imported by os (conditional, optional), posixpath (optional), shutil (conditional), importlib._bootstrap_external (conditional)
missing module named resource - imported by posix (top-level)
missing module named _frozen_importlib_external - imported by importlib._bootstrap (delayed), importlib (optional), importlib.abc (optional), zipimport (top-level)
excluded module named _frozen_importlib - imported by importlib (optional), importlib.abc (optional), zipimport (top-level)
missing module named _posixsubprocess - imported by subprocess (conditional), multiprocessing.util (delayed)
missing module named fcntl - imported by subprocess (optional)
missing module named _posixshmem - imported by multiprocessing.resource_tracker (conditional), multiprocessing.shared_memory (conditional)
missing module named 'xmlrpc.client' - imported by multiprocessing.connection (delayed)
missing module named multiprocessing.BufferTooShort - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named multiprocessing.AuthenticationError - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named multiprocessing.get_context - imported by multiprocessing (top-level), multiprocessing.pool (top-level), multiprocessing.managers (top-level), multiprocessing.sharedctypes (top-level)
missing module named multiprocessing.TimeoutError - imported by multiprocessing (top-level), multiprocessing.pool (top-level)
missing module named multiprocessing.set_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named multiprocessing.get_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named pyimod02_importers - imported by C:\Program Files\Python313\Lib\site-packages\PyInstaller\hooks\rthooks\pyi_rth_pkgutil.py (delayed)
missing module named pytz - imported by schedule (delayed, conditional)
missing module named _scproxy - imported by urllib.request (conditional)
missing module named termios - imported by getpass (optional)
excluded module named ftplib - imported by urllib.request (delayed, conditional)
excluded module named mimetypes - imported by urllib3.fields (top-level), urllib.request (delayed)
missing module named simplejson - imported by requests.compat (conditional, optional)
missing module named dummy_threading - imported by requests.cookies (optional)
missing module named asyncio.DefaultEventLoopPolicy - imported by asyncio (delayed, conditional), asyncio.events (delayed, conditional)
missing module named annotationlib - imported by typing_extensions (conditional)
missing module named zstandard - imported by urllib3.util.request (optional), urllib3.response (optional)
missing module named compression - imported by urllib3.util.request (optional), urllib3.response (optional)
missing module named 'h2.events' - imported by urllib3.http2.connection (top-level)
missing module named 'h2.connection' - imported by urllib3.http2.connection (top-level)
missing module named h2 - imported by urllib3.http2.connection (top-level)
missing module named brotli - imported by urllib3.util.request (optional), urllib3.response (optional)
missing module named brotlicffi - imported by urllib3.util.request (optional), urllib3.response (optional)
missing module named socks - imported by urllib3.contrib.socks (optional)
excluded module named cryptography - imported by urllib3.contrib.pyopenssl (top-level), requests (conditional, optional)
missing module named 'OpenSSL.crypto' - imported by urllib3.contrib.pyopenssl (delayed, conditional)
missing module named 'cryptography.x509' - imported by urllib3.contrib.pyopenssl (delayed, optional)
missing module named 'OpenSSL.SSL' - imported by urllib3.contrib.pyopenssl (top-level)
excluded module named ssl - imported by http.client (optional), asyncio.base_events (optional), asyncio.sslproto (optional), asyncio.selector_events (optional), urllib3.util.ssl_ (conditional, optional), urllib3.util.ssltransport (top-level), urllib3.connectionpool (conditional), urllib3._base_connection (conditional), urllib3.connection (optional), urllib3.poolmanager (conditional), urllib3 (optional), urllib.request (optional), requests (optional), urllib3.contrib.pyopenssl (top-level), urllib3.contrib.socks (optional), requests.adapters (optional)
missing module named 'pyodide.ffi' - imported by urllib3.contrib.emscripten.fetch (delayed, optional)
missing module named pyodide - imported by urllib3.contrib.emscripten.fetch (top-level)
missing module named js - imported by urllib3.contrib.emscripten.fetch (top-level)
excluded module named ast - imported by traceback (delayed)
missing module named vms_lib - imported by platform (delayed, optional)
missing module named 'java.lang' - imported by platform (delayed, optional)
missing module named java - imported by platform (delayed)
