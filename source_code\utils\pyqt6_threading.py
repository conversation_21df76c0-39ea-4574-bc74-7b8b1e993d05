"""
PyQt6专用线程工具模块
提供与PyQt6信号槽机制兼容的线程管理
"""

import threading
from typing import Callable, Any, Optional, Tuple
from PyQt6.QtCore import QObject, QThread, pyqtSignal, QTimer
from PyQt6.QtWidgets import QApplication


class PyQt6WorkerThread(QThread):
    """PyQt6工作线程，支持信号槽机制"""
    
    # 定义信号
    finished = pyqtSignal(object)  # 完成信号，携带结果
    error = pyqtSignal(str)        # 错误信号
    progress = pyqtSignal(float)   # 进度信号
    
    def __init__(self, target: Callable, args: Tuple = (), kwargs: dict = None):
        super().__init__()
        self.target = target
        self.args = args
        self.kwargs = kwargs or {}
        self._stop_requested = False
    
    def run(self):
        """线程主函数"""
        try:
            result = self.target(*self.args, **self.kwargs)
            if not self._stop_requested:
                self.finished.emit(result)
        except Exception as e:
            self.error.emit(str(e))
    
    def stop(self):
        """请求停止线程"""
        self._stop_requested = True
        self.requestInterruption()


class PyQt6ThreadManager(QObject):
    """PyQt6线程管理器"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self._threads = []
        self._lock = threading.Lock()
    
    def start_thread(self, target: Callable, args: Tuple = (), 
                    callback: Optional[Callable] = None, 
                    error_callback: Optional[Callable] = None) -> PyQt6WorkerThread:
        """
        启动新的PyQt6线程
        
        Args:
            target: 要在线程中运行的函数
            args: 函数参数
            callback: 成功完成时的回调函数
            error_callback: 错误时的回调函数
            
        Returns:
            创建的线程对象
        """
        thread = PyQt6WorkerThread(target=target, args=args)
        
        # 连接信号
        if callback:
            thread.finished.connect(callback)
        if error_callback:
            thread.error.connect(error_callback)
        
        # 线程完成后自动清理
        thread.finished.connect(lambda: self._cleanup_thread(thread))
        thread.error.connect(lambda: self._cleanup_thread(thread))
        
        with self._lock:
            self._threads.append(thread)
        
        thread.start()
        return thread
    
    def _cleanup_thread(self, thread: PyQt6WorkerThread):
        """清理完成的线程"""
        with self._lock:
            if thread in self._threads:
                self._threads.remove(thread)
        
        # 确保线程正确退出
        if thread.isRunning():
            thread.quit()
            thread.wait(1000)  # 等待1秒
    
    def stop_all_threads(self):
        """停止所有线程"""
        with self._lock:
            threads_to_stop = self._threads.copy()
        
        for thread in threads_to_stop:
            thread.stop()
            thread.quit()
            thread.wait(2000)  # 等待2秒
    
    def get_active_thread_count(self) -> int:
        """获取活跃线程数量"""
        with self._lock:
            return len([t for t in self._threads if t.isRunning()])
    
    def cleanup_finished_threads(self):
        """清理已完成的线程"""
        with self._lock:
            self._threads = [t for t in self._threads if t.isRunning()]


class SafeUIUpdater(QObject):
    """安全的UI更新器，确保UI更新在主线程中执行"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
    
    @staticmethod
    def call_in_main_thread(func: Callable, *args, **kwargs):
        """在主线程中安全地调用函数"""
        def wrapper():
            try:
                func(*args, **kwargs)
            except Exception as e:
                print(f"Error in UI update: {e}")
        
        # 使用QTimer.singleShot确保在主线程中执行
        QTimer.singleShot(0, wrapper)
    
    @staticmethod
    def update_progress_bar(progress_bar, value: int):
        """安全更新进度条"""
        SafeUIUpdater.call_in_main_thread(progress_bar.setValue, value)
    
    @staticmethod
    def update_status_bar(status_bar, message: str):
        """安全更新状态栏"""
        SafeUIUpdater.call_in_main_thread(status_bar.showMessage, message)
    
    @staticmethod
    def append_to_log(log_widget, message: str):
        """安全添加日志消息"""
        SafeUIUpdater.call_in_main_thread(log_widget.append, message)


def run_in_pyqt6_thread(func: Callable, *args, 
                       callback: Optional[Callable] = None,
                       error_callback: Optional[Callable] = None) -> PyQt6WorkerThread:
    """
    在PyQt6线程中运行函数的便捷方法
    
    Args:
        func: 要在线程中运行的函数
        *args: 函数参数
        callback: 成功完成时的回调函数
        error_callback: 错误时的回调函数
    
    Returns:
        创建的线程对象
    """
    thread = PyQt6WorkerThread(target=func, args=args)
    
    if callback:
        thread.finished.connect(callback)
    if error_callback:
        thread.error.connect(error_callback)
    
    thread.start()
    return thread


class ProgressReporter(QObject):
    """进度报告器，用于在工作线程中报告进度"""
    
    progress_updated = pyqtSignal(float, str)  # 进度值和状态消息
    
    def __init__(self, parent=None):
        super().__init__(parent)
    
    def report_progress(self, progress: float, message: str = ""):
        """报告进度"""
        self.progress_updated.emit(progress, message)


def create_progress_callback(progress_reporter: ProgressReporter):
    """创建进度回调函数"""
    def callback(progress: float, message: str = ""):
        progress_reporter.report_progress(progress, message)
    return callback


class ThreadSafeLogger(QObject):
    """线程安全的日志记录器"""
    
    log_message = pyqtSignal(str, str)  # 消息和级别
    
    def __init__(self, parent=None):
        super().__init__(parent)
    
    def log(self, message: str, level: str = "INFO"):
        """记录日志消息"""
        self.log_message.emit(message, level)
    
    def info(self, message: str):
        """记录信息消息"""
        self.log(message, "INFO")
    
    def warning(self, message: str):
        """记录警告消息"""
        self.log(message, "WARNING")
    
    def error(self, message: str):
        """记录错误消息"""
        self.log(message, "ERROR")
    
    def success(self, message: str):
        """记录成功消息"""
        self.log(message, "SUCCESS")


def ensure_main_thread(func: Callable):
    """装饰器：确保函数在主线程中执行"""
    def wrapper(*args, **kwargs):
        if QApplication.instance() and QApplication.instance().thread() != threading.current_thread():
            # 不在主线程中，使用QTimer.singleShot调度到主线程
            SafeUIUpdater.call_in_main_thread(func, *args, **kwargs)
        else:
            # 已在主线程中，直接执行
            func(*args, **kwargs)
    return wrapper
