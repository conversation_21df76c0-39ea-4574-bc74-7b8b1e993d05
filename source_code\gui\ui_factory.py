"""
UI组件工厂模块
提供统一的UI组件创建方法，减少重复代码，确保样式一致性
"""

from typing import Optional, Callable, List, Tuple
from PyQt6.QtWidgets import (
    QPushButton, QLabel, QLineEdit, QCheckBox, QGroupBox,
    QHBoxLayout, QVBoxLayout, QFrame, QScrollArea, QWidget
)
from PyQt6.QtCore import Qt, QSize
from PyQt6.QtGui import QFont

from .ui_constants import Sizes, Spacing, Fonts, Colors
from .styles_pyqt6 import StyleManager
from .custom_checkbox import create_custom_checkbox


class UIFactory:
    """UI组件工厂类"""
    
    @staticmethod
    def create_button(
        text: str,
        style_type: str = "default",
        size_type: str = "normal",
        min_width: Optional[int] = None,
        min_height: Optional[int] = None,
        callback: Optional[Callable] = None,
        enabled: bool = True,
        tooltip: Optional[str] = None,
        auto_width: bool = True
    ) -> QPushButton:
        """
        创建标准化按钮

        Args:
            text: 按钮文本
            style_type: 样式类型 (primary, secondary, success, warning, danger)
            size_type: 尺寸类型 (small, normal, large)
            min_width: 最小宽度
            min_height: 最小高度
            callback: 点击回调函数
            enabled: 是否启用
            tooltip: 工具提示
            auto_width: 是否根据文字自动调整宽度
        """
        button = QPushButton(text)

        # 计算文字所需的最小宽度
        if auto_width and text:
            # 估算中文字符宽度（每个中文字符约14像素，英文字符约8像素）
            text_width = 0
            for char in text:
                if ord(char) > 127:  # 中文字符
                    text_width += 14
                else:  # 英文字符
                    text_width += 8
            # 添加内边距和边框
            calculated_width = text_width + 40  # 左右各20像素内边距
        else:
            calculated_width = 0

        # 设置尺寸
        if size_type == "small":
            base_height = Sizes.BUTTON_MIN_HEIGHT
            base_width = max(Sizes.BUTTON_MIN_WIDTH, calculated_width)
        elif size_type == "large":
            base_height = Sizes.ACTION_BUTTON_HEIGHT
            base_width = max(Sizes.PRIMARY_BUTTON_MIN_WIDTH, calculated_width)
        else:  # normal
            base_height = Sizes.ACTION_BUTTON_HEIGHT
            base_width = max(Sizes.ACTION_BUTTON_MIN_WIDTH, calculated_width)

        button.setMinimumHeight(min_height or base_height)
        button.setMinimumWidth(min_width or base_width)
        
        # 设置样式
        StyleManager.set_button_style(button, style_type)
        
        # 设置回调
        if callback:
            button.clicked.connect(callback)
        
        # 设置状态
        button.setEnabled(enabled)
        
        # 设置工具提示
        if tooltip:
            button.setToolTip(tooltip)

        # 应用DPI适配修复
        try:
            from utils.dpi_adapter import apply_button_dpi_fix
            apply_button_dpi_fix(button)
        except Exception as e:
            print(f"Warning: Failed to apply DPI fix to button '{text}': {e}")

        return button
    
    @staticmethod
    def create_label(
        text: str,
        style_type: str = "default",
        font_size: Optional[int] = None,
        bold: bool = False,
        color: Optional[str] = None,
        alignment: Qt.AlignmentFlag = Qt.AlignmentFlag.AlignLeft,
        word_wrap: bool = False
    ) -> QLabel:
        """
        创建标准化标签
        
        Args:
            text: 标签文本
            style_type: 样式类型 (title, subtitle, caption, default)
            font_size: 字体大小
            bold: 是否粗体
            color: 文本颜色
            alignment: 对齐方式
            word_wrap: 是否自动换行
        """
        label = QLabel(text)
        
        # 设置对齐
        label.setAlignment(alignment)
        
        # 设置自动换行
        label.setWordWrap(word_wrap)
        
        # 设置字体
        if style_type == "title":
            font = QFont(Fonts.DEFAULT_FAMILY, Fonts.TITLE_SIZE, QFont.Weight.Bold)
            label.setFont(font)
            StyleManager.set_label_style(label, "title")
        elif style_type == "subtitle":
            font = QFont(Fonts.DEFAULT_FAMILY, Fonts.SUBTITLE_SIZE, QFont.Weight.Bold)
            label.setFont(font)
            StyleManager.set_label_style(label, "subtitle")
        elif style_type == "caption":
            font = QFont(Fonts.DEFAULT_FAMILY, Fonts.CAPTION_SIZE)
            label.setFont(font)
            StyleManager.set_label_style(label, "caption")
        else:
            if font_size or bold:
                weight = QFont.Weight.Bold if bold else QFont.Weight.Normal
                font = QFont(Fonts.DEFAULT_FAMILY, font_size or Fonts.NORMAL, weight)
                label.setFont(font)
        
        # 设置颜色
        if color:
            label.setStyleSheet(f"color: {color};")
        
        return label
    
    @staticmethod
    def create_input(
        placeholder: str = "",
        min_width: Optional[int] = None,
        min_height: Optional[int] = None,
        enabled: bool = True,
        text: str = "",
        callback: Optional[Callable] = None,
        tooltip: Optional[str] = None
    ) -> QLineEdit:
        """
        创建标准化输入框
        
        Args:
            placeholder: 占位符文本
            min_width: 最小宽度
            min_height: 最小高度
            enabled: 是否启用
            text: 初始文本
            callback: 文本变化回调
            tooltip: 工具提示
        """
        input_field = QLineEdit()
        
        # 设置属性
        if placeholder:
            input_field.setPlaceholderText(placeholder)
        if text:
            input_field.setText(text)
        
        # 设置尺寸
        input_field.setMinimumWidth(min_width or Sizes.INPUT_MIN_WIDTH)
        input_field.setMinimumHeight(min_height or Sizes.INPUT_MIN_HEIGHT)
        
        # 设置状态
        input_field.setEnabled(enabled)
        
        # 设置回调
        if callback:
            input_field.textChanged.connect(callback)
        
        # 设置工具提示
        if tooltip:
            input_field.setToolTip(tooltip)
        
        return input_field
    
    @staticmethod
    def create_checkbox(
        text: str,
        checked: bool = False,
        callback: Optional[Callable] = None,
        enabled: bool = True,
        tooltip: Optional[str] = None
    ) -> QCheckBox:
        """
        创建标准化复选框

        Args:
            text: 复选框文本
            checked: 是否选中
            callback: 状态变化回调
            enabled: 是否启用
            tooltip: 工具提示
        """
        # 使用自定义复选框以获得更好的视觉效果
        checkbox = create_custom_checkbox(
            text=text,
            checked=checked,
            enabled=enabled,
            callback=callback,
            tooltip=tooltip
        )

        # 不需要文本前缀功能，保持原始文本不变

        return checkbox
    
    @staticmethod
    def create_group_box(
        title: str,
        min_height: Optional[int] = None,
        layout_type: str = "vertical"
    ) -> Tuple[QGroupBox, QVBoxLayout | QHBoxLayout]:
        """
        创建标准化分组框
        
        Args:
            title: 分组框标题
            min_height: 最小高度
            layout_type: 布局类型 (vertical, horizontal)
        
        Returns:
            (分组框, 布局)
        """
        group_box = QGroupBox(title)
        
        # 设置最小高度
        if min_height:
            group_box.setMinimumHeight(min_height)
        
        # 创建布局
        if layout_type == "horizontal":
            layout = QHBoxLayout(group_box)
        else:
            layout = QVBoxLayout(group_box)
        
        # 设置布局属性
        layout.setSpacing(Spacing.LAYOUT_SPACING)
        layout.setContentsMargins(*Spacing.CONTENT_MARGIN)
        
        return group_box, layout
    
    @staticmethod
    def create_horizontal_layout(
        spacing: Optional[int] = None,
        margins: Optional[Tuple[int, int, int, int]] = None
    ) -> QHBoxLayout:
        """创建标准化水平布局"""
        layout = QHBoxLayout()
        layout.setSpacing(spacing or Spacing.LAYOUT_SPACING)
        if margins:
            layout.setContentsMargins(*margins)
        return layout
    
    @staticmethod
    def create_vertical_layout(
        spacing: Optional[int] = None,
        margins: Optional[Tuple[int, int, int, int]] = None
    ) -> QVBoxLayout:
        """创建标准化垂直布局"""
        layout = QVBoxLayout()
        layout.setSpacing(spacing or Spacing.SECTION_SPACING)
        if margins:
            layout.setContentsMargins(*margins)
        return layout
    
    @staticmethod
    def create_scroll_area(
        min_height: Optional[int] = None,
        widget_resizable: bool = True
    ) -> Tuple[QScrollArea, QWidget, QVBoxLayout]:
        """
        创建标准化滚动区域
        
        Returns:
            (滚动区域, 内容组件, 内容布局)
        """
        scroll_area = QScrollArea()
        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout(scroll_widget)
        
        # 设置属性
        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(widget_resizable)
        
        if min_height:
            scroll_area.setMinimumHeight(min_height)
        
        # 设置布局
        scroll_layout.setAlignment(Qt.AlignmentFlag.AlignTop)
        scroll_layout.setSpacing(Spacing.WIDGET_SPACING)
        
        return scroll_area, scroll_widget, scroll_layout
    
    # 复选框样式现在由CustomCheckBox类管理
    
    # 文本前缀功能已移除，复选框保持原始文本
