"""
系统相关工具函数模块
包含系统权限检查、单实例检查、DPI处理等功能
"""

import sys
import os
import ctypes
import ctypes.wintypes
import subprocess
from typing import Optional
from core.constants import WINDOW_TITLE, SW_RESTORE


# Ctypes函数和常量定义
user32 = ctypes.windll.user32
FindWindowW = user32.FindWindowW
SetForegroundWindow = user32.SetForegroundWindow
ShowWindow = user32.ShowWindow
IsIconic = user32.IsIconic


def is_admin() -> bool:
    """检查是否具有管理员权限 (Windows特定)"""
    try:
        return ctypes.windll.shell32.IsUserAnAdmin()
    except:
        return False


def setup_single_instance() -> bool:
    """
    设置单实例检查 (Windows特定)
    返回True表示是第一个实例，False表示已有实例运行
    """
    if sys.platform != 'win32':
        return True
        
    existing_hwnd = FindWindowW(None, WINDOW_TITLE)
    if existing_hwnd != 0:
        print(f"检测到已运行实例 (HWND: {existing_hwnd}), 正在激活...")
        try:
            # 如果窗口最小化则恢复
            if IsIconic(existing_hwnd):
                ShowWindow(existing_hwnd, SW_RESTORE)
            # 将窗口置于前台
            SetForegroundWindow(existing_hwnd)
            print("激活成功，退出当前实例。")
        except Exception as e:
            print(f"激活现有窗口时出错: {e}")
        return False
    else:
        print("未检测到已运行实例，继续启动...")
        return True


def request_admin_privileges() -> bool:
    """
    请求管理员权限
    返回True表示成功请求权限，False表示失败
    """
    if not is_admin():
        print("检测到非管理员权限，正在请求提权...")
        try:
            # 使用ShellExecute请求管理员权限重新启动
            ctypes.windll.shell32.ShellExecuteW(
                None, 
                "runas", 
                sys.executable, 
                " ".join(sys.argv), 
                None, 
                1
            )
            print("已发送提权请求，退出当前进程。")
            return True
        except Exception as e:
            print(f"请求管理员权限失败: {e}")
            return False
    else:
        print("已具备管理员权限。")
        return False


def get_system_dpi() -> float:
    """获取系统DPI缩放因子"""
    try:
        # 获取系统DPI
        system_dpi = ctypes.windll.user32.GetDpiForSystem()
        scale_factor = system_dpi / 96.0  # 96 DPI是标准100%缩放

        print(f"System DPI detected: {system_dpi}, Raw Scale Factor: {scale_factor:.2f}")

        # 智能缩放策略：根据实际DPI调整
        if scale_factor <= 1.0:
            # 100%缩放或更小，保持原样
            adjusted_factor = 1.0
        elif scale_factor <= 1.25:
            # 125%缩放，保持原样
            adjusted_factor = scale_factor
        elif scale_factor <= 1.5:
            # 150%缩放，稍微减小以避免界面过大
            adjusted_factor = scale_factor * 0.9
        else:
            # 更高缩放，使用适中的缩放因子
            adjusted_factor = 1.3

        print(f"Applied Scale Factor: {adjusted_factor:.2f}")
        return adjusted_factor

    except AttributeError:
        print("Warning: Could not access ctypes DPI functions. Using default scaling.")
        return 1.0
    except Exception as e:
        print(f"Warning: Error getting system DPI: {e}. Using default scaling.")
        return 1.0


def detect_drives() -> list:
    """检测可用的驱动器"""
    drives = []
    try:
        import psutil
        partitions = psutil.disk_partitions()
        for partition in partitions:
            try:
                # 检查分区是否可访问
                partition_usage = psutil.disk_usage(partition.mountpoint)
                # 格式化显示：C: (NTFS) - 500GB
                drive_info = f"{partition.device} ({partition.fstype})"
                drives.append(drive_info)
            except PermissionError:
                # 无权限访问的驱动器也添加，但标记
                drive_info = f"{partition.device} (无权限)"
                drives.append(drive_info)
            except Exception:
                # 其他错误的驱动器跳过
                continue
    except ImportError:
        print("Warning: psutil not available, using fallback drive detection")
        # 备用方法：检查A-Z盘符
        for letter in 'ABCDEFGHIJKLMNOPQRSTUVWXYZ':
            drive_path = f"{letter}:\\"
            if os.path.exists(drive_path):
                drives.append(f"{letter}:")
    except Exception as e:
        print(f"Error detecting drives: {e}")
    
    return drives


def get_executable_path() -> str:
    """获取当前可执行文件的绝对路径"""
    if getattr(sys, 'frozen', False):
        # 打包后的可执行文件
        return sys.executable
    else:
        # 开发环境中的脚本文件
        return os.path.abspath(sys.argv[0])


def add_to_startup(app_name: str = "ServerCheckerTool") -> bool:
    """将应用程序添加到Windows启动项"""
    try:
        import winreg
        key_path = r"Software\Microsoft\Windows\CurrentVersion\Run"
        executable_path = get_executable_path()
        
        with winreg.OpenKey(winreg.HKEY_CURRENT_USER, key_path, 0, winreg.KEY_SET_VALUE) as key:
            winreg.SetValueEx(key, app_name, 0, winreg.REG_SZ, executable_path)
        
        print(f"Successfully added {app_name} to startup registry.")
        return True
    except Exception as e:
        print(f"Error adding to startup: {e}")
        return False


def remove_from_startup(app_name: str = "ServerCheckerTool") -> bool:
    """从Windows启动项中移除应用程序"""
    try:
        import winreg
        key_path = r"Software\Microsoft\Windows\CurrentVersion\Run"
        
        with winreg.OpenKey(winreg.HKEY_CURRENT_USER, key_path, 0, winreg.KEY_SET_VALUE) as key:
            try:
                winreg.DeleteValue(key, app_name)
                print(f"Successfully removed {app_name} from startup registry.")
                return True
            except FileNotFoundError:
                print(f"{app_name} was not found in startup registry.")
                return True
    except Exception as e:
        print(f"Error removing from startup: {e}")
        return False


def is_in_startup(app_name: str = "ServerCheckerTool") -> bool:
    """检查应用程序是否在启动项中"""
    try:
        import winreg
        key_path = r"Software\Microsoft\Windows\CurrentVersion\Run"
        
        with winreg.OpenKey(winreg.HKEY_CURRENT_USER, key_path, 0, winreg.KEY_READ) as key:
            try:
                winreg.QueryValueEx(key, app_name)
                return True
            except FileNotFoundError:
                return False
    except Exception as e:
        print(f"Error checking startup status: {e}")
        return False


def sync_system_time() -> bool:
    """
    同步系统时间到NTP服务器

    Returns:
        bool: 同步是否成功
    """
    import platform
    import time
    import socket
    import datetime

    if platform.system() != 'Windows':
        print("时间同步功能目前仅支持 Windows 系统。")
        return False

    ntp_server = "ntp.aliyun.com"

    try:
        # 尝试导入ntplib
        try:
            import ntplib
        except ImportError:
            print("错误: 未找到 `ntplib` 模块。请使用 `pip install ntplib` 安装。")
            return False

        print(f"正在连接 NTP 服务器: {ntp_server}...")
        ntp_client = ntplib.NTPClient()
        response = ntp_client.request(ntp_server, version=3, timeout=10)
        ntp_timestamp_utc = response.tx_time
        print(f"从 NTP 服务器获取时间戳 (UTC): {ntp_timestamp_utc}")

        # 获取当前系统时间
        system_timestamp_utc = time.time()
        print(f"当前系统时间戳 (UTC): {system_timestamp_utc}")

        # 计算时间差
        time_diff = ntp_timestamp_utc - system_timestamp_utc
        print(f"时间差: {time_diff:.4f} 秒")

        # 如果时间差超过1秒，尝试设置系统时间
        if abs(time_diff) > 1.0:
            print(f"时间差超过 1 秒，尝试设置系统时间...")
            try:
                import win32api

                utc_dt = datetime.datetime.utcfromtimestamp(ntp_timestamp_utc)

                # 构建Windows时间元组
                win_time_tuple = (
                    utc_dt.year,
                    utc_dt.month,
                    utc_dt.isoweekday() % 7,  # Windows星期格式
                    utc_dt.day,
                    utc_dt.hour,
                    utc_dt.minute,
                    utc_dt.second,
                    int(utc_dt.microsecond / 1000)  # 毫秒
                )
                print(f"准备设置系统时间为 (UTC): {win_time_tuple}")

                # 设置系统时间
                win32api.SetSystemTime(*win_time_tuple)
                print("成功设置系统时间！")
                return True

            except ImportError:
                print("错误: 未找到 `pywin32` 模块。请使用 `pip install pywin32` 安装，并确保以管理员权限运行程序以设置时间。")
                return False
            except Exception as e_set:
                error_code = getattr(e_set, 'winerror', None)
                if error_code == 5 or error_code == 1314:
                    print("错误: 设置系统时间失败 - 权限不足。请尝试以管理员权限运行此程序。")
                else:
                    print(f"错误: 设置系统时间时发生未知错误: {e_set} (代码: {error_code})")
                return False
        else:
            print("时间差在 1 秒内，无需同步。")
            return True

    except Exception as e:
        if "ntplib.NTPException" in str(type(e)):
            print(f"错误: 连接 NTP 服务器 ({ntp_server}) 或获取时间时出错: {e}")
        elif isinstance(e, socket.gaierror):
            print(f"错误: 无法解析 NTP 服务器地址 ({ntp_server}): {e}")
        elif isinstance(e, OSError):
            print(f"错误: 网络操作失败 (NTP): {e}")
        else:
            print(f"错误: 时间同步过程中发生意外错误: {e}")
        return False


def resource_path(relative_path: str) -> str:
    """
    获取资源文件的绝对路径，兼容开发环境和PyInstaller打包后的环境
    
    Args:
        relative_path: 相对路径
        
    Returns:
        资源文件的绝对路径
    """
    try:
        # PyInstaller打包后的临时文件夹路径
        if hasattr(sys, '_MEIPASS'):
            # 打包后环境：使用临时解压路径
            base_path = sys._MEIPASS
            resource_file_path = os.path.join(base_path, relative_path)
            if os.path.exists(resource_file_path):
                return resource_file_path
        
        # 开发环境：使用相对路径
        # 获取项目根目录
        if hasattr(sys, 'frozen') and sys.frozen:
            # 如果是打包的可执行文件，获取可执行文件所在目录
            base_path = os.path.dirname(sys.executable)
        else:
            # 开发环境，获取项目根目录
            current_dir = os.path.dirname(os.path.abspath(__file__))
            # utils/system.py -> utils -> source_code -> project_root
            base_path = os.path.dirname(os.path.dirname(current_dir))
        
        # 尝试多个可能的路径
        possible_paths = [
            os.path.join(base_path, relative_path),
            os.path.join(os.getcwd(), relative_path),
            os.path.join(os.getcwd(), "source_code", relative_path),  # 开发环境中的source_code路径
            relative_path  # 直接使用相对路径作为最后尝试
        ]
        
        for path in possible_paths:
            if os.path.exists(path):
                return os.path.abspath(path)
        
        # 如果都找不到，返回原始路径
        return relative_path
        
    except Exception:
        # 精简异常处理，减少字符串常量
        return relative_path


def get_icon_path(icon_filename: str = "assets/icon.ico") -> Optional[str]:
    """
    获取应用图标的完整路径

    Args:
        icon_filename: 图标文件名

    Returns:
        图标文件的完整路径，如果不存在则返回None
    """
    try:
        # 使用resource_path获取路径
        icon_path = resource_path(icon_filename)

        if os.path.exists(icon_path):
            return icon_path

        # 如果主图标不存在，尝试其他常见的图标文件
        alternative_icons = [
            "assets/icon.ico",
            "assets/app_icon.ico",
            "assets/app_icon.png",
            "icon.ico",
            "icon.png",
            "favicon.ico",
            "app_icon.ico",
            "app_icon.png"
        ]

        for alt_icon in alternative_icons:
            alt_path = resource_path(alt_icon)
            if os.path.exists(alt_path):
                return alt_path

        return None
        
    except Exception:
        # 精简异常处理
        return None
