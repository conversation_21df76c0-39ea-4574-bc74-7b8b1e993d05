"""
配置管理服务
负责应用程序的配置加载、保存和状态恢复
"""

from typing import Dict, Any, Optional
from PyQt6.QtCore import QObject, pyqtSignal

from utils.smart_logger import create_debug_logger_for_class


@create_debug_logger_for_class
class ConfigurationService(QObject):
    """配置管理服务类 - 负责应用程序配置的统一管理"""
    
    # 信号定义
    config_loaded = pyqtSignal(dict)  # 配置加载完成
    config_saved = pyqtSignal(bool)   # 配置保存结果
    state_restored = pyqtSignal()     # 状态恢复完成
    
    def __init__(self, app_instance):
        super().__init__()
        self.app = app_instance
        self._config_data = {}
        
    def _log(self, message: str, level: str = "INFO"):
        """内部日志方法"""
        if hasattr(self.app, 'log'):
            self.app.log(message, level)
    
    def load_application_config(self) -> Dict[str, Any]:
        """
        加载应用程序配置
        
        Returns:
            加载的配置数据
        """
        try:
            self._log("正在加载应用程序配置...", "INFO")
            
            # 加载配置文件
            config_data = self.app.config.load_config()
            self._config_data = config_data
            
            self.debug_log("配置数据大小: {} KB", len(str(config_data)) / 1024)
            
            # 发射配置加载信号
            self.config_loaded.emit(config_data)
            
            return config_data
            
        except Exception as e:
            self._log(f"加载配置时出错: {e}", "ERROR")
            return {}
    
    def restore_application_state(self) -> bool:
        """
        恢复应用程序状态
        
        Returns:
            恢复是否成功
        """
        try:
            self._log("正在恢复应用程序状态...", "INFO")
            
            # 恢复各个管理器的设置
            restoration_results = {}
            
            # 恢复调度管理器设置
            if hasattr(self.app, 'schedule_manager'):
                try:
                    self.app.schedule_manager.load_settings_from_config()
                    restoration_results['schedule_manager'] = True
                    self.debug_log("调度管理器设置已恢复")
                except Exception as e:
                    self.debug_log("恢复调度管理器设置失败: {}", e)
                    restoration_results['schedule_manager'] = False
            
            # 恢复上传管理器设置
            if hasattr(self.app, 'upload_manager'):
                try:
                    self.app.upload_manager.load_settings_from_config()
                    restoration_results['upload_manager'] = True
                    self.debug_log("上传管理器设置已恢复")
                except Exception as e:
                    self.debug_log("恢复上传管理器设置失败: {}", e)
                    restoration_results['upload_manager'] = False
            
            # 恢复文件管理器状态
            if hasattr(self.app, 'file_manager'):
                try:
                    self.app.file_manager.restore_file_list()
                    restoration_results['file_manager'] = True
                    self.debug_log("文件管理器状态已恢复")
                except Exception as e:
                    self.debug_log("恢复文件管理器状态失败: {}", e)
                    restoration_results['file_manager'] = False
            
            # 恢复UI状态
            try:
                self.restore_ui_state()
                restoration_results['ui_state'] = True
                self.debug_log("UI状态已恢复")
            except Exception as e:
                self.debug_log("恢复UI状态失败: {}", e)
                restoration_results['ui_state'] = False
            
            # 统计恢复结果
            total_components = len(restoration_results)
            successful_components = sum(1 for success in restoration_results.values() if success)
            
            # 生成恢复报告
            self._generate_restoration_report(restoration_results)
            
            # 发射状态恢复完成信号
            self.state_restored.emit()
            
            return successful_components == total_components
            
        except Exception as e:
            self._log(f"恢复应用程序状态时出错: {e}", "ERROR")
            return False
    
    def restore_ui_state(self):
        """恢复UI状态"""
        try:
            # 恢复各个管理器的UI状态
            if hasattr(self.app, 'schedule_manager'):
                self.app.schedule_manager.restore_ui_state()
                
            if hasattr(self.app, 'upload_manager'):
                self.app.upload_manager.restore_ui_state()
            
            self.debug_log("UI状态恢复完成")
            
        except Exception as e:
            self._log(f"恢复UI状态时出错: {e}", "ERROR")
            raise
    
    def _generate_restoration_report(self, restoration_results: Dict[str, bool]):
        """生成恢复报告"""
        try:
            # 计算文件数量
            file_count = 0
            if hasattr(self.app, 'file_manager') and hasattr(self.app.file_manager, 'file_checkboxes'):
                file_count = len(self.app.file_manager.file_checkboxes)
            
            # 获取调度状态
            schedule_status = "未知"
            if hasattr(self.app, 'schedule_manager') and hasattr(self.app.schedule_manager, 'auto_check_enabled'):
                schedule_status = "启用" if self.app.schedule_manager.auto_check_enabled else "禁用"
            
            # 获取上传状态
            upload_status = "未知"
            if hasattr(self.app, 'upload_manager') and hasattr(self.app.upload_manager, 'upload_enabled'):
                upload_status = "启用" if self.app.upload_manager.upload_enabled else "禁用"
            
            # 计算成功率
            total_components = len(restoration_results)
            successful_components = sum(1 for success in restoration_results.values() if success)
            success_rate = (successful_components / total_components * 100) if total_components > 0 else 0
            
            # 生成报告
            if success_rate == 100:
                self._log(f"✅ 配置加载完成，共恢复 {file_count} 个文件，定时检查: {schedule_status}，自动上传: {upload_status}", "SUCCESS")
            else:
                failed_components = [name for name, success in restoration_results.items() if not success]
                self._log(f"⚠️ 配置部分加载完成 ({success_rate:.1f}%)，失败组件: {', '.join(failed_components)}", "WARNING")
                
        except Exception as e:
            self.debug_log("生成恢复报告时出错: {}", e)
    
    def save_application_config(self, config_data: Optional[Dict[str, Any]] = None) -> bool:
        """
        保存应用程序配置
        
        Args:
            config_data: 要保存的配置数据，如果为None则使用当前配置
            
        Returns:
            保存是否成功
        """
        try:
            if config_data is not None:
                self._config_data = config_data
            
            # 调用主配置对象保存
            success = self.app.config.save_config(self._config_data)
            
            if success:
                self.debug_log("配置保存成功")
            else:
                self._log("配置保存失败", "ERROR")
            
            # 发射保存结果信号
            self.config_saved.emit(success)
            
            return success
            
        except Exception as e:
            self._log(f"保存配置时出错: {e}", "ERROR")
            self.config_saved.emit(False)
            return False
    
    def get_config_value(self, key: str, default_value: Any = None) -> Any:
        """
        获取配置值
        
        Args:
            key: 配置键
            default_value: 默认值
            
        Returns:
            配置值
        """
        try:
            return self._config_data.get(key, default_value)
        except Exception as e:
            self.debug_log("获取配置值 {} 时出错: {}", key, e)
            return default_value
    
    def set_config_value(self, key: str, value: Any) -> bool:
        """
        设置配置值
        
        Args:
            key: 配置键
            value: 配置值
            
        Returns:
            设置是否成功
        """
        try:
            self._config_data[key] = value
            self.debug_log("配置值 {} 已更新", key)
            return True
        except Exception as e:
            self.debug_log("设置配置值 {} 时出错: {}", key, e)
            return False
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取配置管理统计信息"""
        return {
            'config_size': len(str(self._config_data)),
            'config_keys': len(self._config_data),
            'has_config': bool(self._config_data)
        }
    
    def validate_config(self) -> Dict[str, bool]:
        """
        验证配置完整性
        
        Returns:
            验证结果字典
        """
        validation_results = {}
        
        try:
            # 验证基本配置结构
            validation_results['has_config'] = bool(self._config_data)
            validation_results['is_dict'] = isinstance(self._config_data, dict)
            
            # 验证关键配置键
            required_keys = ['files', 'schedule_settings', 'upload_settings']
            for key in required_keys:
                validation_results[f'has_{key}'] = key in self._config_data
            
            # 计算总体验证结果
            validation_results['overall_valid'] = all(validation_results.values())
            
        except Exception as e:
            self.debug_log("验证配置时出错: {}", e)
            validation_results['error'] = str(e)
            validation_results['overall_valid'] = False
        
        return validation_results 