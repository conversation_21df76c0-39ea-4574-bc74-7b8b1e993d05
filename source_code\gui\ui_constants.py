"""
UI常量定义模块
统一管理所有UI相关的常量，包括尺寸、间距、颜色等
"""

from typing import Dict, Any
from PyQt6.QtCore import QSize

# =============================================================================
# 尺寸常量
# =============================================================================

class Sizes:
    """UI尺寸常量"""
    
    # 窗口尺寸 - 优化为适应不同分辨率和DPI
    WINDOW_MIN_WIDTH = 900
    WINDOW_MIN_HEIGHT = 650
    WINDOW_DEFAULT_WIDTH = 1100
    WINDOW_DEFAULT_HEIGHT = 750
    
    # 控件最小尺寸 - 增加宽度以适应中文文字
    BUTTON_MIN_HEIGHT = 32
    BUTTON_MIN_WIDTH = 100  # 增加基础按钮宽度
    INPUT_MIN_HEIGHT = 32
    INPUT_MIN_WIDTH = 120

    # 特殊控件尺寸
    ACTION_BUTTON_HEIGHT = 40  # 增加按钮高度
    ACTION_BUTTON_MIN_WIDTH = 120  # 增加动作按钮宽度
    PRIMARY_BUTTON_MIN_WIDTH = 160  # 增加主要按钮宽度
    
    # 图标尺寸
    ICON_SMALL = QSize(16, 16)
    ICON_MEDIUM = QSize(24, 24)
    ICON_LARGE = QSize(32, 32)
    ICON_XLARGE = QSize(48, 48)
    
    # 滚动区域
    FILE_SCROLL_MIN_HEIGHT = 300
    LOG_TEXT_MIN_HEIGHT = 400
    
    # 分组框最小高度
    SCHEDULE_GROUP_MIN_HEIGHT = 100
    UPLOAD_GROUP_MIN_HEIGHT = 140
    GAMECLIENT_GROUP_MIN_HEIGHT = 120


# =============================================================================
# 间距常量
# =============================================================================

class Spacing:
    """UI间距常量"""
    
    # 基础间距
    TINY = 4
    SMALL = 8
    MEDIUM = 12
    LARGE = 15
    XLARGE = 20
    XXLARGE = 25
    
    # 边距
    MARGIN_SMALL = 10
    MARGIN_MEDIUM = 15
    MARGIN_LARGE = 20
    
    # 内容边距
    CONTENT_MARGIN = (15, 20, 15, 20)  # left, top, right, bottom
    BUTTON_MARGIN = (15, 15, 15, 15)
    
    # 布局间距
    LAYOUT_SPACING = 15
    SECTION_SPACING = 12
    WIDGET_SPACING = 8


# =============================================================================
# 字体常量
# =============================================================================

class Fonts:
    """字体常量"""
    
    # 字体族
    DEFAULT_FAMILY = "Arial"
    MONOSPACE_FAMILY = "Consolas"
    
    # 字体大小
    TINY = 8
    SMALL = 9
    NORMAL = 10
    MEDIUM = 12
    LARGE = 14
    XLARGE = 16
    XXLARGE = 18
    
    # 特殊用途字体大小
    LOG_SIZE = 9
    TITLE_SIZE = 16
    SUBTITLE_SIZE = 14
    CAPTION_SIZE = 12


# =============================================================================
# 颜色扩展
# =============================================================================

class Colors:
    """扩展的颜色系统"""
    
    # 基础颜色
    PRIMARY = "#007AFF"
    PRIMARY_HOVER = "#0056CC"
    PRIMARY_LIGHT = "#E3F2FD"
    
    SECONDARY = "#6C757D"
    SECONDARY_HOVER = "#545B62"
    SECONDARY_LIGHT = "#F8F9FA"
    
    SUCCESS = "#28A745"
    SUCCESS_HOVER = "#218838"
    SUCCESS_LIGHT = "#D4EDDA"
    
    WARNING = "#FFC107"
    WARNING_HOVER = "#E0A800"
    WARNING_LIGHT = "#FFF3CD"
    
    DANGER = "#DC3545"
    DANGER_HOVER = "#C82333"
    DANGER_LIGHT = "#F8D7DA"
    
    INFO = "#17A2B8"
    INFO_HOVER = "#138496"
    INFO_LIGHT = "#D1ECF1"
    
    # 中性颜色
    WHITE = "#FFFFFF"
    LIGHT_GRAY = "#F8F9FA"
    GRAY = "#6C757D"
    DARK_GRAY = "#343A40"
    BLACK = "#000000"
    
    # 边框颜色
    BORDER_LIGHT = "#E9ECEF"
    BORDER_MEDIUM = "#DEE2E6"
    BORDER_DARK = "#ADB5BD"
    
    # 背景颜色
    BACKGROUND_PRIMARY = "#FFFFFF"
    BACKGROUND_SECONDARY = "#F8F9FA"
    BACKGROUND_TERTIARY = "#E9ECEF"
    
    # 文本颜色
    TEXT_PRIMARY = "#212529"
    TEXT_SECONDARY = "#6C757D"
    TEXT_MUTED = "#ADB5BD"
    TEXT_WHITE = "#FFFFFF"
    
    # 状态颜色
    STATUS_ONLINE = "#28A745"
    STATUS_OFFLINE = "#DC3545"
    STATUS_PENDING = "#FFC107"
    STATUS_UNKNOWN = "#6C757D"


# =============================================================================
# 动画常量
# =============================================================================

class Animation:
    """动画相关常量"""
    
    # 动画持续时间（毫秒）
    FAST = 150
    NORMAL = 250
    SLOW = 400
    
    # 缓动类型
    EASE_IN = "ease-in"
    EASE_OUT = "ease-out"
    EASE_IN_OUT = "ease-in-out"
    LINEAR = "linear"


# =============================================================================
# 样式类名
# =============================================================================

class StyleClass:
    """CSS类名常量"""
    
    # 按钮类
    BTN_PRIMARY = "btn-primary"
    BTN_SECONDARY = "btn-secondary"
    BTN_SUCCESS = "btn-success"
    BTN_WARNING = "btn-warning"
    BTN_DANGER = "btn-danger"
    BTN_INFO = "btn-info"
    
    # 文本类
    TEXT_TITLE = "text-title"
    TEXT_SUBTITLE = "text-subtitle"
    TEXT_CAPTION = "text-caption"
    TEXT_MUTED = "text-muted"
    
    # 状态类
    STATUS_SUCCESS = "status-success"
    STATUS_WARNING = "status-warning"
    STATUS_ERROR = "status-error"
    STATUS_INFO = "status-info"


# =============================================================================
# 布局配置
# =============================================================================

class Layout:
    """布局配置常量"""
    
    # 分割器比例
    SPLITTER_FILE_LOG_RATIO = [60, 40]  # 文件区域60%，日志区域40%
    
    # 网格布局
    GRID_SPACING = 10
    GRID_MARGIN = 15
    
    # 表单布局
    FORM_LABEL_WIDTH = 80
    FORM_SPACING = 10
    
    # 工具栏
    TOOLBAR_HEIGHT = 40
    TOOLBAR_SPACING = 5


# =============================================================================
# 消息常量
# =============================================================================

class Messages:
    """用户界面消息常量"""
    
    # 状态消息
    STATUS_READY = "就绪"
    STATUS_CHECKING = "正在检查文件..."
    STATUS_UPLOADING = "正在上传..."
    STATUS_COMPLETED = "操作完成"
    STATUS_CANCELLED = "操作已取消"
    
    # 警告消息
    WARNING_NO_FILES = "请选择至少一个文件进行检查"
    WARNING_INVALID_TIME = "请输入有效的时间格式 (HH:MM)"
    WARNING_INVALID_URL = "请输入有效的上传地址"
    
    # 确认消息
    CONFIRM_CLEAR_FILES = "确定要清空文件列表吗？"
    CONFIRM_EXIT = "确定要退出程序吗？"
    CONFIRM_CANCEL_OPERATION = "确定要取消当前操作吗？"
    
    # 成功消息
    SUCCESS_FILES_ADDED = "文件添加成功"
    SUCCESS_SETTINGS_SAVED = "设置保存成功"
    SUCCESS_UPLOAD_COMPLETED = "上传完成"
    
    # 错误消息
    ERROR_FILE_NOT_FOUND = "文件不存在"
    ERROR_NETWORK_FAILED = "网络连接失败"
    ERROR_PERMISSION_DENIED = "权限不足"


# =============================================================================
# 工具函数
# =============================================================================

def get_button_size(size_type: str = "normal") -> QSize:
    """获取按钮尺寸"""
    size_map = {
        "small": QSize(Sizes.BUTTON_MIN_WIDTH, Sizes.BUTTON_MIN_HEIGHT),
        "normal": QSize(Sizes.ACTION_BUTTON_MIN_WIDTH, Sizes.ACTION_BUTTON_HEIGHT),
        "large": QSize(Sizes.PRIMARY_BUTTON_MIN_WIDTH, Sizes.ACTION_BUTTON_HEIGHT),
    }
    return size_map.get(size_type, size_map["normal"])


def get_spacing(size_type: str = "medium") -> int:
    """获取间距值"""
    spacing_map = {
        "tiny": Spacing.TINY,
        "small": Spacing.SMALL,
        "medium": Spacing.MEDIUM,
        "large": Spacing.LARGE,
        "xlarge": Spacing.XLARGE,
        "xxlarge": Spacing.XXLARGE,
    }
    return spacing_map.get(size_type, spacing_map["medium"])


def get_color_scheme() -> Dict[str, str]:
    """获取完整的颜色方案"""
    return {
        'primary': Colors.PRIMARY,
        'primary_hover': Colors.PRIMARY_HOVER,
        'secondary': Colors.SECONDARY,
        'success': Colors.SUCCESS,
        'warning': Colors.WARNING,
        'danger': Colors.DANGER,
        'info': Colors.INFO,
        'background': Colors.BACKGROUND_PRIMARY,
        'surface': Colors.BACKGROUND_SECONDARY,
        'border': Colors.BORDER_MEDIUM,
        'text': Colors.TEXT_PRIMARY,
        'text_secondary': Colors.TEXT_SECONDARY,
        'disabled': Colors.TEXT_MUTED,
    }
