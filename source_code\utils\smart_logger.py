"""
智能日志系统
优化DEBUG日志性能，避免生产环境中不必要的字符串格式化开销
"""

import os
import functools
from typing import Any, Callable


class SmartLogger:
    """智能日志器，自动检测是否应该记录DEBUG日志"""
    
    def __init__(self, app_instance=None):
        self.app = app_instance
        self._debug_enabled = self._check_debug_mode()
    
    def _check_debug_mode(self) -> bool:
        """检查是否启用DEBUG模式"""
        # 检查环境变量
        env_debug = os.getenv('DEBUG', '').lower() in ('true', '1', 'yes', 'on')
        
        # 检查应用实例的debug模式
        app_debug = False
        if self.app and hasattr(self.app, 'debug_mode'):
            app_debug = self.app.debug_mode
        
        return env_debug or app_debug
    
    @property
    def debug_enabled(self) -> bool:
        """DEBUG模式是否启用"""
        return self._debug_enabled
    
    def debug(self, message_func: Callable[[], str], *args, **kwargs):
        """
        智能DEBUG日志方法
        
        Args:
            message_func: 返回日志消息的函数，只在DEBUG模式下才会调用
            *args, **kwargs: 传递给app.log的额外参数
        """
        if self._debug_enabled and self.app and hasattr(self.app, 'log'):
            try:
                message = message_func() if callable(message_func) else str(message_func)
                self.app.log(message, "DEBUG", *args, **kwargs)
            except Exception as e:
                # 防止日志记录本身出错影响主程序
                print(f"Debug logging error: {e}")
    
    def log_if_debug(self, message: str, *args, **kwargs):
        """
        条件DEBUG日志（简化版本）
        
        Args:
            message: 日志消息
            *args, **kwargs: 传递给app.log的额外参数
        """
        if self._debug_enabled and self.app and hasattr(self.app, 'log'):
            self.app.log(message, "DEBUG", *args, **kwargs)


def debug_logger(app_instance):
    """创建智能DEBUG日志装饰器"""
    logger = SmartLogger(app_instance)
    
    def debug_log(message_func: Callable[[], str]):
        """
        智能DEBUG日志装饰器
        
        使用方法：
        @debug_log(lambda: f"Processing file: {file_name}")
        def some_function():
            pass
        """
        if logger.debug_enabled:
            try:
                message = message_func() if callable(message_func) else str(message_func)
                app_instance.log(message, "DEBUG")
            except Exception:
                pass  # 静默处理日志错误
    
    return debug_log


def smart_debug_log(app_instance, message_template: str, *format_args, **format_kwargs):
    """
    智能DEBUG日志函数
    只在DEBUG模式启用时才进行字符串格式化
    
    Args:
        app_instance: 应用实例
        message_template: 消息模板
        *format_args: 格式化参数
        **format_kwargs: 格式化关键字参数
    """
    # 快速检查DEBUG模式
    debug_enabled = False
    
    # 检查环境变量（最快的检查）
    if os.getenv('DEBUG', '').lower() in ('true', '1', 'yes', 'on'):
        debug_enabled = True
    # 检查应用实例
    elif app_instance and hasattr(app_instance, 'debug_mode') and app_instance.debug_mode:
        debug_enabled = True
    
    if debug_enabled and app_instance and hasattr(app_instance, 'log'):
        try:
            # 只在确实需要时才进行字符串格式化
            formatted_message = message_template.format(*format_args, **format_kwargs)
            app_instance.log(formatted_message, "DEBUG")
        except Exception:
            # 静默处理格式化错误
            pass


def create_debug_logger_for_class(cls):
    """为类创建智能DEBUG日志方法"""
    
    def should_log_debug(self) -> bool:
        """检查是否应该记录DEBUG日志"""
        # 检查环境变量
        if os.getenv('DEBUG', '').lower() in ('true', '1', 'yes', 'on'):
            return True
        
        # 检查应用实例
        if hasattr(self, 'app') and hasattr(self.app, 'debug_mode'):
            return self.app.debug_mode
        
        # 检查自身的debug_mode属性
        if hasattr(self, 'debug_mode'):
            return self.debug_mode
        
        return False
    
    def debug_log(self, message_template: str, *format_args, **format_kwargs):
        """智能DEBUG日志方法"""
        if self.should_log_debug():
            try:
                formatted_message = message_template.format(*format_args, **format_kwargs)
                
                # 尝试不同的日志方法
                if hasattr(self, 'log'):
                    self.log(formatted_message, "DEBUG")
                elif hasattr(self, '_log'):
                    self._log(formatted_message, "DEBUG")
                elif hasattr(self, 'app') and hasattr(self.app, 'log'):
                    self.app.log(formatted_message, "DEBUG")
                else:
                    print(f"[DEBUG] {formatted_message}")
            except Exception:
                pass  # 静默处理错误
    
    # 将方法添加到类中
    cls.should_log_debug = should_log_debug
    cls.debug_log = debug_log
    
    return cls


# 便捷的模块级函数
_global_debug_enabled = None

def is_debug_enabled() -> bool:
    """全局检查DEBUG模式是否启用（缓存结果以提高性能）"""
    global _global_debug_enabled
    
    if _global_debug_enabled is None:
        _global_debug_enabled = os.getenv('DEBUG', '').lower() in ('true', '1', 'yes', 'on')
    
    return _global_debug_enabled


def reset_debug_cache():
    """重置DEBUG模式缓存（在DEBUG环境变量改变时调用）"""
    global _global_debug_enabled
    _global_debug_enabled = None


# 高性能的条件DEBUG日志宏
def DEBUG_LOG(app_instance, message_template: str, *format_args, **format_kwargs):
    """
    高性能条件DEBUG日志宏
    
    使用方法：
    DEBUG_LOG(self.app, "Processing {}: {}", file_name, status)
    """
    if is_debug_enabled() and app_instance and hasattr(app_instance, 'log'):
        try:
            formatted_message = message_template.format(*format_args, **format_kwargs)
            app_instance.log(formatted_message, "DEBUG")
        except Exception:
            pass 