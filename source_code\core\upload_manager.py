"""
上传管理器模块
负责文件上传、上传设置、定时上传等功能
从 app_pyqt6.py 中提取的上传相关功能
"""

import os
import threading
from typing import List, Dict, Any, Optional
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QGroupBox, 
    QLineEdit, QMessageBox
)
from PyQt6.QtCore import Qt, QTimer
from concurrent.futures import ThreadPoolExecutor

from gui.ui_factory import UIFactory
from gui.time_input_widget import TimeInputWidget
from gui.ui_constants import Sizes
from core.constants import DEFAULT_UPLOAD_URL


class UploadManager:
    """上传管理器类 - 负责所有上传相关功能"""
    
    def __init__(self, parent_app):
        """
        初始化上传管理器
        
        Args:
            parent_app: 主应用程序实例
        """
        self.app = parent_app
        
        # 上传设置状态
        self.upload_enabled = False
        self.upload_url = ""
        self.upload_time_hour = "03"
        self.upload_time_minute = "00"
        
        # UI组件引用
        self.upload_checkbox = None
        self.upload_time_input = None
        self.url_input = None
        self.test_upload_button = None
        
        # 内部定时器
        self._upload_time_change_timer = None
    
    def create_upload_section(self, main_layout):
        """创建上传功能区域"""
        upload_group = QGroupBox("上传功能设置")
        upload_group.setMinimumHeight(140)  # 设置最小高度而不是最大高度
        upload_layout = QVBoxLayout(upload_group)
        upload_layout.setSpacing(12)  # 增加行间距
        upload_layout.setContentsMargins(15, 20, 15, 20)  # 增加上下边距

        # First row: Enable checkbox, time setting and buttons
        first_row = QHBoxLayout()
        first_row.setSpacing(15)  # 控件间距

        self.upload_checkbox = UIFactory.create_checkbox(
            text="启用自动上传",
            checked=False,
            callback=self.toggle_upload,
            tooltip="启用后将在指定时间自动上传文件"
        )
        first_row.addWidget(self.upload_checkbox)

        # Upload time setting
        upload_time_label = UIFactory.create_label(
            text="上传时间:",
            alignment=Qt.AlignmentFlag.AlignVCenter
        )
        first_row.addWidget(upload_time_label)

        self.upload_time_input = TimeInputWidget(default_time="03:00")
        self.upload_time_input.setEnabled(False)
        self.upload_time_input.setToolTip("设置自动上传的时间（24小时制）")
        self.upload_time_input.timeChanged.connect(self.on_upload_time_changed)
        first_row.addWidget(self.upload_time_input)

        # Upload buttons in the same row
        self.test_upload_button = UIFactory.create_button(
            text="测试上传",
            style_type="default",
            size_type="small",
            callback=self.test_upload,
            enabled=False,
            tooltip="测试上传连接是否正常"
        )
        first_row.addWidget(self.test_upload_button)

        upload_now_button = UIFactory.create_button(
            text="立即上传",
            style_type="warning",
            size_type="small",
            callback=self.upload_now,
            tooltip="立即执行上传操作"
        )
        first_row.addWidget(upload_now_button)

        # 添加弹性空间，但限制其扩展
        first_row.addStretch(1)
        upload_layout.addLayout(first_row)

        # Second row: URL input (more compact)
        url_layout = QHBoxLayout()
        url_layout.setSpacing(15)

        url_label = QLabel("上传地址:")
        url_label.setMinimumWidth(70)  # 固定标签宽度
        url_layout.addWidget(url_label)

        self.url_input = QLineEdit()
        self.url_input.setPlaceholderText("请输入上传服务器地址...")
        self.url_input.setEnabled(False)
        self.url_input.setMinimumHeight(30)  # 设置最小高度而不是最大高度

        # 设置默认上传地址
        self.url_input.setText(DEFAULT_UPLOAD_URL)

        url_layout.addWidget(self.url_input)
        upload_layout.addLayout(url_layout)

        main_layout.addWidget(upload_group)
    
    def toggle_upload(self, enabled: bool):
        """切换上传功能状态"""
        self.upload_enabled = enabled
        self.url_input.setEnabled(enabled)
        self.upload_time_input.setEnabled(enabled)
        self.test_upload_button.setEnabled(enabled)

        if enabled:
            self.apply_upload_settings()
        else:
            self.app.scheduler_service.clear_jobs_by_tag('upload_job')

        self.app.log(f"自动上传已{'启用' if enabled else '禁用'}")

    def on_upload_time_changed(self, time_str):
        """上传时间输入变化处理"""
        if self.upload_enabled:
            # 延迟应用设置，避免频繁触发
            if hasattr(self, '_upload_time_change_timer') and self._upload_time_change_timer:
                self._upload_time_change_timer.stop()

            self._upload_time_change_timer = QTimer()
            self._upload_time_change_timer.setSingleShot(True)
            self._upload_time_change_timer.timeout.connect(self.apply_upload_settings)
            self._upload_time_change_timer.start(1000)  # 1秒延迟

    def apply_upload_settings(self, *args):
        """应用上传设置"""
        try:
            # 获取上传URL
            upload_url = self.url_input.text().strip()
            
            # 获取上传时间
            upload_time = self.upload_time_input.get_time()

            # 验证时间格式
            if not self._validate_time_format(upload_time):
                self.app.log(f"上传时间格式无效: {upload_time}，请使用 HH:MM 格式", "ERROR")
                return

            # 检查是否有实际变化，避免重复设置
            current_enabled = getattr(self, 'upload_enabled', False)
            current_url = getattr(self, 'upload_url', "")
            current_time = self.app.config.get_config_value("upload_time", "03:00")
            
            if (current_enabled == self.upload_enabled and 
                current_url == upload_url and 
                current_time == upload_time):
                # 没有变化，不需要重新设置
                return

            # 更新状态
            self.upload_url = upload_url

            if self.upload_enabled and upload_url:
                # 清除旧任务
                self.app.scheduler_service.clear_jobs_by_tag('upload_job')

                # 添加新任务
                success = self.app.scheduler_service.add_daily_job(
                    upload_time, self.run_scheduled_upload, 'upload_job'
                )

                if success:
                    self.app.log(f"已更新每天 {upload_time} 自动上传到: {upload_url}", "SUCCESS")
                else:
                    self.app.log(f"设置上传任务失败", "ERROR")

            # 保存配置
            self.app.config.set_config_value("upload_enabled", self.upload_enabled)
            self.app.config.set_config_value("upload_url", upload_url)
            self.app.config.set_config_value("upload_time", upload_time)
            self.app.config.save_config()

        except Exception as e:
            self.app.log(f"应用上传设置时出错: {e}", "ERROR")

    def _validate_time_format(self, time_str: str) -> bool:
        """验证时间格式 HH:MM"""
        try:
            parts = time_str.split(':')
            if len(parts) != 2:
                return False

            # 检查格式是否为两位数
            if len(parts[0]) != 2 or len(parts[1]) != 2:
                return False

            hour = int(parts[0])
            minute = int(parts[1])

            return 0 <= hour <= 23 and 0 <= minute <= 59
        except (ValueError, IndexError):
            return False

    def test_upload(self):
        """测试上传连接"""
        upload_url = self.url_input.text().strip()
        if not upload_url:
            QMessageBox.warning(self.app, "警告", "请先输入上传地址")
            return

        self.app.log(f"正在测试上传连接: {upload_url}", "INFO")

        # 使用PyQt6网络服务测试连接
        try:
            # 使用网络服务进行连接测试
            result = self.app.network_service.test_connection(upload_url)
            if result:
                self.app.log("上传连接测试成功", "SUCCESS")
                QMessageBox.information(self.app, "测试成功", "上传服务器连接正常")
            else:
                self.app.log("上传连接测试失败", "ERROR")
                QMessageBox.warning(self.app, "测试失败", "无法连接到上传服务器")
        except Exception as e:
            self.app.log(f"上传连接测试失败: {e}", "ERROR")
            QMessageBox.critical(self.app, "测试失败", f"连接失败: {e}")

    def upload_now(self):
        """手动触发上传（与原版兼容的实现）"""
        self.app.log("手动触发上传...", "INFO")

        if not self.upload_enabled:
            self.app.log("手动上传失败：定时上传功能未启用。", "WARNING")
            QMessageBox.information(self.app, "上传未启用", '请先在"定时上传设置"中启用上传功能。')
            return

        upload_url = self.url_input.text().strip()
        if not upload_url:
            self.app.log("手动上传失败：未设置上传 URL。", "ERROR")
            QMessageBox.critical(self.app, "URL错误", '请先在"定时上传设置"中输入有效的上传 URL。')
            return

        # 获取目录列表（与原版一致）
        selected_files = self.app.file_manager.get_selected_files()
        if not selected_files:
            # 如果没有选中文件，使用所有文件
            selected_files = [item["path"] for item in self.app.file_manager.file_checkboxes]

        if not selected_files:
            self.app.log("手动上传失败：没有可用的文件。", "WARNING")
            QMessageBox.information(self.app, "无文件", '请先添加要上传的文件。')
            return

        # 获取唯一目录列表
        unique_directories = set()
        for file_path in selected_files:
            try:
                directory = os.path.dirname(file_path)
                unique_directories.add(directory)
            except Exception as e:
                self.app.log(f"获取目录时出错 (文件: {file_path}): {e}", "ERROR")

        directories_to_upload = list(unique_directories)
        if not directories_to_upload:
            self.app.log("手动上传失败：没有找到可上传的目录。", "WARNING")
            QMessageBox.information(self.app, "无目录", '没有找到可上传的目录。')
            return

        self.app.log(f"手动上传检查通过，开始执行上传任务... (目录数: {len(directories_to_upload)})", "INFO")

        # 启动独立的上传线程
        upload_thread = self.app.thread_manager.start_thread(
            target=self._upload_directories_thread,
            args=(directories_to_upload, upload_url),
            callback=self.upload_completed
        )

    def run_scheduled_upload(self):
        """运行定时上传（与原版兼容的实现）"""
        self.app.log("执行定时上传任务", "INFO")

        if not self.upload_enabled:
            self.app.log("[Upload Task] 定时上传任务被触发，但功能已禁用。", "INFO")
            return

        upload_url = self.upload_url
        if not upload_url:
            self.app.log("[Upload Task] 定时上传任务被触发，但未设置 URL。", "WARNING")
            return

        # 动态获取目录列表（与原版一致）
        current_file_paths = [item["path"] for item in self.app.file_manager.file_checkboxes]
        if not current_file_paths:
            self.app.log("[Upload Task] 没有可用的文件", "WARNING")
            return

        # 获取唯一目录列表
        unique_directories = set()
        for file_path in current_file_paths:
            try:
                directory = os.path.dirname(file_path)
                unique_directories.add(directory)
            except Exception as e:
                self.app.log(f"[Upload Task] 获取目录时出错 (文件: {file_path}): {e}", "ERROR")

        directories_to_upload = list(unique_directories)
        if not directories_to_upload:
            self.app.log("[Upload Task] 没有找到可上传的目录", "WARNING")
            return

        self.app.log(f"[Upload Task] 找到 {len(directories_to_upload)} 个目录需要上传", "INFO")

        # 启动独立的上传线程（避免与检查任务冲突）
        upload_thread = self.app.thread_manager.start_thread(
            target=self._upload_directories_thread,
            args=(directories_to_upload, upload_url),
            callback=self.upload_completed
        )

    def _upload_directories_thread(self, directories, upload_url):
        """上传目录的线程函数（与原版兼容的实现）"""
        results = []
        successful_uploads = 0
        failed_uploads = 0
        max_workers = min(len(directories), 3)  # 限制并发数

        self.app.log(f"[Upload Task] 开始上传 {len(directories)} 个目录，使用 {max_workers} 个工作线程", "INFO")

        try:
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                futures = []
                for directory in directories:
                    future = executor.submit(self.app.network_service.upload_directory_files, upload_url, directory)
                    futures.append((future, directory))

                for future, directory in futures:
                    try:
                        success = future.result()
                        results.append({
                            "directory": directory,
                            "success": success
                        })

                        if success:
                            successful_uploads += 1
                            self.app.log(f"[Upload Task] 目录 {directory} 上传成功", "SUCCESS")
                        else:
                            failed_uploads += 1
                            self.app.log(f"[Upload Task] 目录 {directory} 上传失败", "ERROR")
                    except Exception as exc:
                        self.app.log(f'[Upload Task] 目录 {directory} 的上传线程生成了异常: {exc}', 'ERROR')
                        failed_uploads += 1
                        results.append({
                            "directory": directory,
                            "success": False,
                            "error": str(exc)
                        })

            self.app.log(f"[Upload Task] 定时上传任务完成。成功: {successful_uploads}, 失败/跳过: {failed_uploads}", "INFO")

        except Exception as e:
            self.app.log(f"[Upload Task] 线程池执行上传任务时出错: {e}", "ERROR")

        return results

    def _upload_files_thread(self, file_paths, upload_url):
        """上传文件的线程函数（兼容性方法）"""
        results = []

        for file_path in file_paths:
            try:
                success = self.app.network_service.upload_file(upload_url, file_path)
                results.append({
                    "file": file_path,
                    "success": success
                })

                if success:
                    self.app.log(f"成功上传: {os.path.basename(file_path)}", "SUCCESS")
                else:
                    self.app.log(f"上传失败: {os.path.basename(file_path)}", "ERROR")

            except Exception as e:
                self.app.log(f"上传 {os.path.basename(file_path)} 时出错: {e}", "ERROR")
                results.append({
                    "file": file_path,
                    "success": False,
                    "error": str(e)
                })

        return results

    def upload_completed(self, results):
        """上传完成回调"""
        if results:
            success_count = sum(1 for r in results if r.get("success", False))
            self.app.log(f"上传完成，成功: {success_count}/{len(results)}", "SUCCESS")
        else:
            self.app.log("上传完成，但没有结果", "WARNING")

    def restore_ui_state(self):
        """恢复上传UI状态"""
        try:
            # 恢复上传时间设置
            if hasattr(self, 'upload_time_input') and self.upload_time_input:
                upload_time = self.app.config.get_config_value("upload_time", "03:00")
                self.upload_time_input.set_time(upload_time)
                self.app.log(f"恢复上传时间: {upload_time}", "INFO")

            if hasattr(self, 'url_input') and self.url_input:
                self.url_input.setText(self.upload_url)

            # 恢复上传复选框状态（避免重复设置）
            if hasattr(self, 'upload_checkbox') and self.upload_checkbox:
                # 暂时断开信号连接
                self.upload_checkbox.toggled.disconnect()
                self.upload_checkbox.setChecked(self.upload_enabled)
                # 重新连接信号
                self.upload_checkbox.toggled.connect(self.toggle_upload)

                # 手动设置UI状态
                self.upload_time_input.setEnabled(self.upload_enabled)
                self.url_input.setEnabled(self.upload_enabled)
                self.test_upload_button.setEnabled(self.upload_enabled)
                
                if self.upload_enabled and self.upload_url:
                    # 只在启用时设置上传任务
                    upload_time = self.app.config.get_config_value("upload_time", "03:00")
                    self.app.scheduler_service.clear_jobs_by_tag('upload_job')
                    success = self.app.scheduler_service.add_daily_job(
                        upload_time, self.run_scheduled_upload, 'upload_job'
                    )
                    if success:
                        self.app.log(f"✅ 已设置每天 {upload_time} 自动上传到: {self.upload_url}", "SUCCESS")

            self.app.log("上传UI状态恢复完成", "INFO")

        except Exception as e:
            self.app.log(f"恢复上传UI状态时出错: {e}", "ERROR")

    def load_settings_from_config(self):
        """从配置加载上传设置"""
        try:
            # 恢复上传设置
            self.upload_enabled = self.app.config.get_config_value("upload_enabled", False)
            self.upload_url = self.app.config.get_config_value("upload_url", DEFAULT_UPLOAD_URL)
            self.upload_time_hour = self.app.config.get_config_value("upload_time_hour", "03")
            self.upload_time_minute = self.app.config.get_config_value("upload_time_minute", "00")
            
            self.app.log("上传设置加载完成", "INFO")
            
        except Exception as e:
            self.app.log(f"加载上传设置时出错: {e}", "ERROR")