"""
定时任务管理器模块
负责定时检查、时间同步、GameClient控制等定时任务功能
从 app_pyqt6.py 中提取的定时任务相关功能
"""

import os
import time
import threading
from typing import List, Dict, Any, Optional
from PyQt6.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QLabel, QGroupBox, QMessageBox
from PyQt6.QtCore import Qt, QTimer, QTime
from PyQt6.QtGui import QFont
from concurrent.futures import ThreadPoolExecutor, as_completed

from gui.ui_factory import UIFactory
from gui.time_input_widget import TimeInputWidget
from gui.ui_constants import Sizes, Spacing
from utils.pyqt6_threading import SafeUIUpdater


class ScheduleManager:
    """定时任务管理器类 - 负责所有定时任务相关功能"""
    
    def __init__(self, parent_app):
        """
        初始化定时任务管理器
        
        Args:
            parent_app: 主应用程序实例
        """
        self.app = parent_app
        
        # 定时任务状态
        self.schedule_enabled = False
        self.schedule_time = QTime(6, 0)  # Default 6:00 AM
        self.auto_check_enabled = False
        self.auto_check_time = "08:00"
        self.auto_sync_time_enabled = False
        
        # UI组件引用
        self.schedule_checkbox = None
        self.time_input = None
        self.run_now_button = None
        
        # 内部定时器
        self._time_change_timer = None
    
    def create_schedule_section(self, main_layout):
        """创建定时任务区域"""
        schedule_group, schedule_main_layout = UIFactory.create_group_box(
            title="定时任务设置",
            min_height=Sizes.SCHEDULE_GROUP_MIN_HEIGHT,
            layout_type="horizontal"
        )

        # Enable schedule checkbox
        self.schedule_checkbox = UIFactory.create_checkbox(
            text="启用定时检查",
            checked=False,
            callback=self.toggle_schedule,
            tooltip="启用后将在指定时间自动执行检查"
        )
        schedule_main_layout.addWidget(self.schedule_checkbox)

        # Time setting label
        time_label = UIFactory.create_label(
            text="检查时间:",
            alignment=Qt.AlignmentFlag.AlignVCenter
        )
        schedule_main_layout.addWidget(time_label)

        # Time input (HH:MM format with fixed colon)
        self.time_input = TimeInputWidget(default_time="08:00")
        self.time_input.setEnabled(False)
        self.time_input.setToolTip("设置定时检查的时间（24小时制）")
        self.time_input.timeChanged.connect(self.on_time_input_changed)
        schedule_main_layout.addWidget(self.time_input)

        # Immediate execution button
        self.run_now_button = UIFactory.create_button(
            text="立即执行",
            style_type="primary",
            size_type="small",
            min_width=Sizes.BUTTON_MIN_WIDTH,
            min_height=Sizes.BUTTON_MIN_HEIGHT,
            callback=self._safe_run_immediate_check,
            tooltip="按顺序执行：1)同步系统时间 2)检查文件 3)控制GameClient"
        )
        schedule_main_layout.addWidget(self.run_now_button)

        # Sync time button
        sync_button = UIFactory.create_button(
            text="同步系统时间",
            style_type="warning",
            size_type="small",
            min_width=120,
            min_height=Sizes.BUTTON_MIN_HEIGHT,
            callback=self.sync_system_time,
            tooltip="将时间设置同步为当前系统时间"
        )
        schedule_main_layout.addWidget(sync_button)

        # 添加弹性空间
        schedule_main_layout.addStretch(1)

        main_layout.addWidget(schedule_group)
    
    def toggle_schedule(self, enabled: bool):
        """切换定时任务状态"""
        self.schedule_enabled = enabled
        self.auto_check_enabled = enabled
        self.time_input.setEnabled(enabled)

        if enabled:
            self.apply_schedule_settings()
        else:
            self.app.scheduler_service.clear_jobs_by_tag('main_check_job')

        self.app.log(f"定时任务已{'启用' if enabled else '禁用'}")

    def on_time_input_changed(self, time_str):
        """时间输入变化处理"""
        if self.auto_check_enabled:
            # 延迟应用设置，避免频繁触发
            if hasattr(self, '_time_change_timer') and self._time_change_timer:
                self._time_change_timer.stop()

            self._time_change_timer = QTimer()
            self._time_change_timer.setSingleShot(True)
            self._time_change_timer.timeout.connect(self.apply_schedule_settings)
            self._time_change_timer.start(1000)  # 1秒延迟

    def apply_schedule_settings(self, *args):
        """应用定时设置"""
        try:
            # 获取时间字符串
            time_str = self.time_input.get_time()

            # 验证时间格式
            if not self.validate_time_format(time_str):
                self.app.log(f"定时时间格式无效: {time_str}，请使用 HH:MM 格式", "ERROR")
                return

            # 检查是否有实际变化，避免重复设置
            current_enabled = getattr(self, 'auto_check_enabled', False)
            current_time = getattr(self, 'auto_check_time', "08:00")
            
            if current_enabled == self.auto_check_enabled and current_time == time_str:
                # 没有变化，不需要重新设置
                return

            # 更新状态
            self.auto_check_time = time_str

            if self.auto_check_enabled:
                # 清除旧任务
                self.app.scheduler_service.clear_jobs_by_tag('main_check_job')

                # 添加新任务
                success = self.app.scheduler_service.add_daily_job(
                    time_str, self.run_scheduled_check, 'main_check_job'
                )

                if success:
                    self.app.log(f"已更新每天 {time_str} 自动执行检查", "SUCCESS")
                else:
                    self.app.log(f"设置定时任务失败", "ERROR")

            # 保存配置
            self.app.config.set_config_value("auto_check_enabled", self.auto_check_enabled)
            self.app.config.set_config_value("auto_check_time", time_str)
            self.app.config.save_config()

        except Exception as e:
            self.app.log(f"应用定时设置时出错: {e}", "ERROR")

    def validate_time_format(self, time_str: str) -> bool:
        """验证时间格式 HH:MM"""
        try:
            parts = time_str.split(':')
            if len(parts) != 2:
                return False

            # 检查格式是否为两位数
            if len(parts[0]) != 2 or len(parts[1]) != 2:
                return False

            hour = int(parts[0])
            minute = int(parts[1])

            return 0 <= hour <= 23 and 0 <= minute <= 59
        except (ValueError, IndexError):
            return False

    def sync_system_time(self):
        """同步系统时间"""
        self.app.log("开始同步系统时间...", "INFO")

        # 在线程中执行时间同步，避免阻塞UI
        self.app.current_thread = self.app.thread_manager.start_thread(
            target=self._sync_time_thread,
            callback=self._sync_time_completed
        )

    def _sync_time_thread(self):
        """时间同步线程函数"""
        try:
            from utils.system import sync_system_time
            return sync_system_time()
        except Exception as e:
            self.app.log(f"时间同步线程出错: {e}", "ERROR")
            return False

    def _sync_time_completed(self, success):
        """时间同步完成回调"""
        if success:
            self.app.log("系统时间同步成功", "SUCCESS")
        else:
            self.app.log("系统时间同步失败，请检查网络连接和管理员权限", "ERROR")

    def run_scheduled_check(self):
        """运行定时检查"""
        self.app.log("执行定时检查任务", "INFO")

        # 如果启用了自动同步时间，先执行时间同步
        if self.auto_sync_time_enabled:
            self.app.log("定时检查前先执行时间同步...", "INFO")
            self.sync_system_time()

        # 获取选中的文件
        selected_files = self.app.file_manager.get_selected_files()

        # 如果没有选中文件，自动选择所有文件
        if not selected_files and self.app.file_manager.file_checkboxes:
            for item in self.app.file_manager.file_checkboxes:
                item["checkbox"].setChecked(True)
            selected_files = self.app.file_manager.get_selected_files()

        if selected_files:
            self.app.start_check()
        else:
            self.app.log("没有可检查的文件", "WARNING")

    def run_immediate_check(self):
        """立即执行检查任务（原版方法，保持兼容性）"""
        self.app.log("手动触发立即检查...", "INFO")

        # 如果启用了自动同步时间，先执行时间同步
        if self.auto_sync_time_enabled:
            self.app.log("立即检查前先执行时间同步...", "INFO")
            self.sync_system_time()

        # 获取选中的文件
        selected_files = self.app.file_manager.get_selected_files()

        # 如果没有选中文件，自动选择所有文件
        if not selected_files and self.app.file_manager.file_checkboxes:
            for item in self.app.file_manager.file_checkboxes:
                item["checkbox"].setChecked(True)
            selected_files = self.app.file_manager.get_selected_files()

        if selected_files:
            self.app.start_check()
        else:
            self.app.log("没有可检查的文件", "WARNING")
            QMessageBox.warning(self.app, "警告", "没有可检查的文件，请先添加文件")

    def run_immediate_check_enhanced(self):
        """增强版立即执行：按顺序执行三个步骤"""
        if self.app.sequential_executor.is_execution_running():
            self.app.log("已有任务正在执行中，请等待完成", "WARNING")
            return

        # 检查是否有文件
        selected_files = self.app.file_manager.get_selected_files()
        if not selected_files and self.app.file_manager.file_checkboxes:
            # 自动选择所有文件
            for item in self.app.file_manager.file_checkboxes:
                item["checkbox"].setChecked(True)
            selected_files = self.app.file_manager.get_selected_files()

        if not selected_files:
            self.app.log("没有可检查的文件，请先添加文件", "WARNING")
            QMessageBox.warning(self.app, "警告", "没有可检查的文件，请先添加文件")
            return

        self.app.log("开始增强版立即执行：将按顺序执行三个步骤", "INFO")

        # 清空之前的步骤
        self.app.sequential_executor.clear_steps()

        # 添加步骤1：同步系统时间
        self.app.sequential_executor.add_step(
            name="sync_time",
            description="同步系统时间",
            func=self._step_sync_system_time
        )

        # 添加步骤2：执行主要检查功能
        self.app.sequential_executor.add_step(
            name="main_check",
            description="执行文件检查和更新",
            func=self._step_main_check,
            args=(selected_files,)
        )

        # 添加步骤3：控制GameClient功能
        self.app.sequential_executor.add_step(
            name="gameclient_control",
            description="控制GameClient功能",
            func=self._step_gameclient_control,
            args=(selected_files,)
        )

        # 更新UI状态
        self.run_now_button.setEnabled(False)
        self.run_now_button.setText("执行中...")

        # 开始执行
        success = self.app.sequential_executor.start_execution()
        if not success:
            self.app.log("启动顺序执行失败", "ERROR")
            self._reset_immediate_execution_ui()

    def _step_sync_system_time(self):
        """步骤1：同步系统时间"""
        try:
            self.app.log("步骤1：开始同步系统时间...", "INFO")

            # 使用系统工具模块的同步功能
            from utils.system import sync_system_time
            success = sync_system_time()

            if success:
                self.app.log("系统时间同步成功", "SUCCESS")
                return True
            else:
                self.app.log("系统时间同步失败，但继续执行后续步骤", "WARNING")
                return True  # 即使失败也继续执行

        except Exception as e:
            self.app.log(f"同步系统时间时出错: {e}", "ERROR")
            return True  # 即使出错也继续执行

    def _step_main_check(self, selected_files):
        """步骤2：执行主要检查功能"""
        try:
            self.app.log(f"步骤2：开始检查 {len(selected_files)} 个文件...", "INFO")

            # 设置取消事件
            self.app.cancel_event = threading.Event()
            self.app.file_service.set_cancel_event(self.app.cancel_event)

            # 直接执行文件处理逻辑，避免嵌套线程
            results = self._process_files_sync(selected_files)

            if results:
                success_count = sum(1 for r in results if r.get("success", False))
                self.app.log(f"文件检查完成，成功: {success_count}/{len(results)}", "SUCCESS")
                return True
            else:
                self.app.log("文件检查完成，但没有结果", "WARNING")
                return False

        except Exception as e:
            self.app.log(f"执行文件检查时出错: {e}", "ERROR")
            return False

    def _process_files_sync(self, selected_files):
        """同步处理文件（不创建新线程）"""
        total_files = len(selected_files)
        results = []

        try:
            # 检查文件处理服务是否可用
            if not hasattr(self.app, 'file_processing_service') or not self.app.file_processing_service:
                self.app.log("文件处理服务未初始化", "ERROR")
                return []

            # 使用线程池并行处理文件，但不创建新的主线程
            with ThreadPoolExecutor(max_workers=3) as executor:
                # 提交所有任务 - 修复：使用正确的文件处理服务方法
                future_to_file = {
                    executor.submit(self.app.file_processing_service._process_single_file, file_path): file_path
                    for file_path in selected_files
                }

                # 处理完成的任务
                for i, future in enumerate(as_completed(future_to_file)):
                    if self.app.cancel_event and self.app.cancel_event.is_set():
                        break

                    file_path = future_to_file[future]
                    try:
                        result = future.result()
                        results.append(result)

                        # 更新进度（使用线程安全的方式）
                        progress = (i + 1) / total_files
                        progress_percent = int(progress * 100)
                        # 简化进度更新，避免复杂的线程调用
                        try:
                            # 直接在这里记录进度，避免跨线程调用
                            self.app.log(f"处理进度: {progress_percent}%", "INFO")
                        except Exception as e:
                            print(f"更新进度时出错: {e}")

                    except Exception as e:
                        self.app.log(f"处理文件 {os.path.basename(file_path)} 时出错: {e}", "ERROR")
                        results.append({
                            "file": file_path,
                            "success": False,
                            "error": str(e)
                        })

        except Exception as e:
            self.app.log(f"处理过程中发生错误: {e}", "ERROR")

        return results

    def _step_gameclient_control(self, selected_files):
        """步骤3：控制GameClient功能"""
        try:
            self.app.log("步骤3：开始控制GameClient功能...", "INFO")

            # 为所有选中的文件根据配置执行GameClient控制
            return self._parallel_gameclient_control(selected_files)

        except Exception as e:
            self.app.log(f"执行GameClient控制时出错: {e}", "ERROR")
            return False

    def _parallel_gameclient_control(self, selected_files):
        """并行控制多个GameClient实例"""
        try:
            self.app.log(f"开始并行控制 {len(selected_files)} 个GameClient实例", "INFO")

            gc_controlled_count = 0
            total_files = len(selected_files)

            # 使用线程池并行处理多个GameClient控制
            with ThreadPoolExecutor(max_workers=min(total_files, 5)) as executor:
                # 提交所有GameClient控制任务
                future_to_file = {
                    executor.submit(self._single_gameclient_control_with_config, file_path): file_path
                    for file_path in selected_files
                }

                # 处理完成的任务
                for future in as_completed(future_to_file):
                    file_path = future_to_file[future]
                    try:
                        result = future.result()

                        if result and result.get('success', False):
                            gc_controlled_count += 1
                            action = result.get('action', '未知')
                            self.app.log(f"GameClient控制成功: {os.path.basename(file_path)} ({action})", "SUCCESS")
                        else:
                            reason = result.get('reason', '未知错误') if result else '未知错误'
                            self.app.log(f"GameClient控制失败: {os.path.basename(file_path)} - {reason}", "ERROR")

                    except Exception as e:
                        self.app.log(f"处理文件 {os.path.basename(file_path)} 的GameClient控制时出错: {e}", "ERROR")

            self.app.log(f"GameClient控制完成，成功: {gc_controlled_count}/{total_files}", "SUCCESS")
            return gc_controlled_count > 0

        except Exception as e:
            self.app.log(f"并行GameClient控制时出错: {e}", "ERROR")
            return False

    def _single_gameclient_control_with_config(self, file_path):
        """根据配置控制单个GameClient实例"""
        try:
            # 获取文件配置
            file_config = self.app.config.get_file_config(file_path)
            advanced_settings = file_config.get("advanced_settings", {})

            # 检查是否启用了GameClient定时任务
            if not advanced_settings.get("schedule_gameclient_enabled", False):
                return {
                    'success': False,
                    'reason': '未启用GameClient定时任务',
                    'action': '跳过'
                }

            # 获取配置的操作
            action = advanced_settings.get("schedule_gameclient_action", "打开")

            # 验证操作类型
            if action not in ["打开", "关闭", "重启"]:
                return {
                    'success': False,
                    'reason': f'无效的操作类型: {action}',
                    'action': action
                }

            self.app.log(f"执行GameClient控制: {os.path.basename(file_path)} -> {action}", "INFO")

            # 调用GameClient控制器
            success = self.app.gameclient_controller.control_gameclient(file_path, action)

            return {
                'success': success,
                'action': action,
                'reason': '操作完成' if success else '操作失败'
            }

        except Exception as e:
            self.app.log(f"控制单个GameClient时出错 ({os.path.basename(file_path)}): {e}", "ERROR")
            return {
                'success': False,
                'reason': f'异常: {e}',
                'action': '错误'
            }

    def _safe_run_immediate_check(self):
        """安全版立即执行检查"""
        try:
            # 应用崩溃修复
            from utils.crash_fix import apply_crash_fix
            apply_crash_fix(self.app)

            # 执行安全版本 - 修复：检查 self.app 而不是 self
            if hasattr(self.app, 'run_immediate_check_enhanced_safe'):
                self.app.log("✅ 安全版本已就绪，使用安全版本", "SUCCESS")
                self.app.run_immediate_check_enhanced_safe()
            else:
                self.app.log("⚠️ 安全版本未就绪，使用原版本", "WARNING")
                self.run_immediate_check_enhanced()

        except Exception as e:
            self.app.log(f"执行立即检查时出错: {e}", "ERROR")
            print(f"立即执行异常: {e}")

    def _reset_immediate_execution_ui(self):
        """重置立即执行UI状态"""
        self.run_now_button.setEnabled(True)
        self.run_now_button.setText("立即执行")

    def update_gameclient_schedule(self, file_path: str, advanced_settings: dict):
        """更新GameClient定时任务"""
        try:
            # 清除旧任务
            tag = f"gameclient_{os.path.normpath(file_path)}"
            self.app.scheduler_service.clear_jobs_by_tag(tag)

            # 检查是否启用
            enabled = advanced_settings.get("schedule_gameclient_enabled", False)
            if not enabled:
                self.app.log(f"文件 '{os.path.basename(file_path)}' 的GameClient定时任务已禁用", "INFO")
                return

            # 获取时间和动作
            time_str = advanced_settings.get("schedule_gameclient_time", "06:00")
            action = advanced_settings.get("schedule_gameclient_action", "打开")

            # 验证时间格式
            try:
                from datetime import datetime
                datetime.strptime(time_str, "%H:%M")
            except ValueError:
                self.app.log(f"文件 '{os.path.basename(file_path)}' 的GameClient定时时间格式无效: {time_str}", "ERROR")
                return

            # 验证动作
            if action not in ["打开", "关闭", "重启"]:
                self.app.log(f"文件 '{os.path.basename(file_path)}' 的GameClient动作无效: {action}", "ERROR")
                return

            # 添加定时任务
            def gameclient_job():
                self.app.log(f"执行GameClient定时任务: {action} (文件: {os.path.basename(file_path)})", "INFO")
                self.app.control_gameclient_threaded(file_path, action)

            success = self.app.scheduler_service.add_daily_job(time_str, gameclient_job, tag)

            if success:
                self.app.log(f"已为文件 '{os.path.basename(file_path)}' 设置GameClient定时任务: 每天 {time_str} {action}", "SUCCESS")
            else:
                self.app.log(f"为文件 '{os.path.basename(file_path)}' 设置GameClient定时任务失败", "ERROR")

        except Exception as e:
            self.app.log(f"更新GameClient定时任务时出错: {e}", "ERROR")

    def initialize_gameclient_schedules(self):
        """初始化所有文件的GameClient定时任务"""
        try:
            self.app.log("正在初始化GameClient定时任务...", "INFO")
            gc_tasks_added = 0
            existing_tasks = set()

            # 获取已存在的任务标签，避免重复
            existing_jobs = self.app.scheduler_service.get_jobs()
            for tag in existing_jobs.keys():
                if tag.startswith('gameclient_'):
                    existing_tasks.add(tag)

            # 获取所有文件配置
            all_file_configs = self.app.config.get_all_file_configs()

            for file_path, file_config in all_file_configs.items():
                if not os.path.exists(file_path):
                    self.app.log(f"跳过不存在的文件: {file_path}", "WARNING")
                    continue

                # 生成任务标签
                task_tag = f"gameclient_{os.path.normpath(file_path)}"
                
                # 检查任务是否已存在
                if task_tag in existing_tasks:
                    self.app.log(f"GameClient任务已存在，跳过: {os.path.basename(file_path)}", "DEBUG")
                    continue

                advanced_settings = file_config.get("advanced_settings", {})
                if advanced_settings.get("schedule_gameclient_enabled", False):
                    self.update_gameclient_schedule(file_path, advanced_settings)
                    gc_tasks_added += 1
                    existing_tasks.add(task_tag)

            self.app.log(f"✅ GameClient定时任务初始化完成，共添加 {gc_tasks_added} 个任务", "SUCCESS")

        except Exception as e:
            self.app.log(f"初始化GameClient定时任务时出错: {e}", "ERROR")

    def restore_ui_state(self):
        """恢复UI状态"""
        try:
            # 先恢复时间设置（避免被toggle方法覆盖）
            if hasattr(self, 'time_input') and self.time_input:
                self.time_input.set_time(self.auto_check_time)
                self.app.log(f"恢复定时检查时间: {self.auto_check_time}", "INFO")

            # 然后恢复复选框状态（暂时断开信号连接，避免触发事件处理器）
            if hasattr(self, 'schedule_checkbox') and self.schedule_checkbox:
                # 暂时断开信号连接
                self.schedule_checkbox.toggled.disconnect()
                self.schedule_checkbox.setChecked(self.auto_check_enabled)
                # 重新连接信号
                self.schedule_checkbox.toggled.connect(self.toggle_schedule)

                # 手动设置UI状态
                self.schedule_enabled = self.auto_check_enabled
                self.time_input.setEnabled(self.auto_check_enabled)
                if self.auto_check_enabled:
                    # 只在启用时设置定时任务，使用已恢复的时间
                    self.app.scheduler_service.clear_jobs_by_tag('main_check_job')
                    success = self.app.scheduler_service.add_daily_job(
                        self.auto_check_time, self.run_scheduled_check, 'main_check_job'
                    )
                    if success:
                        self.app.log(f"✅ 已设置每天 {self.auto_check_time} 自动执行检查", "SUCCESS")

            self.app.log("定时任务UI状态恢复完成", "INFO")

        except Exception as e:
            self.app.log(f"恢复定时任务UI状态时出错: {e}", "ERROR")

    def load_settings_from_config(self):
        """从配置加载定时任务设置"""
        try:
            # 加载定时任务设置
            self.auto_check_enabled = self.app.config.get_config_value("auto_check_enabled", False)
            self.auto_check_time = self.app.config.get_config_value("auto_check_time", "08:00")
            self.auto_sync_time_enabled = self.app.config.get_config_value("auto_sync_time_enabled", False)
            
            self.app.log("定时任务设置加载完成", "INFO")
            
        except Exception as e:
            self.app.log(f"加载定时任务设置时出错: {e}", "ERROR")