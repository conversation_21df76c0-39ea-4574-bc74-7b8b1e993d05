"""
配置管理模块
负责应用程序配置的加载、保存和管理
"""

import json
import os
from pathlib import Path
from typing import Dict, Any, Optional
from .constants import (
    CONFIG_FILE, DEFAULT_ENABLE_DISABLE, DEFAULT_DAYS_CUTOFF,
    DEFAULT_CONDITIONS_STR, DEFAULT_FIND_OFFSET, DEFAULT_GC_SCHEDULE_ENABLED,
    DEFAULT_GC_SCHEDULE_TIME, DEFAULT_GC_SCHEDULE_ACTION, DEFAULT_UPLOAD_URL,
    DEFAULT_UPLOAD_TIME_HOUR, DEFAULT_UPLOAD_TIME_MINUTE
)


class AppConfig:
    """应用程序配置管理类"""
    
    def __init__(self, app_name: str = "ServerCheckerTool"):
        self.app_name = app_name
        self.config_file_path = self._get_config_path()
        self.file_pid_gid_map = {}
        self._config_data = {}
        
    def _get_config_path(self) -> Path:
        """获取配置文件路径"""
        try:
            local_app_data = os.getenv('LOCALAPPDATA')
            if not local_app_data:
                config_dir = Path.home() / f".{self.app_name}"
                print(f"Warning: LOCALAPPDATA not found, using home directory: {config_dir}")
            else:
                config_dir = Path(local_app_data) / self.app_name
            
            config_dir.mkdir(parents=True, exist_ok=True)
            return config_dir / CONFIG_FILE
            
        except Exception as e:
            print(f"Error creating/accessing config directory: {e}. Falling back to current directory.")
            return Path(CONFIG_FILE).resolve()
    
    def get_default_advanced_settings(self) -> Dict[str, Any]:
        """获取默认高级设置"""
        return {
            "enable_disable_logic": DEFAULT_ENABLE_DISABLE,
            "disable_days_cutoff": DEFAULT_DAYS_CUTOFF,
            "disable_conditions_str": DEFAULT_CONDITIONS_STR,
            "find_server_days_offset": DEFAULT_FIND_OFFSET,
            "schedule_gameclient_enabled": DEFAULT_GC_SCHEDULE_ENABLED,
            "schedule_gameclient_time": DEFAULT_GC_SCHEDULE_TIME,
            "schedule_gameclient_action": DEFAULT_GC_SCHEDULE_ACTION
        }
    
    def load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        default_config = {
            "auto_check_enabled": False,
            "auto_check_time": "08:00",
            "close_behavior": "ask",
            "remember_close_choice": False,
            "start_on_boot_enabled": False,
            "upload_enabled": False,
            "upload_url": DEFAULT_UPLOAD_URL,
            "upload_time_hour": DEFAULT_UPLOAD_TIME_HOUR,
            "upload_time_minute": DEFAULT_UPLOAD_TIME_MINUTE,
            "file_pid_gid_map": {}
        }
        
        try:
            if os.path.exists(self.config_file_path):
                with open(self.config_file_path, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                    if isinstance(config_data, dict):
                        # 合并默认配置和加载的配置
                        for key, value in default_config.items():
                            if key not in config_data:
                                config_data[key] = value
                        
                        # 处理文件映射配置
                        self.file_pid_gid_map = config_data.get("file_pid_gid_map", {})
                        self._normalize_file_config()
                        
                        self._config_data = config_data
                        print(f"Configuration loaded from: {self.config_file_path}")
                        return config_data
                        
        except Exception as e:
            print(f"Error loading config: {e}")
        
        # 返回默认配置
        self._config_data = default_config
        return default_config
    
    def _normalize_file_config(self):
        """标准化文件配置格式"""
        default_settings = self.get_default_advanced_settings()
        
        for file_path, config in self.file_pid_gid_map.items():
            if not isinstance(config, dict):
                config = {"pairs": [], "advanced_settings": default_settings.copy()}
                self.file_pid_gid_map[file_path] = config
            
            if "pairs" not in config:
                config["pairs"] = []
            if "advanced_settings" not in config:
                config["advanced_settings"] = default_settings.copy()
            else:
                # 确保所有默认设置都存在
                for key, default_val in default_settings.items():
                    if key not in config["advanced_settings"]:
                        config["advanced_settings"][key] = default_val
    
    def save_config(self, additional_data: Optional[Dict[str, Any]] = None):
        """保存配置文件"""
        try:
            config_to_save = self._config_data.copy()
            config_to_save["file_pid_gid_map"] = self.file_pid_gid_map
            
            if additional_data:
                config_to_save.update(additional_data)
            
            with open(self.config_file_path, 'w', encoding='utf-8') as f:
                json.dump(config_to_save, f, indent=2, ensure_ascii=False)
            
            print(f"Configuration saved to: {self.config_file_path}")
            
        except Exception as e:
            print(f"Error saving config: {e}")
    
    def get_file_config(self, file_path: str) -> Dict[str, Any]:
        """获取特定文件的配置"""
        normalized_path = os.path.normpath(file_path).lower()
        
        if normalized_path not in self.file_pid_gid_map:
            default_settings = self.get_default_advanced_settings()
            self.file_pid_gid_map[normalized_path] = {
                "pairs": [], 
                "advanced_settings": default_settings
            }
        
        return self.file_pid_gid_map[normalized_path]
    
    def update_file_config(self, file_path: str, config: Dict[str, Any]):
        """更新特定文件的配置"""
        normalized_path = os.path.normpath(file_path).lower()
        self.file_pid_gid_map[normalized_path] = config
    
    def remove_file_config(self, file_path: str):
        """移除特定文件的配置"""
        normalized_path = os.path.normpath(file_path).lower()
        if normalized_path in self.file_pid_gid_map:
            del self.file_pid_gid_map[normalized_path]
    
    def get_config_value(self, key: str, default=None):
        """获取配置值"""
        return self._config_data.get(key, default)
    
    def set_config_value(self, key: str, value: Any):
        """设置配置值"""
        self._config_data[key] = value
    
    def get_startup_enabled(self) -> bool:
        """获取开机启动设置"""
        return self._config_data.get("start_on_boot_enabled", False)
    
    def set_startup_enabled(self, enabled: bool):
        """设置开机启动状态"""
        self._config_data["start_on_boot_enabled"] = enabled

    def get_all_file_configs(self) -> Dict[str, Dict[str, Any]]:
        """获取所有文件配置"""
        return self.file_pid_gid_map.copy()

    def get_all_config_data(self) -> Dict[str, Any]:
        """获取所有配置数据"""
        return self._config_data.copy()
