"""
性能优化模块
提供各种性能优化工具和缓存机制
"""

import time
import weakref
from typing import Dict, Any, Optional, Callable, List
from functools import wraps, lru_cache
from PyQt6.QtCore import QTimer, QObject, pyqtSignal
from PyQt6.QtGui import QIcon, QPixmap


class IconCache:
    """图标缓存管理器"""
    
    def __init__(self, max_size: int = 100):
        self._cache: Dict[str, QIcon] = {}
        self._access_times: Dict[str, float] = {}
        self._max_size = max_size
    
    def get_icon(self, path: str) -> Optional[QIcon]:
        """获取缓存的图标"""
        if path in self._cache:
            self._access_times[path] = time.time()
            return self._cache[path]
        return None
    
    def set_icon(self, path: str, icon: QIcon):
        """缓存图标"""
        if len(self._cache) >= self._max_size:
            self._evict_oldest()
        
        self._cache[path] = icon
        self._access_times[path] = time.time()
    
    def _evict_oldest(self):
        """移除最久未使用的图标"""
        if not self._access_times:
            return
        
        oldest_path = min(self._access_times.keys(), key=lambda k: self._access_times[k])
        del self._cache[oldest_path]
        del self._access_times[oldest_path]
    
    def clear(self):
        """清空缓存"""
        self._cache.clear()
        self._access_times.clear()
    
    def size(self) -> int:
        """获取缓存大小"""
        return len(self._cache)


class UIUpdateThrottler(QObject):
    """UI更新节流器，防止过于频繁的UI更新"""
    
    update_requested = pyqtSignal()
    
    def __init__(self, interval_ms: int = 100):
        super().__init__()
        self._interval_ms = interval_ms
        self._timer = QTimer()
        self._timer.setSingleShot(True)
        self._timer.timeout.connect(self.update_requested.emit)
        self._pending = False
    
    def request_update(self):
        """请求更新（会被节流）"""
        if not self._pending:
            self._pending = True
            self._timer.start(self._interval_ms)
    
    def force_update(self):
        """强制立即更新"""
        if self._timer.isActive():
            self._timer.stop()
        self._pending = False
        self.update_requested.emit()


class MemoryManager:
    """内存管理器"""
    
    def __init__(self):
        self._weak_refs: List[weakref.ref] = []
        self._cleanup_timer = QTimer()
        self._cleanup_timer.timeout.connect(self._cleanup_dead_refs)
        self._cleanup_timer.start(30000)  # 每30秒清理一次
    
    def register_object(self, obj: Any):
        """注册对象用于内存监控"""
        self._weak_refs.append(weakref.ref(obj))
    
    def _cleanup_dead_refs(self):
        """清理已死亡的弱引用"""
        self._weak_refs = [ref for ref in self._weak_refs if ref() is not None]
    
    def get_alive_count(self) -> int:
        """获取存活对象数量"""
        return len([ref for ref in self._weak_refs if ref() is not None])
    
    def force_cleanup(self):
        """强制清理"""
        import gc
        self._cleanup_dead_refs()
        gc.collect()


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self):
        self._timings: Dict[str, List[float]] = {}
        self._counters: Dict[str, int] = {}
    
    def time_function(self, name: str):
        """函数执行时间装饰器"""
        def decorator(func: Callable):
            @wraps(func)
            def wrapper(*args, **kwargs):
                start_time = time.time()
                try:
                    result = func(*args, **kwargs)
                    return result
                finally:
                    end_time = time.time()
                    duration = end_time - start_time
                    self.record_timing(name, duration)
            return wrapper
        return decorator
    
    def record_timing(self, name: str, duration: float):
        """记录执行时间"""
        if name not in self._timings:
            self._timings[name] = []
        self._timings[name].append(duration)
        
        # 只保留最近100次记录
        if len(self._timings[name]) > 100:
            self._timings[name] = self._timings[name][-100:]
    
    def increment_counter(self, name: str):
        """增加计数器"""
        self._counters[name] = self._counters.get(name, 0) + 1
    
    def get_average_timing(self, name: str) -> Optional[float]:
        """获取平均执行时间"""
        if name in self._timings and self._timings[name]:
            return sum(self._timings[name]) / len(self._timings[name])
        return None
    
    def get_counter(self, name: str) -> int:
        """获取计数器值"""
        return self._counters.get(name, 0)
    
    def get_stats(self) -> Dict[str, Any]:
        """获取性能统计信息"""
        stats = {
            'timings': {},
            'counters': self._counters.copy()
        }
        
        for name, times in self._timings.items():
            if times:
                stats['timings'][name] = {
                    'average': sum(times) / len(times),
                    'min': min(times),
                    'max': max(times),
                    'count': len(times)
                }
        
        return stats
    
    def reset(self):
        """重置所有统计信息"""
        self._timings.clear()
        self._counters.clear()


class LazyLoader:
    """延迟加载器"""
    
    def __init__(self):
        self._loaded: Dict[str, Any] = {}
        self._loaders: Dict[str, Callable] = {}
    
    def register_loader(self, name: str, loader: Callable):
        """注册延迟加载器"""
        self._loaders[name] = loader
    
    def get(self, name: str) -> Any:
        """获取对象（延迟加载）"""
        if name not in self._loaded:
            if name in self._loaders:
                self._loaded[name] = self._loaders[name]()
            else:
                raise KeyError(f"No loader registered for '{name}'")
        return self._loaded[name]
    
    def is_loaded(self, name: str) -> bool:
        """检查是否已加载"""
        return name in self._loaded
    
    def unload(self, name: str):
        """卸载对象"""
        if name in self._loaded:
            del self._loaded[name]


# 全局实例
icon_cache = IconCache()
memory_manager = MemoryManager()
performance_monitor = PerformanceMonitor()
lazy_loader = LazyLoader()


# 装饰器函数
def cached_icon(func: Callable) -> Callable:
    """图标缓存装饰器"""
    @wraps(func)
    def wrapper(path: str, *args, **kwargs):
        # 尝试从缓存获取
        cached = icon_cache.get_icon(path)
        if cached is not None:
            return cached

        # 加载图标
        icon = func(path, *args, **kwargs)

        # 检查图标是否有效（支持QIcon和其他类型）
        if icon is not None:
            # 如果是QIcon对象，检查是否有效
            if hasattr(icon, 'isNull'):
                if not icon.isNull():
                    icon_cache.set_icon(path, icon)
            else:
                # 对于其他类型（如测试中的字符串），直接缓存
                icon_cache.set_icon(path, icon)

        return icon
    return wrapper


def throttled_update(interval_ms: int = 100):
    """UI更新节流装饰器"""
    def decorator(func: Callable):
        throttler = UIUpdateThrottler(interval_ms)
        
        @wraps(func)
        def wrapper(*args, **kwargs):
            def do_update():
                func(*args, **kwargs)
            
            throttler.update_requested.connect(do_update)
            throttler.request_update()
        
        return wrapper
    return decorator


def monitor_performance(name: str):
    """性能监控装饰器"""
    return performance_monitor.time_function(name)


@lru_cache(maxsize=128)
def cached_calculation(func: Callable):
    """计算结果缓存装饰器"""
    return func


class BatchProcessor:
    """批处理器，用于批量处理UI更新"""
    
    def __init__(self, batch_size: int = 10, delay_ms: int = 50):
        self._batch_size = batch_size
        self._delay_ms = delay_ms
        self._queue: List[Callable] = []
        self._timer = QTimer()
        self._timer.setSingleShot(True)
        self._timer.timeout.connect(self._process_batch)
    
    def add_task(self, task: Callable):
        """添加任务到批处理队列"""
        self._queue.append(task)
        
        if len(self._queue) >= self._batch_size:
            self._process_batch()
        elif not self._timer.isActive():
            self._timer.start(self._delay_ms)
    
    def _process_batch(self):
        """处理批次"""
        if self._timer.isActive():
            self._timer.stop()
        
        tasks = self._queue.copy()
        self._queue.clear()
        
        for task in tasks:
            try:
                task()
            except Exception as e:
                print(f"Error in batch task: {e}")
    
    def flush(self):
        """立即处理所有待处理任务"""
        self._process_batch()


# 全局批处理器实例
ui_batch_processor = BatchProcessor()


def get_performance_stats() -> Dict[str, Any]:
    """获取性能统计信息"""
    return {
        'icon_cache_size': icon_cache.size(),
        'alive_objects': memory_manager.get_alive_count(),
        'performance_stats': performance_monitor.get_stats()
    }
