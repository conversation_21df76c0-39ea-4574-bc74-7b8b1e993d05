"""
事件处理器模块
负责各种事件处理、信号处理、系统集成等功能
从 app_pyqt6.py 中提取的事件处理相关功能
"""

import os
import sys
import threading
import time
from typing import Dict, Any, Optional, List
from PyQt6.QtWidgets import QMessageBox, QDialog, QApplication
from PyQt6.QtCore import Qt, QTime, QTimer, QEvent
from PyQt6.QtGui import QFont, QCloseEvent
from concurrent.futures import ThreadPoolExecutor, as_completed

from utils.pyqt6_threading import SafeUIUpdater
from utils.sequential_executor import StepStatus


class EventHandler:
    """事件处理器类 - 负责各种事件和信号的处理"""
    
    def __init__(self, parent_app):
        """
        初始化事件处理器
        
        Args:
            parent_app: 主应用程序实例
        """
        self.app = parent_app
        
        # 系统集成设置
        self.start_on_boot_enabled = False
        self.close_behavior = "ask"
        self.remember_close_choice = False
        self.run_in_tray = False
        
        # 日志设置
        self.log_level_filter = ["INFO", "SUCCESS", "WARNING", "ERROR"]
        self.log_batch_update = True
        self.log_batch_size = 10

    def setup_sequential_executor_signals(self):
        """设置顺序执行器信号连接"""
        if hasattr(self.app, 'sequential_executor'):
            self.app.sequential_executor.step_started.connect(self.on_step_started)
            self.app.sequential_executor.step_completed.connect(self.on_step_completed)
            self.app.sequential_executor.step_progress.connect(self.on_step_progress)
            self.app.sequential_executor.execution_completed.connect(self.on_execution_completed)
            self.app.sequential_executor.execution_cancelled.connect(self.on_execution_cancelled)

    def setup_system_integration(self):
        """设置系统集成"""
        # Setup tray service integration with PyQt6 signals
        if hasattr(self.app, 'tray_service'):
            self.app.tray_service.showWindow.connect(self.show_from_tray_safe)
            self.app.tray_service.runCheck.connect(self.run_check_from_tray_safe)
            self.app.tray_service.quitApp.connect(self.quit_app_safe)

            # 也设置回调方式（兼容性）
            self.app.tray_service.set_callback('show_window', self.show_from_tray_safe)
            self.app.tray_service.set_callback('run_check', self.run_check_from_tray_safe)
            self.app.tray_service.set_callback('quit_app', self.quit_app_safe)

            # Show tray icon
            if hasattr(self.app.tray_service, 'show'):
                self.app.tray_service.show()

    def show_from_tray_safe(self):
        """从托盘安全显示应用（线程安全）"""
        # PyQt6信号已经在主线程中，直接调用即可
        self._show_window_main_thread()

    def _show_window_main_thread(self):
        """在主线程中显示窗口"""
        try:
            self.app.show()
            self.app.raise_()
            self.app.activateWindow()
            self.app.setWindowState(self.app.windowState() & ~Qt.WindowState.WindowMinimized)
            self.app.log("已从托盘显示主窗口", "INFO")
        except Exception as e:
            self.app.log(f"从托盘显示窗口时出错: {e}", "ERROR")

    def run_check_from_tray_safe(self):
        """从托盘安全执行检查（线程安全）"""
        # PyQt6信号已经在主线程中，直接调用即可
        self._run_check_main_thread()

    def _run_check_main_thread(self):
        """在主线程中执行检查"""
        try:
            selected_files = self.app.file_manager.get_selected_files()
            if not selected_files:
                if self.app.file_manager.file_checkboxes:
                    self.app.file_manager.select_all_files()
                    selected_files = self.app.file_manager.get_selected_files()

            if selected_files:
                self.app.log(f"从托盘执行检查：将对 {len(selected_files)} 个文件执行检查...", "INFO")
                self.app.start_check()
            else:
                self.app.log("从托盘执行检查失败：没有可检查的文件", "WARNING")
        except Exception as e:
            self.app.log(f"从托盘执行检查时出错: {e}", "ERROR")

    def quit_app_safe(self):
        """安全退出应用（线程安全）"""
        # PyQt6信号已经在主线程中，直接调用即可
        self.quit_app()

    def quit_app(self, icon=None, item=None):
        """退出应用程序"""
        self.app.log("正在退出应用程序...")

        try:
            # 1. 首先隐藏托盘图标，防止用户继续操作
            if hasattr(self.app, 'tray_service') and self.app.tray_service:
                self.app.tray_service.hide()

            # 2. 保存配置
            try:
                self.save_application_config()
            except Exception as e:
                self.app.log(f"保存配置时出错: {e}", "ERROR")

            # 3. 停止调度器
            try:
                if hasattr(self.app, 'scheduler_service') and self.app.scheduler_service:
                    self.app.scheduler_service.stop()
            except Exception as e:
                self.app.log(f"停止调度器时出错: {e}", "ERROR")

            # 4. 停止所有线程
            try:
                if hasattr(self.app, 'thread_manager') and self.app.thread_manager:
                    self.app.thread_manager.stop_all_threads()
                    # 给线程更多时间来完全退出
                    time.sleep(0.1)
            except Exception as e:
                self.app.log(f"停止线程时出错: {e}", "ERROR")

            # 5. 清理网络服务
            try:
                if hasattr(self.app, 'network_service') and self.app.network_service:
                    self.app.network_service.cleanup()
            except Exception as e:
                self.app.log(f"清理网络服务时出错: {e}", "ERROR")

            # 6. 清理托盘服务
            try:
                if hasattr(self.app, 'tray_service') and self.app.tray_service:
                    self.app.tray_service.cleanup()
            except Exception as e:
                self.app.log(f"清理托盘服务时出错: {e}", "ERROR")

            # 7. 清理调度器服务
            try:
                if hasattr(self.app, 'scheduler_service') and self.app.scheduler_service:
                    self.app.scheduler_service.cleanup()
            except Exception as e:
                self.app.log(f"清理调度器服务时出错: {e}", "ERROR")

            # 8. 强制处理所有待处理的事件
            app = QApplication.instance()
            if app:
                app.processEvents()
                # 给Qt一些时间来完成清理
                time.sleep(0.05)

            # 9. 关闭主窗口
            try:
                self.app.close()
            except Exception as e:
                self.app.log(f"关闭窗口时出错: {e}", "ERROR")

        except Exception as e:
            self.app.log(f"退出过程中出错: {e}", "ERROR")
        
        finally:
            # 10. 最终退出应用程序
            try:
                app = QApplication.instance()
                if app:
                    # 确保所有事件都被处理
                    app.processEvents()
                    # 安全退出
                    app.quit()
            except Exception as e:
                print(f"最终退出时出错: {e}")
                # 如果正常退出失败，强制退出
                sys.exit(0)

    def closeEvent(self, event: QCloseEvent):
        """处理窗口关闭事件"""
        if self.close_behavior == "ask":
            reply = QMessageBox.question(
                self.app,
                "确认退出",
                "确定要退出程序吗？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )

            if reply == QMessageBox.StandardButton.Yes:
                self.quit_app()
                event.accept()
            else:
                event.ignore()
        elif self.close_behavior == "exit":
            self.quit_app()
            event.accept()
        else:
            # Hide to tray
            self.app.hide()
            event.ignore()

    def open_system_settings(self):
        """打开系统设置对话框"""
        try:
            from gui.dialogs_pyqt6 import SystemSettingsDialog

            dialog = SystemSettingsDialog(self.app)

            # 设置当前值
            dialog.set_startup_enabled(self.start_on_boot_enabled)
            dialog.set_close_behavior(self.close_behavior)
            dialog.set_remember_choice(self.remember_close_choice)
            dialog.set_run_in_tray(self.run_in_tray)
            dialog.set_auto_sync_time(self.app.schedule_manager.auto_sync_time_enabled)
            
            # 设置日志配置
            dialog.set_log_level_filter(self.log_level_filter)
            dialog.set_log_batch_update(self.log_batch_update)
            dialog.set_log_batch_size(self.log_batch_size)

            result = dialog.exec()
            if result == QDialog.DialogCode.Accepted:
                # 获取设置值
                settings = dialog.get_settings()

                # 应用设置
                old_startup = self.start_on_boot_enabled
                self.start_on_boot_enabled = settings['startup_enabled']
                self.close_behavior = settings['close_behavior']
                self.remember_close_choice = settings['remember_choice']
                self.run_in_tray = settings['run_in_tray']
                self.app.schedule_manager.auto_sync_time_enabled = settings['auto_sync_time']
                
                # 应用日志设置
                if 'log_level_filter' in settings:
                    self.set_log_level_filter(settings['log_level_filter'])
                if 'log_batch_update' in settings:
                    self.toggle_log_batch_update(settings['log_batch_update'])
                if 'log_batch_size' in settings:
                    self.log_batch_size = settings['log_batch_size']

                # 处理开机启动设置变化
                if old_startup != self.start_on_boot_enabled:
                    self.toggle_start_on_boot()

                # 保存配置
                self.save_application_config()

                self.app.log("系统设置已更新", "SUCCESS")
            else:
                self.app.log("系统设置已取消", "INFO")

        except Exception as e:
            self.app.log(f"打开系统设置对话框时出错: {e}", "ERROR")
            import traceback
            self.app.log(f"错误详情: {traceback.format_exc()}", "DEBUG")

    def toggle_start_on_boot(self):
        """切换开机启动设置"""
        try:
            # 检查是否在Windows系统上
            if sys.platform != 'win32':
                self.app.log("开机启动功能仅在Windows系统上可用", "WARNING")
                return

            from utils.system import add_to_startup, remove_from_startup

            if self.start_on_boot_enabled:
                # 启用开机启动
                self.app.log("正在启用开机启动...", "INFO")
                success = add_to_startup()
                if success:
                    self.app.log("开机启动已启用", "SUCCESS")
                else:
                    self.app.log("启用开机启动失败，可能需要管理员权限", "ERROR")
                    self.start_on_boot_enabled = False
            else:
                # 禁用开机启动
                self.app.log("正在禁用开机启动...", "INFO")
                success = remove_from_startup()
                if success:
                    self.app.log("开机启动已禁用", "SUCCESS")
                else:
                    self.app.log("禁用开机启动失败，可能需要管理员权限", "ERROR")
                    self.start_on_boot_enabled = True

        except ImportError as e:
            self.app.log(f"导入系统模块失败: {e}", "ERROR")
            self.start_on_boot_enabled = not self.start_on_boot_enabled  # 恢复原状态
        except Exception as e:
            self.app.log(f"设置开机启动时出错: {e}", "ERROR")
            self.start_on_boot_enabled = not self.start_on_boot_enabled  # 恢复原状态

    def setup_tray_menu(self):
        """设置托盘菜单"""
        try:
            if not hasattr(self.app, 'tray_service'):
                return
                
            # 清除现有菜单项，避免重复
            self.app.tray_service._menu_items.clear()

            # 添加托盘菜单项（使用线程安全的方法）
            self.app.tray_service.add_menu_item("显示主窗口", self.show_from_tray_safe)
            self.app.tray_service.add_menu_item("执行检查", self.run_check_from_tray_safe)
            self.app.tray_service.add_menu_item("退出程序", self.quit_app_safe)

            self.app.log("托盘菜单设置完成", "INFO")
        except Exception as e:
            self.app.log(f"设置托盘菜单时出错: {e}", "ERROR")

    # ==================== PyQt6网络服务信号处理 ====================

    def handle_network_data(self, url: str, data):
        """处理网络数据接收"""
        try:
            self.app.log(f"网络数据接收成功: {url}", "INFO")

            # 这里可以添加具体的数据处理逻辑
            # 由于原来的fetch_server_data是同步的，这里需要适配
            if isinstance(data, list):
                self.app.log(f"接收到服务器列表，共 {len(data)} 个服务器", "INFO")
            elif isinstance(data, dict):
                self.app.log(f"接收到数据对象，键: {list(data.keys())[:5]}...", "INFO")

        except Exception as e:
            self.app.log(f"处理网络数据时出错: {e}", "ERROR")

    def handle_network_error(self, url: str, error: str):
        """处理网络错误"""
        self.app.log(f"网络请求失败: {url} - {error}", "ERROR")

    # ==================== PyQt6调度服务信号处理 ====================

    def handle_job_executed(self, tag: str, function_name: str):
        """任务执行成功处理"""
        self.app.log(f"定时任务执行成功: {function_name} (标签: {tag})", "INFO")

    def handle_job_error(self, tag: str, function_name: str, error: str):
        """任务执行错误处理"""
        self.app.log(f"定时任务执行失败: {function_name} (标签: {tag}) - {error}", "ERROR")

    # ==================== 顺序执行器信号处理 ====================

    def on_step_started(self, step_name: str, description: str):
        """步骤开始信号处理"""
        self.app.log(f"▶ 开始执行: {description}", "INFO")
        if hasattr(self.app, 'status_bar'):
            self.app.status_bar.showMessage(f"正在执行: {description}")
        if hasattr(self.app, 'progress_bar'):
            self.app.progress_bar.setVisible(True)

    def on_step_completed(self, step_name: str, success: bool, message: str):
        """步骤完成信号处理"""
        if success:
            self.app.log(f"✓ 步骤完成: {message}", "SUCCESS")
        else:
            self.app.log(f"✗ 步骤失败: {message}", "ERROR")

    def on_step_progress(self, step_name: str, progress_message: str):
        """步骤进度信号处理"""
        self.app.log(f"  {progress_message}", "INFO")

    def on_execution_completed(self, success: bool, summary_message: str):
        """执行完成信号处理"""
        if success:
            self.app.log(f"🎉 立即执行完成: {summary_message}", "SUCCESS")
            if hasattr(self.app, 'status_bar'):
                self.app.status_bar.showMessage("立即执行完成")
        else:
            self.app.log(f"⚠ 立即执行结束: {summary_message}", "WARNING")
            if hasattr(self.app, 'status_bar'):
                self.app.status_bar.showMessage("立即执行中断")

        if hasattr(self.app, 'progress_bar'):
            self.app.progress_bar.setVisible(False)
        self._reset_immediate_execution_ui()

        # 显示执行摘要
        if hasattr(self.app, 'sequential_executor'):
            summary = self.app.sequential_executor.get_execution_summary()
            self._show_execution_summary(summary)

    def on_execution_cancelled(self):
        """执行取消信号处理"""
        self.app.log("立即执行已取消", "WARNING")
        if hasattr(self.app, 'status_bar'):
            self.app.status_bar.showMessage("立即执行已取消")
        if hasattr(self.app, 'progress_bar'):
            self.app.progress_bar.setVisible(False)
        self._reset_immediate_execution_ui()

    def _show_execution_summary(self, summary: dict):
        """显示执行摘要"""
        total = summary['total_steps']
        completed = summary['completed_steps']
        failed = summary['failed_steps']
        duration = summary['total_duration']

        summary_text = f"执行摘要:\n"
        summary_text += f"总步骤: {total}\n"
        summary_text += f"成功: {completed}\n"
        summary_text += f"失败: {failed}\n"
        summary_text += f"总耗时: {duration:.2f}秒\n\n"

        summary_text += "详细信息:\n"
        for step in summary['steps_detail']:
            status_icon = "✓" if step['status'] == 'completed' else "✗" if step['status'] == 'failed' else "⏸"
            duration_str = f" ({step['duration']:.2f}s)" if step['duration'] else ""
            summary_text += f"{status_icon} {step['description']}{duration_str}\n"
            if step['error']:
                summary_text += f"   错误: {step['error']}\n"

        self.app.log(summary_text, "INFO")

    def _reset_immediate_execution_ui(self):
        """重置立即执行UI状态"""
        if hasattr(self.app, 'schedule_manager') and self.app.schedule_manager.run_now_button:
            self.app.schedule_manager.run_now_button.setEnabled(True)
            self.app.schedule_manager.run_now_button.setText("立即执行")

    def load_system_integration_settings(self):
        """加载系统集成设置"""
        try:
            # 加载开机启动设置
            self.start_on_boot_enabled = self.app.config.get_config_value("start_on_boot_enabled", False)

            # 加载关闭行为设置
            self.close_behavior = self.app.config.get_config_value("close_behavior", "ask")
            self.remember_close_choice = self.app.config.get_config_value("remember_close_choice", False)

            # 加载托盘运行设置
            self.run_in_tray = self.app.config.get_config_value("run_in_tray", False)
            
            # 恢复日志设置
            self.log_level_filter = self.app.config.get_config_value("log_level_filter", ["INFO", "SUCCESS", "WARNING", "ERROR"])
            self.log_batch_update = self.app.config.get_config_value("log_batch_update", True)
            self.log_batch_size = self.app.config.get_config_value("log_batch_size", 10)

            self.app.log("系统集成设置加载完成", "INFO")

        except Exception as e:
            self.app.log(f"加载系统集成设置时出错: {e}", "ERROR")

    def save_application_config(self):
        """保存应用程序配置"""
        try:
            self.app.log("正在保存应用程序配置...", "INFO")

            # 更新配置数据
            self.app.config.set_config_value("auto_check_enabled", self.app.schedule_manager.auto_check_enabled)
            self.app.config.set_config_value("auto_check_time", self.app.schedule_manager.auto_check_time)
            self.app.config.set_config_value("auto_sync_time_enabled", self.app.schedule_manager.auto_sync_time_enabled)

            self.app.config.set_config_value("upload_enabled", self.app.upload_manager.upload_enabled)
            self.app.config.set_config_value("upload_url", self.app.upload_manager.upload_url)

            # 保存上传时间
            if hasattr(self.app.upload_manager, 'upload_time_input') and self.app.upload_manager.upload_time_input:
                upload_time = self.app.upload_manager.upload_time_input.text().strip()
                self.app.config.set_config_value("upload_time", upload_time)
            else:
                self.app.config.set_config_value("upload_time", "03:00")

            self.app.config.set_config_value("start_on_boot_enabled", self.start_on_boot_enabled)
            self.app.config.set_config_value("close_behavior", self.close_behavior)
            self.app.config.set_config_value("remember_close_choice", self.remember_close_choice)
            self.app.config.set_config_value("run_in_tray", self.run_in_tray)
            
            # 保存日志设置
            self.app.config.set_config_value("log_level_filter", self.log_level_filter)
            self.app.config.set_config_value("log_batch_update", self.log_batch_update)
            self.app.config.set_config_value("log_batch_size", self.log_batch_size)

            # 保存配置文件
            self.app.config.save_config()

            self.app.log("应用程序配置保存完成", "SUCCESS")

        except Exception as e:
            self.app.log(f"保存配置时出错: {e}", "ERROR")

    def set_log_level_filter(self, levels: list):
        """设置日志级别过滤器
        
        Args:
            levels: 要显示的日志级别列表，如 ["INFO", "SUCCESS", "WARNING", "ERROR"]
        """
        self.log_level_filter = levels
        self.app.log(f"日志级别过滤已更新: {', '.join(levels)}", "INFO")
    
    def toggle_log_batch_update(self, enabled: bool):
        """切换日志批量更新模式
        
        Args:
            enabled: 是否启用批量更新
        """
        self.log_batch_update = enabled
        if not enabled:
            # 禁用时立即刷新缓冲区
            self._flush_log_batch()
        self.app.log(f"日志批量更新已{'启用' if enabled else '禁用'}", "INFO")

    def _flush_log_batch(self):
        """批量刷新日志缓冲区（暂时禁用）"""
        # 暂时禁用批量更新功能以解决性能问题
        pass