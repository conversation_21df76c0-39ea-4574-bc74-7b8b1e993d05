"""
延迟导入管理器
实现模块的延迟加载，减少启动时间
"""

import sys
import time
import importlib
import threading
from typing import Dict, Any, Optional, Callable, List
from functools import wraps

from utils.performance_optimizer import performance_monitor, monitor_performance


class LazyImportManager:
    """延迟导入管理器"""
    
    def __init__(self):
        self._modules = {}
        self._loading_modules = set()
        self._load_lock = threading.Lock()
        self._import_stats = {}
        
        # 预定义的延迟导入模块
        self._lazy_modules = {
            # GUI相关模块
            'gui.dialogs_pyqt6': None,
            'gui.styles_pyqt6': None,
            
            # 工具模块
            'utils.ui_optimizer': None,
            'utils.process_control': None,
            
            # 服务模块
            'services.optimized_file_service': None,
            'services.unified_network_service': None,
            
            # 第三方模块
            'chardet': None,
            'psutil': None,
            'ntplib': None,
        }
    
    @monitor_performance('lazy_import')
    def get_module(self, module_name: str, force_load: bool = False) -> Optional[Any]:
        """
        获取模块（延迟加载）
        
        Args:
            module_name: 模块名称
            force_load: 是否强制立即加载
            
        Returns:
            模块对象或None
        """
        # 如果模块已经加载，直接返回
        if module_name in self._modules:
            return self._modules[module_name]
        
        # 如果模块正在加载中，等待加载完成
        if module_name in self._loading_modules:
            while module_name in self._loading_modules:
                time.sleep(0.001)  # 短暂等待
            return self._modules.get(module_name)
        
        # 如果不是延迟模块且不强制加载，返回None
        if module_name not in self._lazy_modules and not force_load:
            return None
        
        # 加载模块
        return self._load_module(module_name)
    
    def _load_module(self, module_name: str) -> Optional[Any]:
        """加载模块"""
        with self._load_lock:
            # 双重检查
            if module_name in self._modules:
                return self._modules[module_name]
            
            if module_name in self._loading_modules:
                return None
            
            self._loading_modules.add(module_name)
            
            try:
                start_time = time.time()
                
                # 尝试导入模块
                module = importlib.import_module(module_name)
                
                # 记录导入时间
                import_time = time.time() - start_time
                self._import_stats[module_name] = import_time
                
                # 缓存模块
                self._modules[module_name] = module
                
                print(f"延迟加载模块: {module_name} ({import_time:.3f}s)")
                performance_monitor.record_timing(f'import_{module_name}', import_time)
                
                return module
                
            except ImportError as e:
                print(f"延迟导入失败: {module_name} - {e}")
                return None
            except Exception as e:
                print(f"延迟导入异常: {module_name} - {e}")
                return None
            finally:
                self._loading_modules.discard(module_name)
    
    def preload_modules(self, module_names: List[str], background: bool = True):
        """
        预加载模块
        
        Args:
            module_names: 要预加载的模块名称列表
            background: 是否在后台线程中加载
        """
        if background:
            threading.Thread(
                target=self._preload_modules_sync,
                args=(module_names,),
                daemon=True
            ).start()
        else:
            self._preload_modules_sync(module_names)
    
    def _preload_modules_sync(self, module_names: List[str]):
        """同步预加载模块"""
        for module_name in module_names:
            try:
                self.get_module(module_name, force_load=True)
            except Exception as e:
                print(f"预加载模块失败: {module_name} - {e}")
    
    def get_import_stats(self) -> Dict[str, float]:
        """获取导入统计信息"""
        return self._import_stats.copy()
    
    def get_loaded_modules(self) -> List[str]:
        """获取已加载的模块列表"""
        return list(self._modules.keys())
    
    def clear_cache(self):
        """清空缓存"""
        self._modules.clear()
        self._import_stats.clear()


class LazyImportProxy:
    """延迟导入代理"""
    
    def __init__(self, module_name: str, manager: LazyImportManager):
        self._module_name = module_name
        self._manager = manager
        self._module = None
    
    def __getattr__(self, name: str):
        """获取模块属性"""
        if self._module is None:
            self._module = self._manager.get_module(self._module_name, force_load=True)
            if self._module is None:
                raise ImportError(f"无法加载模块: {self._module_name}")
        
        return getattr(self._module, name)
    
    def __call__(self, *args, **kwargs):
        """如果模块本身是可调用的"""
        if self._module is None:
            self._module = self._manager.get_module(self._module_name, force_load=True)
            if self._module is None:
                raise ImportError(f"无法加载模块: {self._module_name}")
        
        return self._module(*args, **kwargs)


def lazy_import(module_name: str) -> LazyImportProxy:
    """
    创建延迟导入代理
    
    Args:
        module_name: 模块名称
        
    Returns:
        延迟导入代理对象
    """
    return LazyImportProxy(module_name, lazy_import_manager)


def lazy_import_decorator(module_name: str):
    """
    延迟导入装饰器
    
    Args:
        module_name: 模块名称
    """
    def decorator(func: Callable):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 在函数执行前确保模块已加载
            module = lazy_import_manager.get_module(module_name, force_load=True)
            if module is None:
                raise ImportError(f"无法加载模块: {module_name}")
            
            return func(*args, **kwargs)
        return wrapper
    return decorator


class StartupOptimizer:
    """启动优化器"""
    
    def __init__(self):
        self._startup_phases = []
        self._current_phase = 0
        self._phase_times = {}
        self._total_start_time = None
    
    def add_phase(self, name: str, func: Callable, priority: int = 0):
        """
        添加启动阶段
        
        Args:
            name: 阶段名称
            func: 执行函数
            priority: 优先级（数字越小优先级越高）
        """
        self._startup_phases.append({
            'name': name,
            'func': func,
            'priority': priority
        })
        
        # 按优先级排序
        self._startup_phases.sort(key=lambda x: x['priority'])
    
    @monitor_performance('startup_optimization')
    def execute_startup(self, progress_callback: Optional[Callable] = None) -> bool:
        """
        执行优化的启动流程
        
        Args:
            progress_callback: 进度回调函数
            
        Returns:
            启动是否成功
        """
        self._total_start_time = time.time()
        total_phases = len(self._startup_phases)
        
        print("开始优化启动流程...")
        
        for i, phase in enumerate(self._startup_phases):
            phase_name = phase['name']
            phase_func = phase['func']
            
            print(f"执行启动阶段: {phase_name}")
            
            try:
                start_time = time.time()
                
                # 执行阶段函数
                result = phase_func()
                
                # 记录阶段时间
                phase_time = time.time() - start_time
                self._phase_times[phase_name] = phase_time
                
                print(f"阶段 '{phase_name}' 完成 ({phase_time:.3f}s)")
                
                # 更新进度
                if progress_callback:
                    progress = (i + 1) / total_phases
                    progress_callback(progress, f"完成: {phase_name}")
                
                # 如果阶段函数返回False，停止启动
                if result is False:
                    print(f"启动阶段 '{phase_name}' 失败，停止启动")
                    return False
                    
            except Exception as e:
                print(f"启动阶段 '{phase_name}' 异常: {e}")
                return False
        
        total_time = time.time() - self._total_start_time
        print(f"启动流程完成，总耗时: {total_time:.3f}s")
        
        # 输出阶段统计
        self._print_phase_stats()
        
        return True
    
    def _print_phase_stats(self):
        """输出阶段统计信息"""
        print("\n启动阶段统计:")
        print("-" * 40)
        
        total_time = sum(self._phase_times.values())
        
        for phase_name, phase_time in self._phase_times.items():
            percentage = (phase_time / total_time) * 100 if total_time > 0 else 0
            print(f"{phase_name:25} {phase_time:6.3f}s ({percentage:5.1f}%)")
        
        print("-" * 40)
        print(f"{'总计':25} {total_time:6.3f}s (100.0%)")
    
    def get_phase_stats(self) -> Dict[str, float]:
        """获取阶段统计信息"""
        return self._phase_times.copy()


# 全局实例
lazy_import_manager = LazyImportManager()
startup_optimizer = StartupOptimizer()


# 常用的延迟导入
def get_chardet():
    """获取chardet模块"""
    return lazy_import_manager.get_module('chardet', force_load=True)


def get_psutil():
    """获取psutil模块"""
    return lazy_import_manager.get_module('psutil', force_load=True)


def get_ntplib():
    """获取ntplib模块"""
    return lazy_import_manager.get_module('ntplib', force_load=True)
