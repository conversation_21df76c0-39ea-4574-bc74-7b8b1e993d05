"""
对话框模块 - PyQt6版本
包含各种配置和设置对话框
"""

import copy
import os
import re
from typing import Dict, Any, Optional

from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QGridLayout, QFrame, QLabel,
    QPushButton, QTextEdit, QLineEdit, QCheckBox, QRadioButton,
    QComboBox, QScrollArea, QWidget, QGroupBox, QSpinBox, QTimeEdit,
    QMessageBox, QButtonGroup
)
from PyQt6.QtCore import Qt, QTimer, QTime
from PyQt6.QtGui import QFont
from gui.time_input_widget import TimeInputWidget

from core.constants import (
    DEFAULT_ENABLE_DISABLE, DEFAULT_DAYS_CUTOFF, DEFAULT_CONDITIONS_STR,
    DEFAULT_FIND_OFFSET, DEFAULT_GC_SCHEDULE_ENABLED, DEFAULT_GC_SCHEDULE_TIME,
    DEFAULT_GC_SCHEDULE_ACTION
)
from .ui_factory import UIFactory


class PIDGIDConfigDialog(QDialog):
    """PID/GID配置对话框"""
    
    def __init__(self, parent, file_path: str, file_config: Dict[str, Any], app_config):
        super().__init__(parent)
        self.parent_app = parent
        self.file_path = file_path
        self.app_config = app_config
        
        # Deep copy configuration to avoid direct modification
        self.file_config_copy = copy.deepcopy(file_config)
        
        # Setup window
        self.setWindowTitle(f"配置文件设置 - {os.path.basename(file_path)}")
        self.setModal(True)
        self.resize(800, 700)
        
        # Create interface
        self.create_widgets()
        
        # Center dialog
        QTimer.singleShot(100, self.center_dialog)
    
    def create_widgets(self):
        """创建界面组件"""
        main_layout = QVBoxLayout(self)
        
        # Main scroll area
        scroll_area = QScrollArea()
        scroll_widget = QWidget()
        self.scroll_layout = QVBoxLayout(scroll_widget)
        
        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)
        main_layout.addWidget(scroll_area)
        
        # PID/GID configuration section
        self.create_pid_gid_section()
        
        # Advanced settings section
        self.create_advanced_settings_section()
        
        # Button section
        self.create_buttons(main_layout)
    
    def create_pid_gid_section(self):
        """创建PID/GID配置区域"""
        pid_gid_group = QGroupBox("PID/GID配置")
        pid_gid_layout = QVBoxLayout(pid_gid_group)
        
        # Help text
        help_label = QLabel(
            "输入PID,GID及覆盖值:\n"
            "- 每行一个 PID,GID 或 键=值\n"
            "- 覆盖值(键=值)应用于上方最近的所有PID,GID\n"
            "- 使用空行分隔不同的配置组"
        )
        help_label.setWordWrap(True)
        help_label.setStyleSheet("QLabel { color: #666; margin: 5px; }")
        pid_gid_layout.addWidget(help_label)
        
        # Text area
        self.pid_gid_textbox = QTextEdit()
        self.pid_gid_textbox.setMinimumHeight(150)
        self.pid_gid_textbox.setFont(QFont("Consolas", 9))
        pid_gid_layout.addWidget(self.pid_gid_textbox)
        
        # Populate existing data
        self.populate_pid_gid_textbox()
        
        self.scroll_layout.addWidget(pid_gid_group)
    
    def populate_pid_gid_textbox(self):
        """填充PID/GID文本框"""
        pairs_list = self.file_config_copy.get("pairs", [])
        initial_text = ""
        
        # Group by override values
        grouped_by_override = {}
        for pair in pairs_list:
            override_key = str(pair.get("overrides", {}))
            if override_key not in grouped_by_override:
                grouped_by_override[override_key] = []
            grouped_by_override[override_key].append(pair)
        
        # Generate text
        for override_key, pairs in grouped_by_override.items():
            for pair in pairs:
                initial_text += f"{pair['pid']},{pair['gid']}\n"
            
            # Add override values
            if pairs and pairs[0].get("overrides"):
                for key, value in pairs[0]["overrides"].items():
                    initial_text += f"{key}={value}\n"
            
            initial_text += "\n"  # Empty line between groups
        
        self.pid_gid_textbox.setPlainText(initial_text.strip())
    
    def create_advanced_settings_section(self):
        """创建高级设置区域"""
        advanced_group = QGroupBox("高级设置")
        advanced_layout = QVBoxLayout(advanced_group)
        
        # Get advanced settings
        advanced_settings = self.file_config_copy.get("advanced_settings", {})
        
        # Enable/disable logic
        self.enable_disable_checkbox = UIFactory.create_checkbox(
            text="启用旧服务器禁用逻辑",
            checked=advanced_settings.get("enable_disable_logic", DEFAULT_ENABLE_DISABLE),
            tooltip="启用旧版本服务器的禁用逻辑处理"
        )
        advanced_layout.addWidget(self.enable_disable_checkbox)
        
        # Disable days cutoff
        cutoff_layout = QHBoxLayout()
        cutoff_layout.addWidget(QLabel("禁用天数阈值:"))
        
        self.cutoff_entry = QLineEdit()
        self.cutoff_entry.setMaximumWidth(100)
        self.cutoff_entry.setText(str(advanced_settings.get("disable_days_cutoff", DEFAULT_DAYS_CUTOFF)))
        cutoff_layout.addWidget(self.cutoff_entry)
        cutoff_layout.addStretch()
        
        advanced_layout.addLayout(cutoff_layout)
        
        # Disable conditions
        advanced_layout.addWidget(QLabel("禁用条件 (每行一个键=值):"))
        
        self.conditions_textbox = QTextEdit()
        self.conditions_textbox.setMaximumHeight(80)
        self.conditions_textbox.setFont(QFont("Consolas", 9))
        self.conditions_textbox.setPlainText(
            advanced_settings.get("disable_conditions_str", DEFAULT_CONDITIONS_STR)
        )
        advanced_layout.addWidget(self.conditions_textbox)
        
        # Find server days offset
        offset_layout = QHBoxLayout()
        offset_layout.addWidget(QLabel("查找服务器天数偏移:"))
        
        self.offset_entry = QLineEdit()
        self.offset_entry.setMaximumWidth(100)
        self.offset_entry.setText(str(advanced_settings.get("find_server_days_offset", DEFAULT_FIND_OFFSET)))
        offset_layout.addWidget(self.offset_entry)
        offset_layout.addStretch()
        
        advanced_layout.addLayout(offset_layout)
        
        # GameClient scheduled task settings
        gc_group = QGroupBox("GameClient定时任务")
        gc_layout = QVBoxLayout(gc_group)
        
        self.gc_enabled_checkbox = UIFactory.create_checkbox(
            text="启用GameClient定时任务",
            checked=advanced_settings.get("schedule_gameclient_enabled", DEFAULT_GC_SCHEDULE_ENABLED),
            tooltip="启用GameClient的定时任务功能"
        )
        gc_layout.addWidget(self.gc_enabled_checkbox)
        
        # Time and action settings
        gc_settings_layout = QHBoxLayout()
        
        gc_settings_layout.addWidget(QLabel("执行时间:"))
        self.gc_time_entry = TimeInputWidget(
            default_time=advanced_settings.get("schedule_gameclient_time", DEFAULT_GC_SCHEDULE_TIME)
        )
        self.gc_time_entry.setToolTip("设置GameClient定时任务的执行时间（24小时制）")
        gc_settings_layout.addWidget(self.gc_time_entry)
        
        gc_settings_layout.addWidget(QLabel("动作:"))
        self.gc_action_combobox = QComboBox()
        self.gc_action_combobox.addItems(["打开", "关闭", "重启"])
        self.gc_action_combobox.setCurrentText(
            advanced_settings.get("schedule_gameclient_action", DEFAULT_GC_SCHEDULE_ACTION)
        )
        gc_settings_layout.addWidget(self.gc_action_combobox)
        gc_settings_layout.addStretch()
        
        gc_layout.addLayout(gc_settings_layout)
        advanced_layout.addWidget(gc_group)
        
        self.scroll_layout.addWidget(advanced_group)
    
    def create_buttons(self, main_layout):
        """创建按钮"""
        button_layout = QHBoxLayout()
        
        # Save button
        save_button = QPushButton("保存并关闭")
        save_button.setMinimumWidth(120)
        save_button.clicked.connect(self.save_and_close)
        button_layout.addWidget(save_button)
        
        button_layout.addStretch()
        
        # Cancel button
        cancel_button = QPushButton("取消")
        cancel_button.setMinimumWidth(80)
        cancel_button.clicked.connect(self.reject)
        button_layout.addWidget(cancel_button)
        
        main_layout.addLayout(button_layout)
    
    def center_dialog(self):
        """居中对话框"""
        if self.parent():
            parent_rect = self.parent().geometry()
            dialog_rect = self.geometry()
            
            x = parent_rect.x() + (parent_rect.width() - dialog_rect.width()) // 2
            y = parent_rect.y() + (parent_rect.height() - dialog_rect.height()) // 2
            
            self.move(x, y)
    
    def save_and_close(self):
        """保存并关闭"""
        try:
            # Parse PID/GID configuration
            new_pairs_list = self.parse_pid_gid_input()
            
            # Collect advanced settings
            new_advanced_settings = {}
            
            # Process enable/disable logic
            new_advanced_settings["enable_disable_logic"] = self.enable_disable_checkbox.isChecked()
            
            # Process disable days cutoff
            days_str = self.cutoff_entry.text().strip()
            if not days_str:
                days_str = str(DEFAULT_DAYS_CUTOFF)
            try:
                days = int(days_str.replace(',', ''))  # Remove possible thousand separators
                if days < 0:
                    raise ValueError("天数不能为负数")
                new_advanced_settings["disable_days_cutoff"] = days
            except ValueError as e:
                QMessageBox.critical(self, "高级设置错误", f"禁用天数阈值必须是有效的整数: '{days_str}'")
                return
            
            # Process disable conditions
            new_advanced_settings["disable_conditions_str"] = self.conditions_textbox.toPlainText().strip()
            
            # Process find server offset
            offset_str = self.offset_entry.text().strip()
            valid_offset_ints = []
            if offset_str:
                parts = offset_str.split(',')
                for part in parts:
                    part_stripped = part.strip()
                    if not part_stripped:
                        continue
                    try:
                        offset_int = int(part_stripped)
                        valid_offset_ints.append(offset_int)
                    except ValueError:
                        QMessageBox.critical(self, "高级设置错误", f"查找服务器偏移量包含无效整数: '{part_stripped}'")
                        return
            new_advanced_settings["find_server_days_offset"] = ','.join(map(str, valid_offset_ints))
            
            # Process GameClient settings
            new_advanced_settings["schedule_gameclient_enabled"] = self.gc_enabled_checkbox.isChecked()
            new_advanced_settings["schedule_gameclient_time"] = self.gc_time_entry.get_time() or DEFAULT_GC_SCHEDULE_TIME
            new_advanced_settings["schedule_gameclient_action"] = self.gc_action_combobox.currentText() or DEFAULT_GC_SCHEDULE_ACTION
            
            # Update configuration
            self.app_config.update_file_config(self.file_path, {
                "pairs": new_pairs_list,
                "advanced_settings": new_advanced_settings
            })
            
            # Update GameClient schedule
            if hasattr(self.parent_app, 'update_gameclient_schedule'):
                self.parent_app.update_gameclient_schedule(self.file_path, new_advanced_settings)
            
            # Save configuration
            self.app_config.save_config()
            
            if hasattr(self.parent_app, 'log'):
                self.parent_app.log(f"已保存文件 {os.path.basename(self.file_path)} 的配置")
            
            self.accept()
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"保存配置时出错: {e}")
    
    def parse_pid_gid_input(self):
        """解析PID/GID输入 - 使用与原始版本相同的按块解析逻辑"""
        raw_pid_gid_text = self.pid_gid_textbox.toPlainText().strip()
        
        # Split by empty lines into blocks (same logic as original version)
        blocks = re.split(r'\n\s*\n+', raw_pid_gid_text)
        new_pairs_list = []
        
        for block_index, block in enumerate(blocks):
            block = block.strip()
            if not block:
                continue
            
            lines = [line.strip() for line in block.splitlines() if line.strip()]
            if not lines:
                continue
            
            current_pid_gids = []
            current_overrides = {}
            parsing_overrides = False
            
            for i, line in enumerate(lines):
                is_pid_gid = False
                
                # Try to parse PID,GID line
                try:
                    pid_parts = [p.strip() for p in line.split(',')]
                    if len(pid_parts) == 2:
                        pid = int(pid_parts[0])
                        gid = int(pid_parts[1])
                        if parsing_overrides:
                            raise ValueError("PID,GID 行不能出现在 键=值 行之后")
                        current_pid_gids.append((pid, gid))
                        is_pid_gid = True
                except ValueError:
                    pass
                
                # If not PID,GID line, try to parse as override value
                if not is_pid_gid:
                    try:
                        if '=' not in line:
                            raise ValueError("行既不是 PID,GID 也不是有效的 键=值")
                        key, value = line.split('=', 1)
                        key = key.strip()
                        value = value.strip()
                        if not key:
                            raise ValueError("键不能为空")
                        if not current_pid_gids:
                            raise ValueError(f"键=值 行 '{line}' 必须出现在至少一个 PID,GID 行之后")
                        current_overrides[key] = value
                        parsing_overrides = True
                    except ValueError as e:
                        # In modular version, we log errors but don't show message boxes, skip invalid lines
                        print(f"解析错误 - 第 {block_index + 1} 组配置的第 {i + 1} 行格式错误: '{line}' - {e}")
                        continue
            
            # Check if there are valid PID,GID pairs
            if not current_pid_gids:
                print(f"解析警告 - 第 {block_index + 1} 组配置缺少有效的 PID,GID 行")
                continue
            
            # Create configuration items for each PID,GID pair
            for pid, gid in current_pid_gids:
                new_pairs_list.append({
                    "pid": pid,
                    "gid": gid,
                    "overrides": current_overrides.copy()
                })
        
        return new_pairs_list


class CloseBehaviorDialog(QDialog):
    """关闭行为设置对话框"""

    def __init__(self, parent):
        super().__init__(parent)
        self.parent_app = parent

        # Setup window
        self.setWindowTitle("关闭行为设置")
        self.setModal(True)
        self.setFixedSize(350, 380)

        # Create interface
        self.create_widgets()

        # Center dialog
        QTimer.singleShot(100, self.center_dialog)

    def create_widgets(self):
        """创建界面组件"""
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(15)

        # Title
        title_label = QLabel("设置关闭窗口行为")
        title_font = QFont()
        title_font.setPointSize(14)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        main_layout.addWidget(title_label)

        # Description
        description_label = QLabel("选择当点击关闭按钮时的默认行为：")
        description_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        main_layout.addWidget(description_label)

        # Options group
        options_group = QGroupBox()
        options_layout = QVBoxLayout(options_group)

        # Radio button group
        self.behavior_group = QButtonGroup()

        # Ask option
        self.ask_radio = QRadioButton("每次询问")
        self.behavior_group.addButton(self.ask_radio, 0)
        options_layout.addWidget(self.ask_radio)

        # Exit option
        self.exit_radio = QRadioButton("直接退出程序")
        self.behavior_group.addButton(self.exit_radio, 1)
        options_layout.addWidget(self.exit_radio)

        # Minimize option
        self.minimize_radio = QRadioButton("最小化到系统托盘")
        self.behavior_group.addButton(self.minimize_radio, 2)
        options_layout.addWidget(self.minimize_radio)

        # Set current behavior
        current_behavior = getattr(self.parent_app, 'close_behavior', 'ask')
        if current_behavior == "ask":
            self.ask_radio.setChecked(True)
        elif current_behavior == "exit":
            self.exit_radio.setChecked(True)
        elif current_behavior == "minimize":
            self.minimize_radio.setChecked(True)
        else:
            self.ask_radio.setChecked(True)  # Default

        main_layout.addWidget(options_group)

        # Remember choice
        self.remember_checkbox = UIFactory.create_checkbox(
            text="记住我的选择",
            checked=getattr(self.parent_app, 'remember_close_choice', False),
            tooltip="记住选择的关闭行为，下次不再询问"
        )
        main_layout.addWidget(self.remember_checkbox)

        # Buttons
        button_layout = QHBoxLayout()

        save_button = QPushButton("保存")
        save_button.setMinimumWidth(100)
        save_button.clicked.connect(self.save_settings)
        button_layout.addWidget(save_button)

        button_layout.addStretch()

        cancel_button = QPushButton("取消")
        cancel_button.setMinimumWidth(100)
        cancel_button.clicked.connect(self.reject)
        button_layout.addWidget(cancel_button)

        main_layout.addLayout(button_layout)

    def center_dialog(self):
        """居中对话框"""
        if self.parent():
            parent_rect = self.parent().geometry()
            dialog_rect = self.geometry()

            x = parent_rect.x() + (parent_rect.width() - dialog_rect.width()) // 2
            y = parent_rect.y() + (parent_rect.height() - dialog_rect.height()) // 2

            self.move(x, y)

    def save_settings(self):
        """保存设置"""
        # Determine selected behavior
        if self.ask_radio.isChecked():
            behavior = "ask"
        elif self.exit_radio.isChecked():
            behavior = "exit"
        elif self.minimize_radio.isChecked():
            behavior = "minimize"
        else:
            behavior = "ask"  # Default

        # Save to parent app
        self.parent_app.close_behavior = behavior
        self.parent_app.remember_close_choice = self.remember_checkbox.isChecked()

        # Save to configuration
        if hasattr(self.parent_app, 'config'):
            self.parent_app.config.set_config_value("close_behavior", behavior)
            self.parent_app.config.set_config_value("remember_close_choice", self.remember_checkbox.isChecked())
            self.parent_app.config.save_config()

        if hasattr(self.parent_app, 'log'):
            self.parent_app.log("关闭行为设置已保存")

        self.accept()


class SystemSettingsDialog(QDialog):
    """系统设置对话框"""

    def __init__(self, parent):
        super().__init__(parent)
        self.parent_app = parent

        # Setup window
        self.setWindowTitle("系统设置")
        self.setModal(True)
        self.setFixedSize(500, 650)

        # Create interface
        self.create_widgets()

        # Center dialog
        QTimer.singleShot(100, self.center_dialog)

    def create_widgets(self):
        """创建界面组件"""
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(20)

        # Title
        title_label = QLabel("系统设置")
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        main_layout.addWidget(title_label)

        # Startup settings group
        startup_group = QGroupBox("启动设置")
        startup_layout = QVBoxLayout(startup_group)

        self.startup_checkbox = UIFactory.create_checkbox(
            text="开机自动启动",
            tooltip="程序将在Windows启动时自动运行"
        )
        startup_layout.addWidget(self.startup_checkbox)

        self.run_in_tray_checkbox = UIFactory.create_checkbox(
            text="启动时最小化到托盘",
            tooltip="程序启动后直接最小化到系统托盘"
        )
        startup_layout.addWidget(self.run_in_tray_checkbox)

        main_layout.addWidget(startup_group)

        # Close behavior group
        close_group = QGroupBox("关闭行为")
        close_layout = QVBoxLayout(close_group)

        # Radio button group
        self.behavior_group = QButtonGroup()

        # Ask option
        self.ask_radio = QRadioButton("每次询问")
        self.behavior_group.addButton(self.ask_radio, 0)
        close_layout.addWidget(self.ask_radio)

        # Exit option
        self.exit_radio = QRadioButton("直接退出程序")
        self.behavior_group.addButton(self.exit_radio, 1)
        close_layout.addWidget(self.exit_radio)

        # Minimize option
        self.minimize_radio = QRadioButton("最小化到系统托盘")
        self.behavior_group.addButton(self.minimize_radio, 2)
        close_layout.addWidget(self.minimize_radio)

        # Remember choice
        self.remember_checkbox = UIFactory.create_checkbox(
            text="记住我的选择",
            tooltip="记住选择的关闭行为，下次不再询问"
        )
        close_layout.addWidget(self.remember_checkbox)

        main_layout.addWidget(close_group)

        # Time sync group
        time_group = QGroupBox("时间同步")
        time_layout = QVBoxLayout(time_group)

        self.auto_sync_checkbox = UIFactory.create_checkbox(
            text="定时任务执行前自动同步系统时间",
            tooltip="在执行定时检查任务前自动同步系统时间"
        )
        time_layout.addWidget(self.auto_sync_checkbox)

        main_layout.addWidget(time_group)

        # Log settings group
        log_group = QGroupBox("日志设置")
        log_layout = QVBoxLayout(log_group)

        # Log level filter
        log_level_label = QLabel("日志级别过滤：")
        log_layout.addWidget(log_level_label)

        # Create checkboxes for log levels
        self.log_level_checkboxes = {}
        log_levels = ["INFO", "SUCCESS", "WARNING", "ERROR", "DEBUG"]
        
        level_layout = QHBoxLayout()
        for level in log_levels:
            checkbox = UIFactory.create_checkbox(
                text=level,
                checked=level != "DEBUG",  # Default: exclude DEBUG
                tooltip=f"显示 {level} 级别的日志"
            )
            self.log_level_checkboxes[level] = checkbox
            level_layout.addWidget(checkbox)
        log_layout.addLayout(level_layout)

        # Batch update settings
        self.batch_update_checkbox = UIFactory.create_checkbox(
            text="启用日志批量更新（提升性能）",
            checked=True,
            tooltip="将多条日志合并后更新，减少UI刷新频率"
        )
        log_layout.addWidget(self.batch_update_checkbox)

        # Batch size
        batch_size_layout = QHBoxLayout()
        batch_size_label = QLabel("批量大小：")
        batch_size_layout.addWidget(batch_size_label)
        
        self.batch_size_spinbox = QSpinBox()
        self.batch_size_spinbox.setRange(5, 50)
        self.batch_size_spinbox.setValue(10)
        self.batch_size_spinbox.setSuffix(" 条")
        self.batch_size_spinbox.setToolTip("每批次处理的日志条数")
        batch_size_layout.addWidget(self.batch_size_spinbox)
        
        batch_size_layout.addStretch()
        log_layout.addLayout(batch_size_layout)

        main_layout.addWidget(log_group)

        # Buttons
        button_layout = QHBoxLayout()

        save_button = QPushButton("保存")
        save_button.setMinimumWidth(100)
        save_button.clicked.connect(self.save_settings)
        button_layout.addWidget(save_button)

        button_layout.addStretch()

        cancel_button = QPushButton("取消")
        cancel_button.setMinimumWidth(100)
        cancel_button.clicked.connect(self.cancel_settings)
        button_layout.addWidget(cancel_button)

        main_layout.addLayout(button_layout)

    def center_dialog(self):
        """居中对话框"""
        if self.parent():
            parent_rect = self.parent().geometry()
            dialog_rect = self.geometry()

            x = parent_rect.x() + (parent_rect.width() - dialog_rect.width()) // 2
            y = parent_rect.y() + (parent_rect.height() - dialog_rect.height()) // 2

            self.move(x, y)

    def set_startup_enabled(self, enabled: bool):
        """设置开机启动状态"""
        self.startup_checkbox.setChecked(enabled)

    def set_close_behavior(self, behavior: str):
        """设置关闭行为"""
        if behavior == "ask":
            self.ask_radio.setChecked(True)
        elif behavior == "exit":
            self.exit_radio.setChecked(True)
        elif behavior == "minimize":
            self.minimize_radio.setChecked(True)
        else:
            self.ask_radio.setChecked(True)  # Default

    def set_remember_choice(self, remember: bool):
        """设置记住选择"""
        self.remember_checkbox.setChecked(remember)

    def set_run_in_tray(self, run_in_tray: bool):
        """设置启动时最小化到托盘"""
        self.run_in_tray_checkbox.setChecked(run_in_tray)

    def set_auto_sync_time(self, auto_sync: bool):
        """设置自动同步时间"""
        self.auto_sync_checkbox.setChecked(auto_sync)

    def set_log_level_filter(self, levels: list):
        """设置日志级别过滤"""
        for level, checkbox in self.log_level_checkboxes.items():
            checkbox.setChecked(level in levels)
    
    def set_log_batch_update(self, enabled: bool):
        """设置日志批量更新"""
        self.batch_update_checkbox.setChecked(enabled)
    
    def set_log_batch_size(self, size: int):
        """设置日志批量大小"""
        self.batch_size_spinbox.setValue(size)

    def get_settings(self) -> dict:
        """获取设置值"""
        try:
            # Determine selected behavior
            behavior = "ask"  # Default value
            if hasattr(self, 'ask_radio') and self.ask_radio.isChecked():
                behavior = "ask"
            elif hasattr(self, 'exit_radio') and self.exit_radio.isChecked():
                behavior = "exit"
            elif hasattr(self, 'minimize_radio') and self.minimize_radio.isChecked():
                behavior = "minimize"

            # Get selected log levels
            selected_log_levels = []
            if hasattr(self, 'log_level_checkboxes'):
                for level, checkbox in self.log_level_checkboxes.items():
                    if checkbox.isChecked():
                        selected_log_levels.append(level)

            return {
                'startup_enabled': self.startup_checkbox.isChecked() if hasattr(self, 'startup_checkbox') else False,
                'close_behavior': behavior,
                'remember_choice': self.remember_checkbox.isChecked() if hasattr(self, 'remember_checkbox') else False,
                'run_in_tray': self.run_in_tray_checkbox.isChecked() if hasattr(self, 'run_in_tray_checkbox') else False,
                'auto_sync_time': self.auto_sync_checkbox.isChecked() if hasattr(self, 'auto_sync_checkbox') else False,
                'log_level_filter': selected_log_levels,
                'log_batch_update': self.batch_update_checkbox.isChecked() if hasattr(self, 'batch_update_checkbox') else True,
                'log_batch_size': self.batch_size_spinbox.value() if hasattr(self, 'batch_size_spinbox') else 10
            }
        except Exception as e:
            # 返回默认设置值
            return {
                'startup_enabled': False,
                'close_behavior': 'ask',
                'remember_choice': False,
                'run_in_tray': False,
                'auto_sync_time': False,
                'log_level_filter': [],
                'log_batch_update': True,
                'log_batch_size': 10
            }

    def save_settings(self):
        """保存设置"""
        try:
            # 验证设置值
            settings = self.get_settings()
            if settings:
                self.accept()
            else:
                # 如果获取设置失败，显示错误消息
                from PyQt6.QtWidgets import QMessageBox
                QMessageBox.warning(self, "错误", "获取设置值失败，请检查输入")
        except Exception as e:
            # 记录错误并显示消息
            from PyQt6.QtWidgets import QMessageBox
            QMessageBox.critical(self, "错误", f"保存设置时出错: {e}")

    def cancel_settings(self):
        """取消设置"""
        try:
            self.reject()
        except Exception as e:
            # 强制关闭对话框
            self.close()

    def reject(self):
        """重写reject方法，确保安全关闭"""
        try:
            super().reject()
        except Exception as e:
            # 如果正常关闭失败，强制关闭
            self.close()

    def closeEvent(self, event):
        """处理关闭事件"""
        try:
            # 确保对话框能够正常关闭
            event.accept()
        except Exception as e:
            # 强制接受关闭事件
            event.accept()
