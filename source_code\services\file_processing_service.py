"""
文件处理服务模块
从主应用类中提取的文件处理逻辑，重构为独立的服务类
"""

import os
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import List, Dict, Any, Optional, Callable
from PyQt6.QtCore import QObject, pyqtSignal

from utils.pyqt6_threading import SafeUIUpdater
from utils.performance_optimizer import monitor_performance
from utils.smart_logger import create_debug_logger_for_class
from core.constants import TARGET_URL_TEMPLATE


@create_debug_logger_for_class
class FileProcessingService(QObject):
    """文件处理服务类 - 负责文件检查和更新的核心逻辑"""
    
    # 信号定义
    progress_updated = pyqtSignal(int, str)  # 进度百分比, 状态消息
    file_processed = pyqtSignal(str, dict)   # 文件路径, 处理结果
    processing_completed = pyqtSignal(list)  # 所有处理结果
    
    def __init__(self, app_instance):
        super().__init__()
        self.app = app_instance
        self.cancel_event = None
        
    def set_cancel_event(self, cancel_event: threading.Event):
        """设置取消事件"""
        self.cancel_event = cancel_event
    
    def _log(self, message: str, level: str = "INFO"):
        """内部日志方法"""
        if hasattr(self.app, 'log'):
            self.app.log(message, level)
    
    @monitor_performance('file_processing_batch')
    def process_files(self, selected_files: List[str]) -> List[Dict[str, Any]]:
        """
        批量处理文件的主方法
        
        Args:
            selected_files: 选中的文件路径列表
            
        Returns:
            处理结果列表
        """
        total_files = len(selected_files)
        results = []
        
        self._log(f"开始批量处理 {total_files} 个文件", "INFO")
        
        try:
            # 动态计算线程数（基于CPU核心数和文件数量）
            max_workers = min(max(1, os.cpu_count() or 1), 3, total_files)
            
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                # 提交所有任务
                future_to_file = {
                    executor.submit(self._process_single_file, file_path): file_path
                    for file_path in selected_files
                }
                
                # 处理完成的任务
                for i, future in enumerate(as_completed(future_to_file)):
                    if self.cancel_event and self.cancel_event.is_set():
                        self._log("处理被用户取消", "WARNING")
                        break
                    
                    file_path = future_to_file[future]
                    try:
                        result = future.result()
                        results.append(result)
                        
                        # 发射文件处理完成信号
                        self.file_processed.emit(file_path, result)
                        
                        # 更新进度
                        progress = int((i + 1) / total_files * 100)
                        status_msg = f"已处理 {i + 1}/{total_files} 个文件"
                        self.progress_updated.emit(progress, status_msg)
                        
                    except Exception as e:
                        self._log(f"处理文件 {os.path.basename(file_path)} 时出错: {e}", "ERROR")
                        results.append({
                            "file": file_path,
                            "success": False,
                            "error": str(e)
                        })
        
        except Exception as e:
            self._log(f"批量处理过程中发生错误: {e}", "ERROR")
        
        # 发射处理完成信号
        self.processing_completed.emit(results)
        return results
    
    @monitor_performance('single_file_processing')
    def _process_single_file(self, file_path: str) -> Dict[str, Any]:
        """
        处理单个文件的核心方法
        
        Args:
            file_path: 文件路径
            
        Returns:
            处理结果字典
        """
        file_name = os.path.basename(file_path)
        self._log(f"开始处理: {file_name}", "INFO")
        
        try:
            # 第1步：验证文件
            self._log(f"步骤1: 验证文件 {file_name}", "DEBUG")
            validation_result = self._validate_file(file_path)
            if not validation_result['valid']:
                self._log(f"文件验证失败: {validation_result.get('error', '未知错误')}", "ERROR")
                return validation_result
            
            # 第2步：获取文件配置
            self._log(f"步骤2: 获取文件配置 {file_name}", "DEBUG")
            if not hasattr(self.app, 'config') or not self.app.config:
                self._log("应用配置对象未初始化", "ERROR")
                return {"file": file_path, "success": False, "error": "应用配置未初始化"}
                
            file_config = self.app.config.get_file_config(file_path)
            pairs_for_file = file_config.get("pairs", [])
            
            if not pairs_for_file:
                self._log(f"{file_name} 未配置PID/GID对，跳过", "INFO")
                return {"file": file_path, "success": True, "updated": False}
            
            self._log(f"找到 {len(pairs_for_file)} 个PID/GID对", "DEBUG")
            
            # 第3步：获取高级设置
            self._log(f"步骤3: 获取高级设置 {file_name}", "DEBUG")
            advanced_settings = file_config.get("advanced_settings", {})
            
            # 第4步：收集API数据
            self._log(f"步骤4: 收集API数据 {file_name}", "DEBUG")
            if not hasattr(self.app, 'network_service') or not self.app.network_service:
                self._log("网络服务未初始化", "ERROR")
                return {"file": file_path, "success": False, "error": "网络服务未初始化"}
                
            api_data_result = self._collect_api_data(file_path, pairs_for_file)
            if not api_data_result['success']:
                error_msg = api_data_result.get('error', 'API数据收集失败')
                self._log(f"API数据收集失败: {error_msg}", "ERROR")
                return api_data_result
            
            all_api_data = api_data_result['api_data']
            self._log(f"成功收集 {len(all_api_data)} 组API数据", "DEBUG")
            
            # 第5步：查找目标服务器
            self._log(f"步骤5: 查找目标服务器 {file_name}", "DEBUG")
            if not hasattr(self.app, 'helpers') or not self.app.helpers:
                self._log("辅助工具未初始化", "ERROR")
                return {"file": file_path, "success": False, "error": "辅助工具未初始化"}
                
            servers_result = self._find_target_servers(file_path, pairs_for_file, all_api_data, advanced_settings)
            servers_to_add = servers_result['servers']
            self._log(f"找到 {len(servers_to_add)} 个目标服务器", "DEBUG")
            
            # 第6步：处理旧服务器禁用
            self._log(f"步骤6: 处理旧服务器禁用 {file_name}", "DEBUG")
            changes_made = self._handle_old_server_disable(file_path, all_api_data, advanced_settings)
            
            # 第7步：添加新服务器
            if servers_to_add:
                self._log(f"步骤7: 添加 {len(servers_to_add)} 个新服务器", "DEBUG")
                add_result = self._add_new_servers(file_path, servers_to_add, all_api_data, advanced_settings)
                if add_result['success']:
                    changes_made = True
                else:
                    self._log(f"添加服务器失败: {add_result.get('error', '未知错误')}", "WARNING")
            else:
                self._log("没有找到需要添加的服务器", "DEBUG")
            
            self._log(f"处理完成: {file_name}, 有更改: {changes_made}", "INFO")
            return {"file": file_path, "success": True, "updated": changes_made}
            
        except AttributeError as e:
            error_msg = f"属性错误（可能是依赖未初始化）: {e}"
            self._log(f"{file_name} - {error_msg}", "ERROR")
            return {"file": file_path, "success": False, "error": error_msg}
        except Exception as e:
            self._log(f"{file_name} - 处理失败: {e}", "ERROR")
            import traceback
            self._log(f"详细错误信息: {traceback.format_exc()}", "DEBUG")
            return {"file": file_path, "success": False, "error": str(e)}
    
    def _validate_file(self, file_path: str) -> Dict[str, Any]:
        """验证文件路径和可读性"""
        file_name = os.path.basename(file_path)
        
        # 验证文件路径参数
        if not file_path or not isinstance(file_path, str):
            return {"file": file_path, "success": False, "error": "无效的文件路径参数", "valid": False}
        
        # 规范化文件路径
        try:
            normalized_path = os.path.normpath(os.path.abspath(file_path))
            self.debug_log("规范化后的文件路径: {}", normalized_path)
            file_path = normalized_path
        except Exception as e:
            return {"file": file_path, "success": False, "error": f"文件路径规范化失败: {e}", "valid": False}
        
        # 检查文件是否存在
        if not os.path.exists(file_path):
            return {"file": file_path, "success": False, "error": "文件不存在", "valid": False}
        
        # 验证文件是否可读
        if not os.access(file_path, os.R_OK):
            return {"file": file_path, "success": False, "error": "文件无读取权限", "valid": False}
        
        return {"valid": True, "file_path": file_path}
    
    def _collect_api_data(self, file_path: str, pairs_for_file: List[Dict]) -> Dict[str, Any]:
        """收集所有需要的API数据"""
        file_name = os.path.basename(file_path)
        all_api_data = {}
        total_pairs = len(pairs_for_file)
        processed_pairs = 0
        failed_pairs = 0
        
        for pair_info in pairs_for_file:
            if self.cancel_event and self.cancel_event.is_set():
                return {"success": False, "cancelled": True}
            
            pid = pair_info["pid"]
            gid = pair_info["gid"]
            
            try:
                # 验证PID和GID参数
                if not isinstance(pid, (int, str)) or not isinstance(gid, (int, str)):
                    self.debug_log("{} - PID/GID参数无效: {},{}", file_name, pid, gid)
                    failed_pairs += 1
                    continue
                
                # 转换为字符串并验证
                pid_str = str(pid).strip()
                gid_str = str(gid).strip()
                
                if not pid_str or not gid_str:
                    failed_pairs += 1
                    continue
                
                # 构建URL
                url = TARGET_URL_TEMPLATE.format(pid=pid_str, gid=gid_str)
                self.debug_log("API URL: {}", url)
                
                # 获取服务器数据
                server_list_data = self.app.network_service.fetch_server_data(url)
                all_api_data[(pid, gid)] = server_list_data
                
                if not server_list_data:
                    failed_pairs += 1
                    continue
                
                processed_pairs += 1
                
            except Exception as e:
                self.debug_log("{} - 获取API数据失败: {}", file_name, e)
                failed_pairs += 1
                continue
        
        # 汇总API获取结果
        if failed_pairs > 0:
            self._log(f"{file_name} - API获取: 成功 {processed_pairs}/{total_pairs} 对", "INFO")
        
        return {"success": True, "api_data": all_api_data}
    
    def _find_target_servers(self, file_path: str, pairs_for_file: List[Dict], 
                           all_api_data: Dict, advanced_settings: Dict) -> Dict[str, Any]:
        """查找目标服务器"""
        file_name = os.path.basename(file_path)
        servers_to_add = []
        
        for pair_info in pairs_for_file:
            if self.cancel_event and self.cancel_event.is_set():
                break
            
            pid = pair_info["pid"]
            gid = pair_info["gid"]
            overrides = pair_info.get("overrides", {})
            
            server_list_data = all_api_data.get((pid, gid), [])
            if not server_list_data:
                continue
            
            # 确保server_list_data是列表格式
            if isinstance(server_list_data, dict):
                server_list_data = [server_list_data]
            elif not isinstance(server_list_data, list):
                continue
            
            # 特殊处理：蓬莱大区过滤
            if pid == 1 and gid == 1008142:
                original_count = len(server_list_data)
                server_list_data = [
                    s for s in server_list_data
                    if isinstance(s, dict) and "蓬莱" in s.get("zoneName", "")
                ]
                filtered_count = len(server_list_data)
                self.debug_log("{} - 蓬莱过滤: {}/{}", file_name, filtered_count, original_count)
                
                if not server_list_data:
                    continue
            
            # 解析天数偏移量配置
            offset_str = advanced_settings.get("find_server_days_offset", "0")
            valid_offsets = []
            if offset_str:
                parts = offset_str.split(',')
                for part in parts:
                    part_stripped = part.strip()
                    if not part_stripped:
                        continue
                    try:
                        offset_int = int(part_stripped)
                        valid_offsets.append(offset_int)
                    except ValueError:
                        self.debug_log("忽略无效的偏移量值: '{}'", part_stripped)
            
            if not valid_offsets:
                self.debug_log("{} - 偏移量配置无效，跳过", file_name)
                continue
            
            # 根据天数偏移量查找目标服务器
            found_count = 0
            for offset in valid_offsets:
                if self.cancel_event and self.cancel_event.is_set():
                    break
                
                target_server_id = self.app.helpers.find_target_server(server_list_data, offset)
                if target_server_id:
                    # 检查是否已在待添加列表中
                    if not any(s[2] == target_server_id for s in servers_to_add if s[0]==pid and s[1]==gid):
                        servers_to_add.append((pid, gid, target_server_id, overrides))
                        found_count += 1
        
        return {"servers": servers_to_add}
    
    def _handle_old_server_disable(self, file_path: str, all_api_data: Dict, advanced_settings: Dict) -> bool:
        """处理旧服务器禁用逻辑"""
        if self.cancel_event and self.cancel_event.is_set():
            return False
        
        enable_disable = advanced_settings.get("enable_disable_logic", False)
        if enable_disable:
            days_cutoff = advanced_settings.get("disable_days_cutoff", 26)
            conditions_str = advanced_settings.get("disable_conditions_str", "")
            
            made_disable_changes = self.app.helpers.disable_old_servers(
                file_path, all_api_data, days_cutoff, conditions_str
            )
            if made_disable_changes:
                file_name = os.path.basename(file_path)
                self._log(f"{file_name} - 已禁用过期服务器", "INFO")
                return True
        
        return False
    
    def _add_new_servers(self, file_path: str, servers_to_add: List, all_api_data: Dict, advanced_settings: Dict) -> Dict[str, Any]:
        """添加新找到的服务器"""
        file_name = os.path.basename(file_path)
        
        # 预检查：验证服务器
        validated_servers = []
        for pid, gid, server_id, overrides in servers_to_add:
            if self.cancel_event and self.cancel_event.is_set():
                return {"success": False, "cancelled": True}
            
            server_data = all_api_data.get((pid, gid), [])
            if self.app.helpers.validate_server_for_ini_operation(
                server_data, server_id, days_cutoff=advanced_settings.get("disable_days_cutoff", 26)
            ):
                validated_servers.append((pid, gid, server_id, overrides))
            else:
                self.debug_log("服务器 {} 未通过验证", server_id)
        
        # 只对通过验证的服务器执行INI操作
        if validated_servers:
            added_count = 0
            for pid, gid, server_id, overrides in validated_servers:
                if self.cancel_event and self.cancel_event.is_set():
                    return {"success": False, "cancelled": True}
                
                # 更新INI文件
                update_success = self.app.helpers.update_ini_file(
                    file_path, server_id, pid, gid, {"server_id": server_id}, overrides
                )
                if update_success:
                    added_count += 1
            
            # 汇总添加结果
            if added_count > 0:
                self._log(f"{file_name} - 已添加 {added_count} 个服务器", "SUCCESS")
                return {"success": True, "added_count": added_count}
            else:
                self.debug_log("{} - 没有服务器通过验证", file_name)
                return {"success": False, "error": "没有服务器通过验证"}
        else:
            self._log(f"{file_name} - 未找到目标服务器", "INFO")
            return {"success": False, "error": "未找到目标服务器"} 