"""

游戏服务器检查与更新工具 - PyQt6版本主入口文件
重构后的模块化版本
"""

import sys
import os
from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QFont

# 核心模块导入
from core.app_pyqt6 import ServerCheckerApp
from utils.system import is_admin, setup_single_instance, request_admin_privileges
from gui.styles_pyqt6 import StyleManager


def setup_application():
    """设置应用程序属性"""
    # 使用新的DPI适配器设置高DPI支持
    from utils.dpi_adapter import setup_high_dpi_support
    setup_high_dpi_support()

    # 设置其他应用程序属性
    if hasattr(Qt.ApplicationAttribute, 'AA_DisableWindowContextHelpButton'):
        QApplication.setAttribute(Qt.ApplicationAttribute.AA_DisableWindowContextHelpButton, True)


def main():
    """主函数"""
    print("启动游戏服务器检查与更新工具 (PyQt6版本)...")
    
    # 单实例检查
    if not setup_single_instance():
        print("检测到程序已在运行，退出当前实例")
        sys.exit(0)
    
    # 管理员权限检查（Windows特定）
    if sys.platform == 'win32':
        if request_admin_privileges():
            # 如果请求了权限提升，当前进程会退出
            sys.exit(0)
    
    app = None
    main_window = None
    
    try:
        # Setup application
        setup_application()
        
        # 创建QApplication实例
        app = QApplication(sys.argv)
        app.setApplicationName("ServerCheckerTool")
        app.setApplicationVersion("2.0.0")
        app.setOrganizationName("ServerChecker")

        # 使用DPI适配器设置最优字体
        from utils.dpi_adapter import get_dpi_adapter
        import platform

        dpi_adapter = get_dpi_adapter()

        # 根据系统选择最佳字体
        if platform.system() == "Windows":
            font_family = "Microsoft YaHei UI"
        else:
            font_family = "Arial"

        # 获取DPI适配的字体
        font = dpi_adapter.get_optimal_font(font_family, 9)
        app.setFont(font)

        # 打印DPI调试信息
        dpi_adapter.print_debug_info()

        # 应用高DPI修复
        from utils.high_dpi_fix import setup_high_dpi_environment, get_dpi_info
        setup_high_dpi_environment()
        dpi_info = get_dpi_info()

        print(f"High DPI Fix Applied - Scale: {dpi_info['scale_factor']:.2f}, 4K: {dpi_info['is_4k']}")

        # 设置应用程序样式
        app.setStyle('Fusion')  # Use Fusion style for better cross-platform appearance
        StyleManager.apply_style(app)  # Apply custom styles

        print("创建主应用程序实例...")
        # 创建并显示主窗口
        main_window = ServerCheckerApp()
        main_window.show()

        # 应用全局DPI修复（在窗口显示后）
        from utils.high_dpi_fix import apply_global_dpi_fixes
        apply_global_dpi_fixes()

        print("启动应用程序主循环...")
        # 运行应用程序事件循环
        exit_code = app.exec()
        
        # 应用程序正常退出后的清理
        print("应用程序正常退出，开始清理...")
        
    except Exception as e:
        print(f"应用程序运行时出错: {e}")
        import traceback
        traceback.print_exc()
        exit_code = 1
        
    finally:
        # 确保所有资源都被正确清理
        try:
            if main_window:
                # 确保主窗口完全关闭
                main_window.close()
                main_window = None
            
            if app:
                # 处理所有待处理的事件
                app.processEvents()
                
                # 给Qt一些时间来完成所有清理工作
                import time
                time.sleep(0.1)
                
                # 删除应用程序实例
                app = None
            
            print("应用程序清理完成")
            
        except Exception as e:
            print(f"清理过程中出错: {e}")
        
        # 最终退出
        sys.exit(exit_code if 'exit_code' in locals() else 0)


if __name__ == "__main__":
    main()
