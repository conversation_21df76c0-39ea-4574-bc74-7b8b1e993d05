# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a **Game Server Checker and Update Tool** built with PyQt6 - a Windows desktop application for monitoring and managing game server configurations. The application provides automated server checking, configuration file management, and scheduled task functionality.

**Key Features:**
- Server status monitoring and automated checking
- Configuration file (.ini) generation and management  
- Scheduled task execution with time-based triggers
- File upload functionality with batch processing
- System tray integration with Windows notifications
- Multi-threaded architecture with Qt6 integration

## Architecture

### Core Structure
- **Entry Point**: `source_code/main_pyqt6.py` - Application bootstrap with DPI scaling and admin privileges
- **Main App**: `source_code/core/app_pyqt6.py` - Central application class (`ServerCheckerApp`)
- **Configuration**: `source_code/core/config.py` - Settings management via JSON config files
- **Constants**: `source_code/core/constants.py` - Global constants and API endpoints

### Module Organization

**Core Modules** (`source_code/core/`):
- `app_pyqt6.py` - Main application window and startup management
- `config.py` - Configuration loading/saving with `AppConfig` class
- `file_manager.py` - Server configuration file (.ini) generation
- `schedule_manager.py` - Task scheduling and automation
- `upload_manager.py` - File upload operations
- `ui_coordinator.py` - UI state management and coordination
- `event_handler.py` - Application event processing

**Services** (`source_code/services/`):
- `unified_network_service.py` - HTTP API calls with connection pooling and performance optimization
- `optimized_file_service.py` - File I/O operations with caching and batch processing
- `qt_scheduler_service.py` - Qt-based task scheduling with signal/slot integration  
- `qt_tray_service.py` - System tray integration with Windows notifications
- `configuration_service.py` - Advanced configuration management and validation
- `file_processing_service.py` - Specialized file processing and server validation

**GUI Components** (`source_code/gui/`):
- `components_pyqt6.py` - Custom PyQt6 widgets and controls
- `dialogs_pyqt6.py` - Modal dialogs and popups  
- `styles_pyqt6.py` - Application styling and theme management
- `ui_factory.py` - Widget factory patterns and DPI-aware component creation
- `ui_constants.py` - UI-related constants (sizes, spacing, colors)
- `time_input_widget.py` - Specialized time input component
- `custom_checkbox.py` - Enhanced checkbox component with additional features

**Utilities** (`source_code/utils/`):
- `performance_optimizer.py` - Caching, memory management, and performance monitoring
- `pyqt6_threading.py` - Thread-safe Qt operations and UI updates
- `dpi_adapter.py` + `high_dpi_fix.py` - High DPI display scaling support
- `system.py` - System-level operations (admin privileges, single instance, drive detection)
- `sequential_executor.py` - Ordered task execution with progress tracking
- `smart_logger.py` - Advanced logging with debug capabilities
- `startup_optimization.py` - Application startup performance optimization
- `windows_startup.py` - Windows registry integration for startup management
- `crash_fix.py` - System crash recovery and error handling
- `process_control.py` - External process management (GameClient integration)
- `lazy_import_manager.py` - Delayed module loading for faster startup
- `ui_optimizer.py` - UI performance optimization and batch processing

## Development Commands

### Build and Package
```bash
# Build executable with PyInstaller (optimized spec file)
pyinstaller GameServerChecker.spec --clean

# Quick build without cleaning cache
pyinstaller GameServerChecker.spec

# Build output location
dist/GameServerChecker.exe
```

### Testing and Validation
```bash
# Test module imports before building
cd source_code && python -c "from core.app_pyqt6 import ServerCheckerApp; print('Import successful')"

# Test PyQt6 availability
python -c "import PyQt6.QtWidgets; print('PyQt6 available')"

# Validate build dependencies
pip show PyQt6 pyinstaller
```

### Dependencies
```bash
# Install required packages
pip install -r requirements.txt
```

**Key Dependencies:**
- PyQt6 >= 6.9.0 (GUI framework) 
- pillow >= 10.0.0 (Image processing)
- psutil >= 5.9.0 (System monitoring)
- requests >= 2.25.0 (HTTP client)
- pywin32 >= 306 (Windows API integration)
- ntplib >= 0.4.0 (Time synchronization)
- chardet >= 5.0.0 (Character encoding detection)

### Running the Application
```bash
# Run from source
python source_code/main_pyqt6.py

# Run built executable  
dist/GameServerChecker.exe
```

## Technical Details

### API Integration
- **Target API**: `https://api.t5game.5jli.com/serverselect/loginserverdata/`
- **Parameters**: pid (default: 7), gid (default: 1003491), version, platCode
- **Purpose**: Fetch game server data for configuration generation

### Configuration Management
- **Config File**: `%LOCALAPPDATA%/ServerCheckerTool/app_config.json`
- **Server Files**: Generated `.ini` files with server parameters
- **Template**: Uses `CONTENT_TEMPLATE` from constants for server configuration

### Threading Architecture
- **Main Thread**: PyQt6 UI operations
- **Worker Threads**: Network requests, file operations, scheduled tasks
- **Thread Safety**: `PyQt6ThreadManager` and `SafeUIUpdater` for cross-thread communication

### Performance Optimizations
- **Icon Caching**: `IconCache` with LRU eviction
- **Memory Management**: Automatic cleanup and monitoring
- **Network Pool**: Connection pooling for HTTP requests
- **Sequential Execution**: `SequentialExecutor` for ordered task processing

## Development Guidelines

### Adding New Features
1. Create services in `services/` for business logic
2. Add UI components in `gui/` following existing patterns
3. Use `UICoordinator` for state management between components
4. Implement thread-safe operations using `PyQt6ThreadManager`

### Configuration Changes
- Update `constants.py` for new default values
- Modify `AppConfig` class for new settings
- Use `ConfigurationService` for complex configuration logic

### Network Operations
- Extend `UnifiedNetworkService` for new API endpoints
- Use connection pooling for performance
- Implement proper error handling and retries

### UI Development
- Follow existing PyQt6 patterns in `gui/components_pyqt6.py`
- Use `StyleManager` for consistent theming
- Implement proper DPI scaling with `dpi_adapter.py`

## Build Configuration

The `GameServerChecker.spec` file contains optimized PyInstaller settings:
- Excludes unnecessary modules to reduce size (~32MB executable)
- Includes all required PyQt6 components and project modules
- Configures Windows-specific optimizations (UPX compression, symbol stripping)
- Filters out deprecated modules and unused dependencies

**Recent Optimizations:**
- Removed legacy service modules (`file_service.py`, `scheduler_service.py`)
- Cleaned unused GUI components and utility modules  
- Updated hiddenimports to reflect current module structure
- Optimized startup performance with lazy loading

## Important Development Notes

### Code Quality and Architecture
- All modules follow a clean separation of concerns
- Services handle business logic, GUI handles presentation, Utils provide shared functionality
- Thread-safe patterns are implemented throughout using PyQt6 signals/slots
- Performance optimizations include caching, connection pooling, and lazy loading

### Module Dependencies
- Core modules should not import from Services (services import from core)
- GUI modules can import from both Core and Utils, but not Services directly
- Utils modules should be self-contained with minimal cross-dependencies
- Service-to-service communication goes through the main application class

### Windows-Specific Features
- Admin privilege elevation for registry access and system integration
- Single instance enforcement to prevent multiple app instances
- System tray integration with native Windows notifications
- High DPI scaling support for 4K displays