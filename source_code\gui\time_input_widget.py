"""
时间输入组件
提供带固定冒号的时间输入功能
"""

from PyQt6.QtWidgets import (QWidget, QHBoxLayout, QLineEdit, QLabel, 
                             QSizePolicy, QFrame)
from PyQt6.QtCore import pyqtSignal, Qt, QTimer
from PyQt6.QtGui import QFont, QValidator, QIntValidator
import re


class TimeValidator(QValidator):
    """时间验证器"""
    
    def __init__(self, is_hour=True):
        super().__init__()
        self.is_hour = is_hour
    
    def validate(self, input_str, pos):
        if not input_str:
            return QValidator.State.Intermediate, input_str, pos
        
        try:
            value = int(input_str)
            if self.is_hour:
                if 0 <= value <= 23:
                    return QValidator.State.Acceptable, input_str, pos
                elif value < 100:  # 允许输入过程中的中间状态
                    return QValidator.State.Intermediate, input_str, pos
            else:  # 分钟
                if 0 <= value <= 59:
                    return QValidator.State.Acceptable, input_str, pos
                elif value < 100:  # 允许输入过程中的中间状态
                    return QValidator.State.Intermediate, input_str, pos
        except ValueError:
            pass
        
        return QValidator.State.Invalid, input_str, pos


class TimeInputWidget(QWidget):
    """时间输入组件，带固定冒号"""
    
    # 信号定义
    timeChanged = pyqtSignal(str)  # 时间改变信号，格式为 "HH:MM"
    
    def __init__(self, default_time="08:00", parent=None):
        super().__init__(parent)
        self.setup_ui()
        self.set_time(default_time)
        self.setup_connections()
    
    def setup_ui(self):
        """设置UI"""
        # 主布局
        layout = QHBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(2)
        
        # 小时输入框
        self.hour_input = QLineEdit()
        self.hour_input.setMaximumWidth(30)
        self.hour_input.setMinimumHeight(30)
        self.hour_input.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.hour_input.setMaxLength(2)
        self.hour_input.setValidator(TimeValidator(is_hour=True))
        self.hour_input.setPlaceholderText("HH")
        
        # 固定冒号标签
        self.colon_label = QLabel(":")
        self.colon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.colon_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #333;
                background-color: transparent;
                padding: 0px 2px;
            }
        """)
        
        # 分钟输入框
        self.minute_input = QLineEdit()
        self.minute_input.setMaximumWidth(30)
        self.minute_input.setMinimumHeight(30)
        self.minute_input.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.minute_input.setMaxLength(2)
        self.minute_input.setValidator(TimeValidator(is_hour=False))
        self.minute_input.setPlaceholderText("MM")
        
        # 添加到布局
        layout.addWidget(self.hour_input)
        layout.addWidget(self.colon_label)
        layout.addWidget(self.minute_input)
        
        # 设置样式
        self.setStyleSheet("""
            QLineEdit {
                border: 1px solid #ccc;
                border-radius: 4px;
                padding: 4px;
                font-size: 12px;
                background-color: white;
            }
            QLineEdit:focus {
                border: 2px solid #007acc;
                background-color: #f0f8ff;
            }
            QLineEdit:disabled {
                background-color: #f5f5f5;
                color: #999;
                border: 1px solid #ddd;
            }
        """)
    
    def setup_connections(self):
        """设置信号连接"""
        self.hour_input.textChanged.connect(self._on_hour_changed)
        self.minute_input.textChanged.connect(self._on_minute_changed)
        
        # 设置焦点切换
        self.hour_input.textChanged.connect(self._check_hour_complete)
        self.minute_input.textChanged.connect(self._check_minute_complete)
    
    def _on_hour_changed(self, text):
        """小时改变处理"""
        self._emit_time_changed()
    
    def _on_minute_changed(self, text):
        """分钟改变处理"""
        self._emit_time_changed()
    
    def _check_hour_complete(self, text):
        """检查小时输入是否完成，自动切换焦点"""
        if len(text) == 2:
            try:
                hour = int(text)
                if 0 <= hour <= 23:
                    self.minute_input.setFocus()
                    self.minute_input.selectAll()
            except ValueError:
                pass
    
    def _check_minute_complete(self, text):
        """检查分钟输入是否完成"""
        if len(text) == 2:
            try:
                minute = int(text)
                if 0 <= minute <= 59:
                    # 分钟输入完成，可以触发其他操作
                    pass
            except ValueError:
                pass
    
    def _emit_time_changed(self):
        """发射时间改变信号"""
        time_str = self.get_time()
        if self.is_valid_time(time_str):
            self.timeChanged.emit(time_str)
    
    def set_time(self, time_str):
        """设置时间"""
        if not time_str or not isinstance(time_str, str):
            time_str = "08:00"
        
        # 解析时间字符串
        if ":" in time_str:
            parts = time_str.split(":")
            if len(parts) == 2:
                try:
                    hour = int(parts[0])
                    minute = int(parts[1])
                    
                    if 0 <= hour <= 23 and 0 <= minute <= 59:
                        self.hour_input.setText(f"{hour:02d}")
                        self.minute_input.setText(f"{minute:02d}")
                        return
                except ValueError:
                    pass
        
        # 默认时间
        self.hour_input.setText("08")
        self.minute_input.setText("00")
    
    def get_time(self):
        """获取时间字符串"""
        hour_text = self.hour_input.text().strip()
        minute_text = self.minute_input.text().strip()
        
        # 补零
        if hour_text and len(hour_text) == 1:
            hour_text = f"0{hour_text}"
        if minute_text and len(minute_text) == 1:
            minute_text = f"0{minute_text}"
        
        # 默认值
        if not hour_text:
            hour_text = "08"
        if not minute_text:
            minute_text = "00"
        
        return f"{hour_text}:{minute_text}"
    
    def is_valid_time(self, time_str):
        """验证时间格式是否有效"""
        if not time_str or ":" not in time_str:
            return False
        
        parts = time_str.split(":")
        if len(parts) != 2:
            return False
        
        try:
            hour = int(parts[0])
            minute = int(parts[1])
            return 0 <= hour <= 23 and 0 <= minute <= 59
        except ValueError:
            return False
    
    def setEnabled(self, enabled):
        """设置启用状态"""
        super().setEnabled(enabled)
        self.hour_input.setEnabled(enabled)
        self.minute_input.setEnabled(enabled)
        
        # 更新冒号标签的样式
        if enabled:
            self.colon_label.setStyleSheet("""
                QLabel {
                    font-size: 14px;
                    font-weight: bold;
                    color: #333;
                    background-color: transparent;
                    padding: 0px 2px;
                }
            """)
        else:
            self.colon_label.setStyleSheet("""
                QLabel {
                    font-size: 14px;
                    font-weight: bold;
                    color: #999;
                    background-color: transparent;
                    padding: 0px 2px;
                }
            """)
    
    def clear(self):
        """清空输入"""
        self.hour_input.clear()
        self.minute_input.clear()
    
    def setFocus(self):
        """设置焦点到小时输入框"""
        self.hour_input.setFocus()
    
    def setToolTip(self, tooltip):
        """设置工具提示"""
        super().setToolTip(tooltip)
        self.hour_input.setToolTip(tooltip)
        self.minute_input.setToolTip(tooltip)
        self.colon_label.setToolTip(tooltip)

    # 兼容性方法，保持与QLineEdit的接口一致
    def setText(self, text):
        """兼容性方法：设置文本（实际调用set_time）"""
        self.set_time(text)

    def text(self):
        """兼容性方法：获取文本（实际调用get_time）"""
        return self.get_time()


class CompactTimeInputWidget(TimeInputWidget):
    """紧凑版时间输入组件"""
    
    def setup_ui(self):
        """设置紧凑版UI"""
        super().setup_ui()
        
        # 调整尺寸
        self.hour_input.setMaximumWidth(25)
        self.hour_input.setMinimumHeight(25)
        self.minute_input.setMaximumWidth(25)
        self.minute_input.setMinimumHeight(25)
        
        # 调整字体
        font = QFont()
        font.setPointSize(10)
        self.hour_input.setFont(font)
        self.minute_input.setFont(font)
        self.colon_label.setFont(font)
        
        # 调整冒号样式
        self.colon_label.setStyleSheet("""
            QLabel {
                font-size: 12px;
                font-weight: bold;
                color: #333;
                background-color: transparent;
                padding: 0px 1px;
            }
        """)
