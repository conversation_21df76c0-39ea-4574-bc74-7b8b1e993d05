"""
应用程序辅助方法模块
包含文件处理、服务器验证等辅助功能
"""

import os
import datetime
import configparser
import sys
from typing import List, Dict, Any, Optional
import traceback

from utils.smart_logger import smart_debug_log, create_debug_logger_for_class


@create_debug_logger_for_class
class AppHelpers:
    """应用程序辅助方法类"""
    
    def __init__(self, app_instance):
        self.app = app_instance
    
    def log(self, message: str, level: str = "INFO"):
        """委托日志记录给主应用"""
        if hasattr(self.app, 'log'):
            self.app.log(message, level)
    
    def find_target_server(self, server_list: List[Dict], days_offset: int = 1) -> Optional[str]:
        """
        查找指定天数偏移后开放的服务器ID

        Args:
            server_list: 服务器列表
            days_offset: 天数偏移量，默认为1（明天）

        Returns:
            符合条件的服务器ID或None
        """
        self.debug_log("查找 {} 天后开放的服务器...", days_offset)

        try:
            # 计算目标日期 (UTC+8)
            tz_utc8 = datetime.timezone(datetime.timedelta(hours=8))
            now_utc8 = datetime.datetime.now(tz_utc8)
            target_date_utc8 = (now_utc8 + datetime.timedelta(days=days_offset)).date()
            self.debug_log("目标日期: {}", target_date_utc8)

            for server in server_list:
                if isinstance(server, dict) and "autoOpenTime" in server and "serverId" in server:
                    try:
                        timestamp_ms = server['autoOpenTime']
                        if isinstance(timestamp_ms, (int, float)):
                            # 将时间戳转换为UTC时间
                            opentime_utc = datetime.datetime.utcfromtimestamp(timestamp_ms / 1000)

                            # 转换为UTC+8时间
                            opentime_utc8 = opentime_utc.replace(tzinfo=datetime.timezone.utc).astimezone(tz_utc8)

                            # 检查日期是否匹配
                            if opentime_utc8.date() == target_date_utc8:
                                server_id = server['serverId']
                                self.debug_log("找到服务器ID: {}", server_id)
                                return server_id
                    except (ValueError, TypeError, OSError) as e_conv:
                        self.debug_log("处理服务器时间戳出错: {}", e_conv)
                        continue

            self.debug_log("未找到 {} 天后开放的服务器", days_offset)
            return None
        except Exception as e:
            self.log(f"查找服务器时发生错误: {e}", "ERROR")
            return None

    def validate_server_for_ini_operation(self, server_data: List[Dict], server_id: str, days_cutoff: int = 26) -> bool:
        """
        验证服务器是否符合INI操作的业务要求

        Args:
            server_data: 服务器数据列表
            server_id: 要验证的服务器ID
            days_cutoff: 天数阈值

        Returns:
            是否通过验证
        """
        try:
            # 查找目标服务器
            target_server = None
            for server in server_data:
                if isinstance(server, dict) and str(server.get("serverId", "")) == str(server_id):
                    target_server = server
                    break

            if not target_server:
                self.debug_log("验证失败: 未找到服务器 {}", server_id)
                return False

            # 检查服务器开放时间
            if "autoOpenTime" not in target_server:
                self.debug_log("验证失败: 服务器 {} 缺少时间信息", server_id)
                return False

            try:
                timestamp_ms = target_server['autoOpenTime']
                if isinstance(timestamp_ms, (int, float)):
                    # 计算服务器开放时间
                    tz_utc8 = datetime.timezone(datetime.timedelta(hours=8))
                    opentime_utc = datetime.datetime.utcfromtimestamp(timestamp_ms / 1000)
                    opentime_utc8 = opentime_utc.replace(tzinfo=datetime.timezone.utc).astimezone(tz_utc8)
                    
                    # 计算当前时间
                    now_utc8 = datetime.datetime.now(tz_utc8)
                    
                    # 计算天数差
                    days_diff = (opentime_utc8.date() - now_utc8.date()).days
                    
                    # 验证是否在合理范围内
                    if days_diff < -days_cutoff:
                        self.debug_log("服务器 {} 过期 ({} 天前)", server_id, abs(days_diff))
                        return False
                    elif days_diff > 30:  # 不允许超过30天后的服务器
                        self.debug_log("服务器 {} 时间过晚 ({} 天后)", server_id, days_diff)
                        return False
                    
                    self.debug_log("服务器 {} 验证通过", server_id)
                    return True
                else:
                    self.debug_log("服务器 {} 时间格式无效", server_id)
                    return False
                    
            except (ValueError, TypeError, OSError) as e:
                self.debug_log("验证服务器 {} 时出错: {}", server_id, e)
                return False

        except Exception as e:
            self.debug_log("验证服务器 {} 时发生错误: {}", server_id, e)
            return False

    def update_ini_file(self, ini_path: str, server_id: str, pid: int, gid: int, 
                       server_data: Dict[str, Any], overrides: Dict[str, str]) -> bool:
        """更新INI文件 - 核心逻辑"""
        section_name = f"{pid}_{gid}_{server_id}"
        file_name = os.path.basename(ini_path)

        self.debug_log("更新 {} 段落: {}", file_name, section_name)

        # 创建配置解析器
        config = configparser.ConfigParser(allow_no_value=True, strict=False)
        files_read = []

        try:
            # 尝试读取文件
            files_read = config.read(ini_path, encoding='utf-8')
        except UnicodeDecodeError:
            self.debug_log("{} UTF-8解码失败，尝试其他编码...", file_name)
            # 尝试多种编码
            encodings_to_try = ['gbk', 'gb2312', 'cp1252', sys.getdefaultencoding()]
            files_read = []

            for encoding in encodings_to_try:
                try:
                    files_read = config.read(ini_path, encoding=encoding)
                    if files_read:
                        self.debug_log("使用 {} 编码读取成功", encoding)
                        break
                except Exception:
                    continue

            if not files_read:
                self.log(f"无法读取文件 {file_name}", "ERROR")
                return False
        except configparser.Error as e_parse:
            self.debug_log("解析 {} 出错: {}", file_name, e_parse)
            return False
        except Exception as e_read:
            self.debug_log("读取 {} 出错: {}", file_name, e_read)
            return False

        if not files_read:
            self.debug_log("文件 {} 为空或不存在", file_name)
            return False

        try:
            if not config.has_section(section_name):
                # 创建新段落
                self.debug_log("创建新段落 {}", section_name)
                config.add_section(section_name)

                # 准备默认值
                from core.constants import CONTENT_TEMPLATE
                default_values = {}
                try:
                    template_config = configparser.ConfigParser(allow_no_value=True)
                    template_content = CONTENT_TEMPLATE.format(pid=pid, gid=gid, sid=server_id)
                    template_config.read_string(f"[DEFAULT]\n{template_content}")
                    if 'DEFAULT' in template_config:
                        default_values = dict(template_config['DEFAULT'])
                    else:
                        raise ValueError("无法解析模板内容")
                except Exception as e_template:
                    self.debug_log("解析模板出错: {}", e_template)
                    default_values = {}

                # 合并默认值和覆盖值
                final_values = default_values.copy()
                if overrides:
                    self.debug_log("应用 {} 个覆盖值", len(overrides))
                    for k, v in overrides.items():
                        final_values[k] = v
                elif not default_values:
                    self.debug_log("新段落 {} 将为空", section_name)

                # 设置值
                for key, value in final_values.items():
                    config.set(section_name, key, value)

                # 写入文件
                try:
                    with open(ini_path, 'w', encoding='utf-8') as configfile:
                        config.write(configfile, space_around_delimiters=False)
                    self.debug_log("成功创建段落 {}", section_name)
                    return True
                except Exception as e_write:
                    self.log(f"写入 {file_name} 失败: {e_write}", "ERROR")
                    return False
            else:
                # 更新现有段落
                if overrides:
                    self.debug_log("段落 {} 已存在，检查覆盖值", section_name)
                    updated = False
                    for key, value in overrides.items():
                        config.set(section_name, key, value)
                        self.debug_log("设置 {} = {}", key, value)
                        updated = True

                    if updated:
                        try:
                            with open(ini_path, 'w', encoding='utf-8') as configfile:
                                config.write(configfile, space_around_delimiters=False)
                            self.debug_log("更新段落 {}", section_name)
                            return True
                        except Exception as e_write:
                            self.log(f"更新 {file_name} 失败: {e_write}", "ERROR")
                            return False
                    else:
                        self.debug_log("段落 {} 无需更新", section_name)
                        return False
                else:
                    self.debug_log("段落 {} 已存在，无覆盖值", section_name)
                    return False

        except Exception as e:
            self.debug_log("处理 {} 时出错: {}", file_name, e)
            return False

    def disable_old_servers(self, file_path: str, all_api_data: Dict, days_cutoff: int,
                           conditions_str: str) -> bool:
        """
        禁用旧服务器

        Args:
            file_path: INI文件路径
            all_api_data: 所有API数据
            days_cutoff: 天数阈值
            conditions_str: 禁用条件字符串

        Returns:
            是否有更改
        """
        import datetime
        import configparser

        made_changes = False
        basename = os.path.basename(file_path)

        # 解析禁用条件
        disable_conditions = {}
        try:
            for line in conditions_str.splitlines():
                line = line.strip()
                if '=' in line:
                    key, value = line.split('=', 1)
                    disable_conditions[key.strip()] = value.strip()
        except Exception as e_cond:
            self.debug_log("{} 解析禁用条件出错: {}", basename, e_cond)
            disable_conditions = {}

        # 计算截止日期
        try:
            tz_utc8 = datetime.timezone(datetime.timedelta(hours=8))
            now_utc8 = datetime.datetime.now(tz_utc8)
            cutoff_date_utc8 = (now_utc8 - datetime.timedelta(days=days_cutoff)).date()
            self.debug_log("{} 检查 {} 天前的旧服务器...", basename, days_cutoff)
        except Exception as e_tz:
            self.debug_log("{} 计算截止日期出错: {}", basename, e_tz)
            return False

        disabled_count = 0
        checked_count = 0

        # 读取当前INI文件
        try:
            config = configparser.ConfigParser(allow_no_value=True, strict=False)
            config.read(file_path, encoding='utf-8')
        except Exception as e_read:
            self.debug_log("{} 读取INI文件出错: {}", basename, e_read)
            return False

        # 检查每个服务器
        for (pid, gid), server_list in all_api_data.items():
            if not server_list:
                continue

            for server in server_list:
                if isinstance(server, dict) and "autoOpenTime" in server and "serverId" in server:
                    try:
                        timestamp_ms = server['autoOpenTime']
                        opentime_utc = datetime.datetime.utcfromtimestamp(timestamp_ms / 1000)
                        server_id = server['serverId']

                        # 转换为UTC+8时间
                        opentime_utc8 = opentime_utc.replace(tzinfo=datetime.timezone.utc).astimezone(tz_utc8)

                        # 检查是否需要禁用
                        if opentime_utc8.date() < cutoff_date_utc8:
                            section_name = f"{pid}_{gid}_{server_id}"
                            if config.has_section(section_name):
                                checked_count += 1
                                
                                # 检查禁用条件
                                all_conditions_met = True
                                if disable_conditions:
                                    for key, expected_value in disable_conditions.items():
                                        try:
                                            current_value = config.get(section_name, key)
                                            if current_value != expected_value:
                                                all_conditions_met = False
                                                break
                                        except configparser.NoOptionError:
                                            all_conditions_met = False
                                            break
                                        except Exception as e_get:
                                            self.debug_log("获取段落 {} 的键 {} 出错: {}", section_name, key, e_get)
                                            all_conditions_met = False
                                            break

                                # 检查当前状态并禁用
                                current_state = config.get(section_name, 'state', fallback='1')

                                if all_conditions_met and current_state != '0':
                                    self.debug_log("禁用段落: {}", section_name)
                                    config.set(section_name, 'state', '0')
                                    made_changes = True
                                    disabled_count += 1
                    except (ValueError, TypeError, OSError, KeyError, configparser.Error) as e_inner:
                        self.debug_log("处理服务器 {} 数据出错: {}", server.get('serverId', 'N/A'), e_inner)
                        continue

        # 如果有更改，写回文件
        if made_changes:
            try:
                with open(file_path, 'w', encoding='utf-8') as configfile:
                    config.write(configfile, space_around_delimiters=False)
                if disabled_count > 0:
                    self.debug_log("{} 禁用了 {} 个旧服务器", basename, disabled_count)
            except Exception as e_write:
                self.log(f"{basename} 写入禁用更改出错: {e_write}", "ERROR")
                return False

        return made_changes
