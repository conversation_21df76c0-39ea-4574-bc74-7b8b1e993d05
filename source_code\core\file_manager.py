"""
文件管理器模块
负责文件列表管理、文件搜索、文件处理等功能
从 app_pyqt6.py 中提取的文件相关功能
"""

import os
import threading
from typing import List, Dict, Any, Optional
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
    QScrollArea, QFileDialog, QMessageBox, QGroupBox, QComboBox
)
from PyQt6.QtCore import Qt, QTimer
from PyQt6.QtGui import QFont

from gui.ui_factory import UIFactory
from gui.components_pyqt6 import Tooltip
from gui.dialogs_pyqt6 import PIDGIDConfigDialog
from gui.ui_constants import Sizes, Spacing
from utils.pyqt6_threading import SafeUIUpdater
from utils.system import detect_drives


class FileManager:
    """文件管理器类 - 负责文件列表管理和搜索功能"""
    
    def __init__(self, parent_app):
        """
        初始化文件管理器
        
        Args:
            parent_app: 主应用程序实例
        """
        self.app = parent_app
        self.file_checkboxes = []
        self.selected_files = []
        self.search_in_progress = False
        self.cancel_event = None
        self.current_thread = None
        
        # UI组件引用
        self.file_scroll_area = None
        self.file_scroll_widget = None
        self.file_scroll_layout = None
        self.disk_combo = None
    
    def create_file_section(self, parent):
        """创建文件管理区域"""
        file_frame = self.app.QFrame()
        file_layout = QVBoxLayout(file_frame)
        
        # File list title
        file_title = QLabel("文件列表")
        file_title.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        file_layout.addWidget(file_title)
        
        # File operation buttons
        self._create_file_buttons(file_layout)
        
        # File scroll area
        self.file_scroll_area = QScrollArea()
        self.file_scroll_widget = QWidget()
        self.file_scroll_layout = QVBoxLayout(self.file_scroll_widget)
        self.file_scroll_layout.setAlignment(Qt.AlignmentFlag.AlignTop)
        
        self.file_scroll_area.setWidget(self.file_scroll_widget)
        self.file_scroll_area.setWidgetResizable(True)
        self.file_scroll_area.setMinimumHeight(300)
        
        file_layout.addWidget(self.file_scroll_area)
        
        # Search section
        self._create_search_section(file_layout)
        
        parent.addWidget(file_frame)
    
    def _create_file_buttons(self, layout):
        """创建文件操作按钮"""
        buttons_layout = UIFactory.create_horizontal_layout(spacing=Spacing.LAYOUT_SPACING)

        # Browse files button
        browse_button = UIFactory.create_button(
            text="浏览文件",
            style_type="default",
            size_type="normal",
            min_height=Sizes.BUTTON_MIN_HEIGHT,
            callback=self.browse_files,
            tooltip="选择要检查的文件"
        )
        buttons_layout.addWidget(browse_button)

        # Add folder button
        folder_button = UIFactory.create_button(
            text="添加文件夹",
            style_type="default",
            size_type="normal",
            min_height=Sizes.BUTTON_MIN_HEIGHT,
            callback=self.add_folder,
            tooltip="添加整个文件夹中的文件"
        )
        buttons_layout.addWidget(folder_button)

        # Clear all button
        clear_button = UIFactory.create_button(
            text="清空列表",
            style_type="danger",
            size_type="normal",
            min_height=Sizes.BUTTON_MIN_HEIGHT,
            callback=self.clear_file_list,
            tooltip="清空所有已添加的文件"
        )
        buttons_layout.addWidget(clear_button)

        layout.addLayout(buttons_layout)
    
    def _create_search_section(self, layout):
        """创建搜索区域"""
        # 创建搜索组框
        search_group = QGroupBox("文件搜索")
        search_group.setMinimumHeight(120)
        search_main_layout = QVBoxLayout(search_group)
        search_main_layout.setSpacing(10)
        search_main_layout.setContentsMargins(15, 15, 15, 15)

        # 第一行：磁盘选择
        disk_layout = QHBoxLayout()
        disk_layout.setSpacing(10)

        # 磁盘选择标签
        disk_label = QLabel("选择搜索磁盘:")
        disk_label.setMinimumWidth(100)
        disk_layout.addWidget(disk_label)

        # 磁盘选择下拉框
        self.disk_combo = QComboBox()
        self.disk_combo.setMinimumHeight(30)
        self.disk_combo.setMinimumWidth(200)
        self.disk_combo.setToolTip("选择要搜索的磁盘驱动器")
        disk_layout.addWidget(self.disk_combo)

        # 刷新磁盘列表按钮
        refresh_disk_button = QPushButton("刷新磁盘")
        refresh_disk_button.setMinimumHeight(30)
        refresh_disk_button.setMaximumWidth(80)
        refresh_disk_button.clicked.connect(self.refresh_disk_list)
        refresh_disk_button.setToolTip("重新检测可用的磁盘驱动器")

        # 应用DPI适配修复
        from utils.dpi_adapter import apply_button_dpi_fix
        apply_button_dpi_fix(refresh_disk_button)

        disk_layout.addWidget(refresh_disk_button)

        # 添加弹性空间
        disk_layout.addStretch()
        search_main_layout.addLayout(disk_layout)

        # 第二行：搜索按钮和选项
        button_layout = QHBoxLayout()
        button_layout.setSpacing(10)

        # 搜索按钮
        search_button = QPushButton("搜索文件")
        search_button.clicked.connect(self.search_files)
        search_button.setMinimumHeight(35)
        search_button.setMinimumWidth(100)
        search_button.setToolTip("在选定的磁盘中搜索server.ini文件")

        # 应用DPI适配修复
        apply_button_dpi_fix(search_button)
        button_layout.addWidget(search_button)

        # 全盘搜索按钮
        search_all_button = QPushButton("搜索所有磁盘")
        search_all_button.clicked.connect(self.search_all_disks)
        search_all_button.setMinimumHeight(35)
        search_all_button.setMinimumWidth(120)
        search_all_button.setToolTip("在所有可用磁盘中搜索server.ini文件")

        # 应用DPI适配修复
        apply_button_dpi_fix(search_all_button)
        button_layout.addWidget(search_all_button)

        # 添加弹性空间
        button_layout.addStretch()
        search_main_layout.addLayout(button_layout)

        layout.addWidget(search_group)

        # 初始化磁盘列表 - 在所有UI组件创建完成后
        self.refresh_disk_list_sync()
    
    def browse_files(self):
        """浏览并添加文件"""
        files, _ = QFileDialog.getOpenFileNames(
            self.app,
            "选择文件",
            "",
            "所有文件 (*.*)"
        )
        if files:
            for file_path in files:
                self.add_file_to_list(file_path)
            self.app.log(f"添加了 {len(files)} 个文件")

    def add_folder(self):
        """添加文件夹中的所有文件"""
        folder = QFileDialog.getExistingDirectory(self.app, "选择文件夹")
        if folder:
            # Add logic to scan folder for files
            self.app.log(f"添加文件夹: {folder}")

    def clear_file_list(self):
        """清空文件列表"""
        # Clear the scroll area
        for i in reversed(range(self.file_scroll_layout.count())):
            child = self.file_scroll_layout.itemAt(i).widget()
            if child:
                child.setParent(None)

        self.file_checkboxes.clear()
        self.selected_files.clear()
        self.app.log("已清空文件列表")
        self.app.update_check_button_state()

    def add_file_to_list(self, file_path: str):
        """添加文件到列表"""
        # 标准化路径格式
        normalized_path = os.path.normpath(file_path)

        # 检查文件是否已存在于列表中
        for item in self.file_checkboxes:
            existing_path = os.path.normpath(item['path'])
            if existing_path.lower() == normalized_path.lower():
                self.app.log(f"文件已存在于列表中: {os.path.basename(normalized_path)}", "WARNING")
                QMessageBox.information(
                    self.app,
                    "文件已存在",
                    f"文件已存在于列表中，无需重复添加：\n\n{normalized_path}"
                )
                return

        # 使用标准化后的路径
        file_path = normalized_path

        # Create file item widget
        file_widget = QWidget()
        file_layout = QHBoxLayout(file_widget)
        file_layout.setContentsMargins(5, 2, 5, 2)

        # Checkbox
        checkbox = UIFactory.create_checkbox(
            text="",
            checked=True,
            callback=self.app.update_check_button_state,
            tooltip=f"选择/取消选择文件: {os.path.basename(file_path)}"
        )
        file_layout.addWidget(checkbox)

        # File path label - show full path
        file_label = QLabel(file_path)
        file_label.setWordWrap(True)  # 允许换行
        file_label.setStyleSheet("QLabel { color: #333; }")
        file_layout.addWidget(file_label, 1)

        # Add tooltip with additional info
        tooltip_text = f"完整路径: {file_path}\n文件名: {os.path.basename(file_path)}"
        tooltip = Tooltip(file_label, tooltip_text, self.app.scale_factor)

        # Config button - 使用UIFactory创建
        config_button = UIFactory.create_button(
            text="配置PID/GID",
            style_type="default",
            size_type="small",
            callback=lambda: self.open_pid_gid_config(file_path),
            tooltip=f"配置文件 {os.path.basename(file_path)} 的PID/GID设置"
        )
        file_layout.addWidget(config_button)

        # Delete button - 使用UIFactory创建
        delete_button = UIFactory.create_button(
            text="删除",
            style_type="danger",
            size_type="small",
            callback=lambda: self.remove_file_from_list(file_path),
            tooltip=f"从列表中移除文件 {os.path.basename(file_path)}"
        )
        file_layout.addWidget(delete_button)

        # Add to scroll area
        self.file_scroll_layout.addWidget(file_widget)

        # Store reference
        self.file_checkboxes.append({
            'path': file_path,
            'checkbox': checkbox,
            'widget': file_widget
        })

        self.app.update_check_button_state()

        # 确保文件配置存在（这会自动保存配置）
        self.app.config.get_file_config(file_path)

    def remove_file_from_list(self, file_path: str):
        """从列表中删除指定文件"""
        try:
            # 确认删除
            reply = QMessageBox.question(
                self.app,
                "确认删除",
                f"确定要从列表中删除以下文件吗？\n\n{file_path}\n\n注意：这只会从列表中移除，不会删除实际文件。",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )

            if reply != QMessageBox.StandardButton.Yes:
                return

            # 查找并删除文件项
            for i, item in enumerate(self.file_checkboxes):
                if item['path'] == file_path:
                    # 从UI中移除widget
                    widget = item['widget']
                    self.file_scroll_layout.removeWidget(widget)
                    widget.setParent(None)
                    widget.deleteLater()

                    # 从列表中移除
                    self.file_checkboxes.pop(i)

                    # 从配置中删除
                    self.app.config.remove_file_config(file_path)

                    self.app.log(f"已从列表中删除文件: {os.path.basename(file_path)}", "INFO")
                    break

            # 更新UI状态
            self.app.update_check_button_state()
            self.app.update_buttons_state(searching=False, has_files=len(self.file_checkboxes) > 0)

        except Exception as e:
            self.app.log(f"删除文件时出错: {e}", "ERROR")
            QMessageBox.critical(self.app, "错误", f"删除文件时出错: {e}")

    def open_pid_gid_config(self, file_path: str):
        """打开PID/GID配置对话框"""
        self.app.log(f"打开文件 {os.path.basename(file_path)} 的配置...")

        file_config = self.app.config.get_file_config(file_path)
        dialog = PIDGIDConfigDialog(self.app, file_path, file_config, self.app.config)
        dialog.exec()

    def refresh_disk_list_sync(self):
        """同步刷新磁盘列表（用于UI初始化）"""
        try:
            drives = detect_drives()

            # 直接更新UI（在主线程中调用）
            self._update_disk_combo(drives)

        except Exception as e:
            error_msg = f"同步刷新磁盘列表时出错: {e}"
            try:
                self.app.log(error_msg, "ERROR")
            except:
                pass

    def refresh_disk_list(self):
        """刷新磁盘列表（线程安全版）"""
        try:
            drives = detect_drives()

            # 检查是否在主线程中，如果是则直接更新，否则使用SafeUIUpdater
            from PyQt6.QtWidgets import QApplication
            import threading

            if QApplication.instance() and QApplication.instance().thread() == threading.current_thread():
                # 在主线程中，直接更新
                self._update_disk_combo(drives)
            else:
                # 不在主线程中，使用SafeUIUpdater
                SafeUIUpdater.call_in_main_thread(self._update_disk_combo, drives)

        except Exception as e:
            # 记录错误日志
            error_msg = f"刷新磁盘列表时出错: {e}"
            try:
                from PyQt6.QtWidgets import QApplication
                import threading

                if QApplication.instance() and QApplication.instance().thread() == threading.current_thread():
                    self.app.log(error_msg, "ERROR")
                else:
                    SafeUIUpdater.call_in_main_thread(self.app.log, error_msg, "ERROR")
            except:
                print(error_msg)

    def _update_disk_combo(self, drives):
        """在主线程中更新磁盘下拉框"""
        if self.disk_combo is None or not hasattr(self, 'disk_combo'):
            return

        self.disk_combo.clear()

        if drives:
            self.disk_combo.addItem("所有磁盘", "ALL")
            for drive in drives:
                if ":" in drive and " " in drive:
                    drive_letter = drive.split(":")[0]
                    display_text = f"{drive_letter}: 盘 - {drive}"
                    self.disk_combo.addItem(display_text, drive)
                else:
                    self.disk_combo.addItem(drive, drive)
            self.app.log(f"检测到 {len(drives)} 个可用磁盘", "INFO")
        else:
            self.disk_combo.addItem("未检测到磁盘", "NONE")
            self.app.log("未检测到可用磁盘", "WARNING")

    def get_selected_drives(self):
        """获取选中的驱动器列表"""
        try:
            if not self.disk_combo:
                return []
                
            current_data = self.disk_combo.currentData()

            if current_data == "ALL":
                # 返回所有驱动器
                return detect_drives()
            elif current_data in ["NONE", "ERROR", None]:
                return []
            else:
                # 返回单个驱动器
                return [current_data]

        except Exception as e:
            self.app.log(f"获取选中驱动器时出错: {e}", "ERROR")
            return []

    def search_files(self):
        """搜索文件（在选定磁盘中）"""
        selected_drives = self.get_selected_drives()
        if not selected_drives:
            QMessageBox.warning(self.app, "警告", "请选择要搜索的磁盘")
            return

        # 显示搜索信息
        if len(selected_drives) == 1:
            drive_info = selected_drives[0]
            if ":" in drive_info:
                drive_letter = drive_info.split(":")[0]
                self.app.log(f"开始在 {drive_letter}: 盘中搜索文件...", "INFO")
            else:
                self.app.log(f"开始在 {drive_info} 中搜索文件...", "INFO")
        else:
            self.app.log(f"开始在 {len(selected_drives)} 个磁盘中搜索文件...", "INFO")

        self.start_search(selected_drives)

    def search_all_disks(self):
        """搜索所有磁盘"""
        all_drives = detect_drives()

        if not all_drives:
            QMessageBox.warning(self.app, "警告", "没有检测到可用的磁盘")
            return

        self.app.log(f"开始在所有 {len(all_drives)} 个磁盘中搜索文件...", "INFO")
        self.start_search(all_drives)

    def start_search(self, selected_drives=None):
        """开始搜索server.ini文件"""
        if self.search_in_progress:
            return

        # 如果没有指定驱动器，使用所有驱动器
        if selected_drives is None:
            selected_drives = detect_drives()

        if not selected_drives:
            QMessageBox.warning(self.app, "警告", "没有可搜索的驱动器")
            return

        self.search_in_progress = True
        self.app.log("开始搜索server.ini文件...", "INFO")

        # 设置取消事件
        self.cancel_event = threading.Event()
        self.app.file_service.set_cancel_event(self.cancel_event)

        # 更新UI状态
        self.app.update_buttons_state(searching=True)
        self.app.status_bar.showMessage("正在搜索文件...")
        self.app.progress_bar.setVisible(True)

        # 启动搜索线程
        self.current_thread = self.app.thread_manager.start_thread(
            target=self._search_files_thread,
            args=(selected_drives,),
            callback=self.search_completed
        )

    def _search_files_thread(self, selected_drives):
        """搜索文件的线程函数"""
        def progress_callback(progress, status):
            # 使用SafeUIUpdater在主线程中更新UI
            SafeUIUpdater.call_in_main_thread(self.app.progress_bar.setValue, int(progress * 100))
            SafeUIUpdater.call_in_main_thread(self.app.status_bar.showMessage, status)

        found_files = self.app.file_service.find_server_ini_files(
            progress_callback=progress_callback,
            selected_drives=selected_drives
        )
        return found_files

    def search_completed(self, found_files):
        """搜索完成回调"""
        self.search_in_progress = False

        if found_files:
            for file_path in found_files:
                self.add_file_to_list(file_path)

            self.app.log(f"搜索完成，找到 {len(found_files)} 个文件", "SUCCESS")
        else:
            self.app.log("搜索完成，未找到任何server.ini文件", "WARNING")

        # 重置UI状态
        self.app.update_buttons_state(searching=False, has_files=len(self.file_checkboxes) > 0)
        self.app.status_bar.showMessage("就绪")
        self.app.progress_bar.setVisible(False)
        self.app.progress_bar.setValue(0)

    def get_selected_files(self):
        """获取选中的文件列表"""
        return [item["path"] for item in self.file_checkboxes if item["checkbox"].isChecked()]

    def select_all_files(self):
        """选择所有文件"""
        for item in self.file_checkboxes:
            item["checkbox"].setChecked(True)
        self.app.update_check_button_state()

    def restore_file_list(self):
        """恢复文件列表（从配置加载）"""
        try:
            file_configs = self.app.config.get_all_file_configs()
            restored_count = 0

            for normalized_path in file_configs.keys():
                # 尝试多种方式恢复原始路径
                possible_paths = [
                    normalized_path,  # 直接使用
                    normalized_path.replace('/', '\\'),  # 转换路径分隔符
                    os.path.normpath(normalized_path),  # 标准化路径
                ]

                # 尝试找到存在的文件
                found_path = None
                for path in possible_paths:
                    if os.path.exists(path):
                        found_path = path
                        break

                if found_path:
                    # 避免重复添加（检查路径是否已存在）
                    already_exists = any(
                        os.path.normpath(item['path']) == os.path.normpath(found_path) 
                        for item in self.file_checkboxes
                    )
                    
                    if not already_exists:
                        self.add_file_to_list(found_path)
                        self.app.log(f"恢复文件: {os.path.basename(found_path)}", "INFO")
                        restored_count += 1
                    else:
                        # 文件已存在，跳过（DEBUG级别日志）
                        self.app.log(f"跳过重复文件: {os.path.basename(found_path)}", "DEBUG")
                else:
                    self.app.log(f"跳过不存在的文件: {normalized_path}", "WARNING")

            if restored_count > 0:
                self.app.log(f"文件列表恢复完成，共恢复 {restored_count} 个文件", "SUCCESS")

        except Exception as e:
            self.app.log(f"恢复文件列表时出错: {e}", "ERROR")