"""
PyQt6样式管理模块
提供统一的样式和主题管理
"""

from PyQt6.QtCore import Qt
from PyQt6.QtGui import QPalette, QColor
from PyQt6.QtWidgets import QApplication

try:
    from .ui_constants import Colors as UIColors, Fonts, Spacing, Animation
    USE_NEW_CONSTANTS = True
except ImportError:
    USE_NEW_CONSTANTS = False


class StyleManager:
    """样式管理器"""
    
    # 颜色定义
    COLORS = {
        # 主色调
        'primary': '#1E90FF',
        'primary_hover': '#0066CC',
        'primary_dark': '#003D82',
        
        # 次要色调
        'secondary': '#FF6B35',
        'secondary_hover': '#E55A2B',
        
        # 状态色调
        'success': '#4CAF50',
        'warning': '#FFA500',
        'error': '#D32F2F',
        'info': '#2196F3',
        
        # 中性色调
        'background': '#FFFFFF',
        'surface': '#F5F5F5',
        'border': '#E0E0E0',
        'text': '#212121',
        'text_secondary': '#757575',
        'disabled': '#BDBDBD',
        
        # 深色主题
        'dark_background': '#2B2B2B',
        'dark_surface': '#3C3C3C',
        'dark_border': '#555555',
        'dark_text': '#FFFFFF',
        'dark_text_secondary': '#CCCCCC',
    }
    
    @classmethod
    def get_main_stylesheet(cls) -> str:
        """获取主样式表"""
        return f"""
        /* 主窗口样式 */
        QMainWindow {{
            background-color: {cls.COLORS['background']};
            color: {cls.COLORS['text']};
        }}
        
        /* 框架样式 */
        QFrame {{
            background-color: {cls.COLORS['surface']};
            border: 1px solid {cls.COLORS['border']};
            border-radius: 4px;
        }}
        
        /* 分组框样式 */
        QGroupBox {{
            font-weight: bold;
            border: 2px solid {cls.COLORS['border']};
            border-radius: 5px;
            margin-top: 10px;
            padding-top: 10px;
        }}
        
        QGroupBox::title {{
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
            color: {cls.COLORS['primary']};
        }}
        
        /* 按钮样式 */
        QPushButton {{
            background-color: {cls.COLORS['surface']};
            border: 1px solid {cls.COLORS['border']};
            border-radius: 4px;
            padding: 8px 16px;
            font-weight: bold;
            color: {cls.COLORS['text']};
        }}
        
        QPushButton:hover {{
            background-color: {cls.COLORS['border']};
            border-color: {cls.COLORS['primary']};
        }}
        
        QPushButton:pressed {{
            background-color: {cls.COLORS['primary']};
            color: white;
        }}
        
        QPushButton:disabled {{
            background-color: {cls.COLORS['disabled']};
            color: {cls.COLORS['text_secondary']};
            border-color: {cls.COLORS['disabled']};
        }}
        
        /* 主要按钮样式 */
        QPushButton.primary {{
            background-color: {cls.COLORS['primary']};
            color: white;
            border: none;
        }}
        
        QPushButton.primary:hover {{
            background-color: {cls.COLORS['primary_hover']};
        }}
        
        QPushButton.primary:pressed {{
            background-color: {cls.COLORS['primary_dark']};
        }}
        
        /* 危险按钮样式 */
        QPushButton.danger {{
            background-color: {cls.COLORS['error']};
            color: white;
            border: none;
        }}
        
        QPushButton.danger:hover {{
            background-color: #B71C1C;
        }}
        
        /* 警告按钮样式 */
        QPushButton.warning {{
            background-color: {cls.COLORS['warning']};
            color: white;
            border: none;
        }}
        
        QPushButton.warning:hover {{
            background-color: #CC8400;
        }}
        
        /* 输入框样式 */
        QLineEdit {{
            border: 1px solid {cls.COLORS['border']};
            border-radius: 4px;
            padding: 8px;
            background-color: white;
            selection-background-color: {cls.COLORS['primary']};
        }}
        
        QLineEdit:focus {{
            border-color: {cls.COLORS['primary']};
            border-width: 2px;
        }}
        
        QLineEdit:disabled {{
            background-color: {cls.COLORS['surface']};
            color: {cls.COLORS['text_secondary']};
        }}
        
        /* 文本编辑器样式 */
        QTextEdit {{
            border: 1px solid {cls.COLORS['border']};
            border-radius: 4px;
            background-color: white;
            selection-background-color: {cls.COLORS['primary']};
        }}
        
        QTextEdit:focus {{
            border-color: {cls.COLORS['primary']};
            border-width: 2px;
        }}
        
        /* 复选框样式 - 蓝色圆角带勾选图标 */
        QCheckBox {{
            spacing: 8px;
            font-weight: 500;
        }}

        QCheckBox::indicator {{
            width: 20px;
            height: 20px;
            border: 2px solid #DDDDDD;
            border-radius: 6px;
            background-color: white;
        }}

        QCheckBox::indicator:checked {{
            background-color: #007AFF;
            border-color: #007AFF;
            image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEzLjMzMzMgNEw2IDExLjMzMzNMMi42NjY2NyA4IiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K);
        }}

        QCheckBox::indicator:hover {{
            border-color: #007AFF;
            background-color: #F8FAFF;
        }}

        QCheckBox::indicator:checked:hover {{
            background-color: #0056CC;
            border-color: #0056CC;
            image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEzLjMzMzMgNEw2IDExLjMzMzNMMi42NjY2NyA4IiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K);
        }}

        QCheckBox::indicator:disabled {{
            background-color: #F5F5F5;
            border-color: #E0E0E0;
        }}

        QCheckBox::indicator:checked:disabled {{
            background-color: #CCCCCC;
            border-color: #CCCCCC;
            image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEzLjMzMzMgNEw2IDExLjMzMzNMMi42NjY2NyA4IiBzdHJva2U9ImdyYXkiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPgo=);
        }}
        
        /* 单选按钮样式 */
        QRadioButton {{
            spacing: 8px;
        }}
        
        QRadioButton::indicator {{
            width: 18px;
            height: 18px;
            border: 2px solid {cls.COLORS['border']};
            border-radius: 9px;
            background-color: white;
        }}
        
        QRadioButton::indicator:checked {{
            background-color: {cls.COLORS['primary']};
            border-color: {cls.COLORS['primary']};
        }}
        
        QRadioButton::indicator:hover {{
            border-color: {cls.COLORS['primary']};
        }}
        
        /* 下拉框样式 */
        QComboBox {{
            border: 1px solid {cls.COLORS['border']};
            border-radius: 4px;
            padding: 6px 8px;
            background-color: white;
            min-width: 100px;
            min-height: 28px;
            color: {cls.COLORS['text']};
        }}

        QComboBox:focus {{
            border-color: {cls.COLORS['primary']};
            border-width: 2px;
        }}

        QComboBox:disabled {{
            background-color: {cls.COLORS['surface']};
            color: {cls.COLORS['text_secondary']};
            border-color: {cls.COLORS['disabled']};
        }}

        QComboBox::drop-down {{
            border: none;
            width: 20px;
            subcontrol-origin: padding;
            subcontrol-position: top right;
        }}

        QComboBox::down-arrow {{
            image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOCIgdmlld0JveD0iMCAwIDEyIDgiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0xIDFMNiA2TDExIDEiIHN0cm9rZT0iIzc1NzU3NSIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPHN2Zz4K);
            width: 12px;
            height: 8px;
        }}

        QComboBox QAbstractItemView {{
            border: 1px solid {cls.COLORS['border']};
            background-color: white;
            selection-background-color: {cls.COLORS['primary']};
            selection-color: white;
            outline: none;
        }}

        QComboBox QAbstractItemView::item {{
            padding: 6px 8px;
            min-height: 20px;
        }}

        QComboBox QAbstractItemView::item:selected {{
            background-color: {cls.COLORS['primary']};
            color: white;
        }}
        
        /* 进度条样式 */
        QProgressBar {{
            border: 1px solid {cls.COLORS['border']};
            border-radius: 4px;
            text-align: center;
            background-color: {cls.COLORS['surface']};
        }}
        
        QProgressBar::chunk {{
            background-color: {cls.COLORS['primary']};
            border-radius: 3px;
        }}
        
        /* 滚动条样式 */
        QScrollBar:vertical {{
            background-color: {cls.COLORS['surface']};
            width: 12px;
            border-radius: 6px;
        }}
        
        QScrollBar::handle:vertical {{
            background-color: {cls.COLORS['border']};
            border-radius: 6px;
            min-height: 20px;
        }}
        
        QScrollBar::handle:vertical:hover {{
            background-color: {cls.COLORS['text_secondary']};
        }}
        
        QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {{
            border: none;
            background: none;
        }}
        
        /* 状态栏样式 */
        QStatusBar {{
            background-color: {cls.COLORS['surface']};
            border-top: 1px solid {cls.COLORS['border']};
        }}
        
        /* 工具提示样式 */
        QToolTip {{
            background-color: #FFFFE0;
            color: black;
            border: 1px solid #999999;
            border-radius: 3px;
            padding: 4px;
        }}
        
        /* 标签样式 */
        QLabel {{
            color: {cls.COLORS['text']};
        }}
        
        QLabel.title {{
            font-size: 16px;
            font-weight: bold;
            color: {cls.COLORS['primary']};
        }}
        
        QLabel.subtitle {{
            font-size: 14px;
            font-weight: bold;
            color: {cls.COLORS['text']};
        }}
        
        QLabel.caption {{
            font-size: 12px;
            color: {cls.COLORS['text_secondary']};
        }}
        """
    
    @classmethod
    def apply_style(cls, app: QApplication):
        """应用样式到应用程序"""
        app.setStyleSheet(cls.get_main_stylesheet())
    
    @classmethod
    def set_button_style(cls, button, style_type: str = 'default'):
        """设置按钮样式"""
        if style_type == 'primary':
            button.setProperty('class', 'primary')
        elif style_type == 'danger':
            button.setProperty('class', 'danger')
        elif style_type == 'warning':
            button.setProperty('class', 'warning')
        
        # 刷新样式
        button.style().unpolish(button)
        button.style().polish(button)
    
    @classmethod
    def set_label_style(cls, label, style_type: str = 'default'):
        """设置标签样式"""
        if style_type == 'title':
            label.setProperty('class', 'title')
        elif style_type == 'subtitle':
            label.setProperty('class', 'subtitle')
        elif style_type == 'caption':
            label.setProperty('class', 'caption')
        
        # 刷新样式
        label.style().unpolish(label)
        label.style().polish(label)
