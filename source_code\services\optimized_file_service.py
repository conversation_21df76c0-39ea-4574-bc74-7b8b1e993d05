"""
优化文件服务模块 - 性能增强版本
使用更高效的文件搜索算法和智能编码检测
"""

import os
import configparser
from pathlib import Path
from typing import List, Optional, Dict, Any, Callable
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
import time

from core.constants import INI_FILENAME
from utils.system import detect_drives
from utils.performance_optimizer import performance_monitor, monitor_performance, cached_calculation

# 延迟导入chardet，确保打包兼容性
chardet = None
try:
    import chardet
    print("chardet模块加载成功")
except ImportError:
    print("chardet模块未找到，将使用基础编码检测")


class EncodingDetector:
    """智能编码检测器"""
    
    def __init__(self):
        # 常见编码优先级列表
        self.common_encodings = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig', 'latin1']
        self._encoding_cache = {}
    
    def detect_encoding(self, file_path: str) -> str:
        """
        检测文件编码
        
        Args:
            file_path: 文件路径
            
        Returns:
            检测到的编码名称
        """
        # 检查缓存
        if file_path in self._encoding_cache:
            return self._encoding_cache[file_path]
        
        try:
            # 读取文件的前几KB用于检测
            with open(file_path, 'rb') as f:
                raw_data = f.read(8192)  # 读取8KB
            
            # 首先尝试常见编码
            for encoding in self.common_encodings:
                try:
                    raw_data.decode(encoding)
                    self._encoding_cache[file_path] = encoding
                    return encoding
                except UnicodeDecodeError:
                    continue
            
            # 如果常见编码都失败，使用chardet检测
            if raw_data and chardet is not None:
                detected = chardet.detect(raw_data)
                if detected and detected['confidence'] > 0.7:
                    encoding = detected['encoding']
                    self._encoding_cache[file_path] = encoding
                    return encoding
            
            # 默认使用UTF-8
            self._encoding_cache[file_path] = 'utf-8'
            return 'utf-8'
            
        except Exception as e:
            print(f"编码检测失败 {file_path}: {e}")
            return 'utf-8'


class FileSearchCache:
    """文件搜索缓存"""
    
    def __init__(self, max_size: int = 1000, ttl_seconds: int = 3600):
        self.max_size = max_size
        self.ttl_seconds = ttl_seconds
        self._cache = {}
        self._timestamps = {}
        self._lock = threading.Lock()
    
    def _generate_key(self, drives: List[str], filename: str) -> str:
        """生成缓存键"""
        return f"{sorted(drives)}:{filename}"
    
    def get(self, drives: List[str], filename: str) -> Optional[List[str]]:
        """从缓存获取搜索结果"""
        key = self._generate_key(drives, filename)
        
        with self._lock:
            if key in self._cache:
                # 检查是否过期
                if time.time() - self._timestamps[key] < self.ttl_seconds:
                    performance_monitor.increment_counter('file_search_cache_hit')
                    return self._cache[key].copy()
                else:
                    # 过期，删除
                    del self._cache[key]
                    del self._timestamps[key]
                    performance_monitor.increment_counter('file_search_cache_expired')
        
        performance_monitor.increment_counter('file_search_cache_miss')
        return None
    
    def set(self, drives: List[str], filename: str, results: List[str]):
        """设置缓存结果"""
        key = self._generate_key(drives, filename)
        
        with self._lock:
            # 如果缓存已满，删除最旧的条目
            if len(self._cache) >= self.max_size:
                oldest_key = min(self._timestamps.keys(), key=lambda k: self._timestamps[k])
                del self._cache[oldest_key]
                del self._timestamps[oldest_key]
            
            self._cache[key] = results.copy()
            self._timestamps[key] = time.time()
    
    def clear(self):
        """清空缓存"""
        with self._lock:
            self._cache.clear()
            self._timestamps.clear()


class OptimizedFileService:
    """优化文件服务类 - 高性能版本"""
    
    def __init__(self, enable_cache: bool = True, max_workers: int = 4):
        self.cancel_event = None
        self.encoding_detector = EncodingDetector()
        self.search_cache = FileSearchCache() if enable_cache else None
        self.max_workers = max_workers
        
        # 性能统计
        self._search_count = 0
        self._cache_hit_count = 0
        
        print(f"优化文件服务已初始化 - 缓存: {'启用' if enable_cache else '禁用'}, 最大工作线程: {max_workers}")
    
    @monitor_performance('file_search')
    def find_server_ini_files(self, filename: str = INI_FILENAME, 
                             progress_callback: Optional[Callable] = None,
                             selected_drives: Optional[List[str]] = None) -> List[str]:
        """
        搜索服务器INI文件（优化版本）
        
        Args:
            filename: 要搜索的文件名
            progress_callback: 进度回调函数
            selected_drives: 要搜索的驱动器列表
            
        Returns:
            找到的文件路径列表
        """
        print(f"正在搜索位于 'settings' 目录下的 '{filename}' 文件（优化版本）...")
        self._search_count += 1
        
        if not selected_drives:
            selected_drives = detect_drives()
        
        # 检查缓存
        if self.search_cache:
            cached_results = self.search_cache.get(selected_drives, filename)
            if cached_results is not None:
                print(f"从缓存获取搜索结果: {len(cached_results)} 个文件")
                self._cache_hit_count += 1
                return cached_results
        
        # 处理驱动器路径格式
        drives_to_search = self._normalize_drive_paths(selected_drives)
        
        # 使用多线程并行搜索
        found_files = self._parallel_search(drives_to_search, filename, progress_callback)
        
        # 缓存结果
        if self.search_cache:
            self.search_cache.set(selected_drives, filename, found_files)
        
        print(f"搜索完成，共找到 {len(found_files)} 个文件")
        return found_files
    
    def _normalize_drive_paths(self, selected_drives: List[str]) -> List[str]:
        """标准化驱动器路径格式"""
        drives_to_search = []
        for drive_path in selected_drives:
            if ":" in drive_path and " " in drive_path:
                # 格式如 "C: (NTFS)"
                drive_letter = drive_path.split(":")[0]
                drive_path = f"{drive_letter}:\\"
            elif not drive_path.endswith("\\"):
                drive_path = f"{drive_path}\\"
            drives_to_search.append(drive_path)
        return drives_to_search
    
    def _parallel_search(self, drives_to_search: List[str], filename: str, 
                        progress_callback: Optional[Callable] = None) -> List[str]:
        """并行搜索文件"""
        found_files = []
        total_drives = len(drives_to_search)
        processed_drives = 0
        
        with ThreadPoolExecutor(max_workers=min(self.max_workers, total_drives)) as executor:
            # 提交搜索任务
            future_to_drive = {
                executor.submit(self._search_drive_optimized, drive_path, filename): drive_path
                for drive_path in drives_to_search
            }
            
            # 收集结果
            for future in as_completed(future_to_drive):
                drive_path = future_to_drive[future]
                
                if self.cancel_event and self.cancel_event.is_set():
                    print("搜索被取消")
                    break
                
                try:
                    drive_files = future.result()
                    found_files.extend(drive_files)
                except Exception as e:
                    print(f"搜索驱动器 {drive_path} 时出错: {e}")
                
                processed_drives += 1
                if progress_callback:
                    progress = processed_drives / total_drives
                    progress_callback(progress, f"已搜索 {processed_drives}/{total_drives} 个驱动器")
        
        return found_files
    
    def _search_drive_optimized(self, drive_path: str, filename: str) -> List[str]:
        """在指定驱动器中搜索文件（优化版本）"""
        found_files = []
        
        try:
            # 使用pathlib.Path.rglob()进行更高效的搜索
            drive_path_obj = Path(drive_path)
            
            # 搜索所有settings目录下的指定文件
            pattern = f"**/settings/{filename}"
            
            for file_path in drive_path_obj.rglob(pattern):
                if self.cancel_event and self.cancel_event.is_set():
                    break
                
                # 验证文件确实存在且可访问
                if file_path.is_file():
                    found_files.append(str(file_path))
                    print(f"找到文件: {file_path}")
        
        except PermissionError:
            print(f"无权限访问驱动器: {drive_path}")
        except Exception as e:
            print(f"搜索目录 {drive_path} 时出错: {e}")
        
        return found_files
    
    @monitor_performance('ini_file_read')
    def read_ini_file(self, file_path: str) -> Optional[configparser.ConfigParser]:
        """
        读取INI文件（优化版本）
        
        Args:
            file_path: INI文件路径
            
        Returns:
            ConfigParser对象，失败时返回None
        """
        try:
            # 使用智能编码检测
            encoding = self.encoding_detector.detect_encoding(file_path)
            
            with open(file_path, 'r', encoding=encoding) as f:
                content = f.read()
            
            # 修复可能的格式问题
            corrected_content = self._fix_ini_format(content)
            
            config = configparser.ConfigParser(allow_no_value=True, strict=False)
            config.read_string(corrected_content)
            
            print(f"成功读取INI文件: {file_path} (编码: {encoding})")
            return config
            
        except Exception as e:
            print(f"读取INI文件时出错: {e}")
            return None
    
    def _fix_ini_format(self, content: str) -> str:
        """修复INI文件格式问题"""
        lines = content.splitlines()
        corrected_lines = []
        
        for line in lines:
            stripped_line = line.strip()
            # 修复缺少结束括号的section头
            if stripped_line.startswith('[') and not stripped_line.endswith(']'):
                corrected_lines.append(stripped_line + ']')
                print(f"修复section头: '{stripped_line}' -> '{stripped_line}]'")
            else:
                corrected_lines.append(line)
        
        return '\n'.join(corrected_lines)
    
    @monitor_performance('ini_file_write')
    def write_ini_file(self, file_path: str, config: configparser.ConfigParser) -> bool:
        """
        写入INI文件（优化版本）
        
        Args:
            file_path: INI文件路径
            config: ConfigParser对象
            
        Returns:
            写入是否成功
        """
        try:
            # 创建备份
            backup_path = f"{file_path}.backup"
            if os.path.exists(file_path):
                import shutil
                shutil.copy2(file_path, backup_path)
            
            # 使用检测到的编码写入
            encoding = self.encoding_detector.detect_encoding(file_path) if os.path.exists(file_path) else 'utf-8'
            
            # 写入新内容
            with open(file_path, 'w', encoding=encoding) as f:
                config.write(f)
            
            print(f"成功写入INI文件: {file_path} (编码: {encoding})")
            return True
            
        except Exception as e:
            print(f"写入INI文件时出错: {e}")
            # 尝试恢复备份
            if os.path.exists(backup_path):
                try:
                    import shutil
                    shutil.copy2(backup_path, file_path)
                    print("已恢复备份文件")
                except:
                    pass
            return False

    def update_ini_section(self, config: configparser.ConfigParser,
                          section_name: str, section_data: Dict[str, Any]) -> bool:
        """
        更新INI文件的section（优化版本）

        Args:
            config: ConfigParser对象
            section_name: section名称
            section_data: section数据

        Returns:
            是否有更新
        """
        updated = False

        # 如果section不存在，创建它
        if not config.has_section(section_name):
            config.add_section(section_name)
            updated = True
            print(f"创建新section: {section_name}")

        # 更新section中的键值对
        for key, value in section_data.items():
            current_value = config.get(section_name, key, fallback=None)
            if current_value != str(value):
                config.set(section_name, key, str(value))
                updated = True
                print(f"更新 {section_name}.{key}: {current_value} -> {value}")

        return updated

    def validate_file_path(self, file_path: str) -> bool:
        """验证文件路径是否有效（优化版本）"""
        try:
            path_obj = Path(file_path)
            return path_obj.exists() and path_obj.is_file()
        except:
            return False

    @cached_calculation
    def get_file_info(self, file_path: str) -> Dict[str, Any]:
        """获取文件信息（缓存版本）"""
        try:
            path_obj = Path(file_path)
            stat = path_obj.stat()
            # 使用适当的创建时间属性
            created_time = getattr(stat, 'st_birthtime', stat.st_ctime)

            return {
                'size': stat.st_size,
                'modified': stat.st_mtime,
                'created': created_time,
                'exists': True,
                'encoding': self.encoding_detector.detect_encoding(file_path) if path_obj.suffix.lower() in ['.ini', '.txt', '.cfg'] else None
            }
        except:
            return {'exists': False}

    def set_cancel_event(self, cancel_event: threading.Event):
        """设置取消事件"""
        self.cancel_event = cancel_event

    def create_directory(self, dir_path: str) -> bool:
        """创建目录（优化版本）"""
        try:
            Path(dir_path).mkdir(parents=True, exist_ok=True)
            return True
        except Exception as e:
            print(f"创建目录失败: {e}")
            return False

    def batch_read_ini_files(self, file_paths: List[str]) -> Dict[str, Optional[configparser.ConfigParser]]:
        """
        批量读取INI文件（新增功能）

        Args:
            file_paths: INI文件路径列表

        Returns:
            文件路径到ConfigParser对象的映射
        """
        results = {}

        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            future_to_path = {
                executor.submit(self.read_ini_file, file_path): file_path
                for file_path in file_paths
            }

            for future in as_completed(future_to_path):
                file_path = future_to_path[future]
                try:
                    config = future.result()
                    results[file_path] = config
                except Exception as e:
                    print(f"批量读取文件 {file_path} 时出错: {e}")
                    results[file_path] = None

        return results

    def get_statistics(self) -> Dict[str, Any]:
        """获取文件服务统计信息"""
        stats = {
            'search_count': self._search_count,
            'cache_hit_count': self._cache_hit_count,
            'cache_hit_rate': self._cache_hit_count / max(self._search_count, 1),
            'cache_enabled': self.search_cache is not None,
            'max_workers': self.max_workers,
            'encoding_cache_size': len(self.encoding_detector._encoding_cache)
        }

        if self.search_cache:
            stats.update({
                'search_cache_size': len(self.search_cache._cache)
            })

        return stats

    def clear_caches(self):
        """清空所有缓存"""
        if self.search_cache:
            self.search_cache.clear()

        self.encoding_detector._encoding_cache.clear()
        print("文件服务缓存已清空")

    def cleanup(self):
        """清理资源"""
        self.clear_caches()
        print("优化文件服务已清理")


# 兼容性别名，保持与原FileService的接口一致
class FileService(OptimizedFileService):
    """兼容性别名"""
    pass
