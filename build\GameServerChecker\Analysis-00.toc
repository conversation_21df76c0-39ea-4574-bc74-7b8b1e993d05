(['C:\\Users\\<USER>\\Desktop\\Project\\gg6\\source_code\\main_pyqt6.py'],
 ['C:\\Users\\<USER>\\Desktop\\Project\\gg6\\source_code',
  'C:\\Users\\<USER>\\Desktop\\Project\\gg6'],
 ['psutil',
  'ntplib',
  'chardet',
  'requests',
  'schedule',
  'keyword',
  'win32api',
  'win32con',
  'win32gui',
  'win32process',
  'pywintypes',
  'PyQt6.QtCore',
  'PyQt6.QtGui',
  'PyQt6.QtWidgets',
  'PyQt6.QtNetwork',
  'core.app_pyqt6',
  'core.config',
  'core.constants',
  'core.event_handler',
  'core.file_manager',
  'core.schedule_manager',
  'core.ui_coordinator',
  'core.upload_manager',
  'gui.components_pyqt6',
  'gui.dialogs_pyqt6',
  'gui.styles_pyqt6',
  'gui.time_input_widget',
  'gui.ui_constants',
  'gui.ui_factory',
  'gui.custom_checkbox',
  'services.unified_network_service',
  'services.optimized_file_service',
  'services.qt_scheduler_service',
  'services.qt_tray_service',
  'services.configuration_service',
  'services.file_processing_service',
  'utils.pyqt6_threading',
  'utils.performance_optimizer',
  'utils.system',
  'utils.dpi_adapter',
  'utils.high_dpi_fix',
  'utils.sequential_executor',
  'utils.process_control',
  'utils.startup_optimization',
  'utils.ui_optimizer',
  'utils.lazy_import_manager',
  'utils.crash_fix',
  'utils.windows_startup',
  'utils.smart_logger'],
 [('C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks',
   -1000),
  ('C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\_pyinstaller_hooks_contrib',
   -1000)],
 {},
 ['pytest',
  'unittest',
  'doctest',
  'pdb',
  'debugpy',
  'IPython',
  'ipython',
  'jupyter',
  'notebook',
  'tkinter',
  'tkinter.ttk',
  'turtle',
  'curses',
  'readline',
  'rlcompleter',
  'code',
  'codeop',
  'pydoc',
  'inspect',
  'profile',
  'cProfile',
  'pstats',
  'timeit',
  'trace',
  'dis',
  'pickletools',
  'py_compile',
  'compileall',
  'tokenize',
  'ast',
  'parser',
  'symbol',
  'token',
  'ftplib',
  'poplib',
  'imaplib',
  'nntplib',
  'smtplib',
  'telnetlib',
  'xmlrpc',
  'http.server',
  'wsgiref',
  'CGIHTTPServer',
  'SimpleHTTPServer',
  'BaseHTTPServer',
  'sqlite3',
  'dbm',
  'xml.dom',
  'xml.sax',
  'xml.parsers',
  'xml.etree',
  'html.parser',
  'html.entities',
  'sgmllib',
  'htmllib',
  'formatter',
  'mailbox',
  'mimetypes',
  'base64',
  'binhex',
  'binascii',
  'uu',
  'quopri',
  'audioop',
  'imageop',
  'rgbimg',
  'wave',
  'chunk',
  'sunau',
  'aifc',
  'sndhdr',
  'ossaudiodev',
  'linuxaudiodev',
  'winsound',
  'tkinter',
  'Tkinter',
  'turtle',
  'turtledemo',
  'test',
  'tests',
  'testing',
  'nose',
  'mock',
  'pydoc',
  'pydoc_data',
  'help',
  'antigravity',
  'this',
  'ssl',
  '_ssl',
  'OpenSSL',
  'pyOpenSSL',
  'cryptography',
  'distutils',
  'setuptools',
  'pip',
  'wheel',
  'pkg_resources',
  'easy_install',
  'numpy',
  'scipy',
  'matplotlib',
  'pandas',
  'sympy',
  '__main__'],
 [],
 False,
 {},
 2,
 [],
 [('assets\\icon.ico',
   'C:\\Users\\<USER>\\Desktop\\Project\\gg6\\source_code\\assets\\icon.ico',
   'DATA')],
 '3.13.5 (tags/v3.13.5:6cb20a2, Jun 11 2025, 16:15:46) [MSC v.1943 64 bit '
 '(AMD64)]',
 [('pyi_rth_pkgutil',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_pyqt6',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pyqt6.py',
   'PYSOURCE'),
  ('pyi_rth_pywintypes',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks\\pyi_rth_pywintypes.py',
   'PYSOURCE'),
  ('main_pyqt6',
   'C:\\Users\\<USER>\\Desktop\\Project\\gg6\\source_code\\main_pyqt6.py',
   'PYSOURCE-2')],
 [('_pyi_rth_utils.qt',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\qt.py',
   'PYMODULE-2'),
  ('importlib',
   'C:\\Program Files\\Python313\\Lib\\importlib\\__init__.py',
   'PYMODULE-2'),
  ('importlib.machinery',
   'C:\\Program Files\\Python313\\Lib\\importlib\\machinery.py',
   'PYMODULE-2'),
  ('importlib._bootstrap_external',
   'C:\\Program Files\\Python313\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE-2'),
  ('importlib.metadata',
   'C:\\Program Files\\Python313\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE-2'),
  ('csv', 'C:\\Program Files\\Python313\\Lib\\csv.py', 'PYMODULE-2'),
  ('importlib.metadata._adapters',
   'C:\\Program Files\\Python313\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE-2'),
  ('importlib.metadata._text',
   'C:\\Program Files\\Python313\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE-2'),
  ('email.message',
   'C:\\Program Files\\Python313\\Lib\\email\\message.py',
   'PYMODULE-2'),
  ('email.policy',
   'C:\\Program Files\\Python313\\Lib\\email\\policy.py',
   'PYMODULE-2'),
  ('email.contentmanager',
   'C:\\Program Files\\Python313\\Lib\\email\\contentmanager.py',
   'PYMODULE-2'),
  ('email.quoprimime',
   'C:\\Program Files\\Python313\\Lib\\email\\quoprimime.py',
   'PYMODULE-2'),
  ('string', 'C:\\Program Files\\Python313\\Lib\\string.py', 'PYMODULE-2'),
  ('email.headerregistry',
   'C:\\Program Files\\Python313\\Lib\\email\\headerregistry.py',
   'PYMODULE-2'),
  ('email._header_value_parser',
   'C:\\Program Files\\Python313\\Lib\\email\\_header_value_parser.py',
   'PYMODULE-2'),
  ('urllib',
   'C:\\Program Files\\Python313\\Lib\\urllib\\__init__.py',
   'PYMODULE-2'),
  ('email.iterators',
   'C:\\Program Files\\Python313\\Lib\\email\\iterators.py',
   'PYMODULE-2'),
  ('email.generator',
   'C:\\Program Files\\Python313\\Lib\\email\\generator.py',
   'PYMODULE-2'),
  ('copy', 'C:\\Program Files\\Python313\\Lib\\copy.py', 'PYMODULE-2'),
  ('random', 'C:\\Program Files\\Python313\\Lib\\random.py', 'PYMODULE-2'),
  ('argparse', 'C:\\Program Files\\Python313\\Lib\\argparse.py', 'PYMODULE-2'),
  ('shutil', 'C:\\Program Files\\Python313\\Lib\\shutil.py', 'PYMODULE-2'),
  ('tarfile', 'C:\\Program Files\\Python313\\Lib\\tarfile.py', 'PYMODULE-2'),
  ('gzip', 'C:\\Program Files\\Python313\\Lib\\gzip.py', 'PYMODULE-2'),
  ('_compression',
   'C:\\Program Files\\Python313\\Lib\\_compression.py',
   'PYMODULE-2'),
  ('struct', 'C:\\Program Files\\Python313\\Lib\\struct.py', 'PYMODULE-2'),
  ('lzma', 'C:\\Program Files\\Python313\\Lib\\lzma.py', 'PYMODULE-2'),
  ('bz2', 'C:\\Program Files\\Python313\\Lib\\bz2.py', 'PYMODULE-2'),
  ('fnmatch', 'C:\\Program Files\\Python313\\Lib\\fnmatch.py', 'PYMODULE-2'),
  ('gettext', 'C:\\Program Files\\Python313\\Lib\\gettext.py', 'PYMODULE-2'),
  ('statistics',
   'C:\\Program Files\\Python313\\Lib\\statistics.py',
   'PYMODULE-2'),
  ('decimal', 'C:\\Program Files\\Python313\\Lib\\decimal.py', 'PYMODULE-2'),
  ('_pydecimal',
   'C:\\Program Files\\Python313\\Lib\\_pydecimal.py',
   'PYMODULE-2'),
  ('contextvars',
   'C:\\Program Files\\Python313\\Lib\\contextvars.py',
   'PYMODULE-2'),
  ('fractions',
   'C:\\Program Files\\Python313\\Lib\\fractions.py',
   'PYMODULE-2'),
  ('numbers', 'C:\\Program Files\\Python313\\Lib\\numbers.py', 'PYMODULE-2'),
  ('hashlib', 'C:\\Program Files\\Python313\\Lib\\hashlib.py', 'PYMODULE-2'),
  ('logging',
   'C:\\Program Files\\Python313\\Lib\\logging\\__init__.py',
   'PYMODULE-2'),
  ('pickle', 'C:\\Program Files\\Python313\\Lib\\pickle.py', 'PYMODULE-2'),
  ('pprint', 'C:\\Program Files\\Python313\\Lib\\pprint.py', 'PYMODULE-2'),
  ('dataclasses',
   'C:\\Program Files\\Python313\\Lib\\dataclasses.py',
   'PYMODULE-2'),
  ('_compat_pickle',
   'C:\\Program Files\\Python313\\Lib\\_compat_pickle.py',
   'PYMODULE-2'),
  ('threading',
   'C:\\Program Files\\Python313\\Lib\\threading.py',
   'PYMODULE-2'),
  ('_threading_local',
   'C:\\Program Files\\Python313\\Lib\\_threading_local.py',
   'PYMODULE-2'),
  ('bisect', 'C:\\Program Files\\Python313\\Lib\\bisect.py', 'PYMODULE-2'),
  ('email._encoded_words',
   'C:\\Program Files\\Python313\\Lib\\email\\_encoded_words.py',
   'PYMODULE-2'),
  ('email.charset',
   'C:\\Program Files\\Python313\\Lib\\email\\charset.py',
   'PYMODULE-2'),
  ('email.encoders',
   'C:\\Program Files\\Python313\\Lib\\email\\encoders.py',
   'PYMODULE-2'),
  ('email.base64mime',
   'C:\\Program Files\\Python313\\Lib\\email\\base64mime.py',
   'PYMODULE-2'),
  ('email._policybase',
   'C:\\Program Files\\Python313\\Lib\\email\\_policybase.py',
   'PYMODULE-2'),
  ('email.header',
   'C:\\Program Files\\Python313\\Lib\\email\\header.py',
   'PYMODULE-2'),
  ('email.errors',
   'C:\\Program Files\\Python313\\Lib\\email\\errors.py',
   'PYMODULE-2'),
  ('email.utils',
   'C:\\Program Files\\Python313\\Lib\\email\\utils.py',
   'PYMODULE-2'),
  ('socket', 'C:\\Program Files\\Python313\\Lib\\socket.py', 'PYMODULE-2'),
  ('selectors',
   'C:\\Program Files\\Python313\\Lib\\selectors.py',
   'PYMODULE-2'),
  ('email._parseaddr',
   'C:\\Program Files\\Python313\\Lib\\email\\_parseaddr.py',
   'PYMODULE-2'),
  ('calendar', 'C:\\Program Files\\Python313\\Lib\\calendar.py', 'PYMODULE-2'),
  ('urllib.parse',
   'C:\\Program Files\\Python313\\Lib\\urllib\\parse.py',
   'PYMODULE-2'),
  ('ipaddress',
   'C:\\Program Files\\Python313\\Lib\\ipaddress.py',
   'PYMODULE-2'),
  ('datetime', 'C:\\Program Files\\Python313\\Lib\\datetime.py', 'PYMODULE-2'),
  ('_pydatetime',
   'C:\\Program Files\\Python313\\Lib\\_pydatetime.py',
   'PYMODULE-2'),
  ('_strptime',
   'C:\\Program Files\\Python313\\Lib\\_strptime.py',
   'PYMODULE-2'),
  ('typing', 'C:\\Program Files\\Python313\\Lib\\typing.py', 'PYMODULE-2'),
  ('importlib.abc',
   'C:\\Program Files\\Python313\\Lib\\importlib\\abc.py',
   'PYMODULE-2'),
  ('importlib.resources.abc',
   'C:\\Program Files\\Python313\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE-2'),
  ('importlib.resources',
   'C:\\Program Files\\Python313\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE-2'),
  ('importlib.resources._functional',
   'C:\\Program Files\\Python313\\Lib\\importlib\\resources\\_functional.py',
   'PYMODULE-2'),
  ('importlib.resources._common',
   'C:\\Program Files\\Python313\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE-2'),
  ('importlib.resources._adapters',
   'C:\\Program Files\\Python313\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE-2'),
  ('tempfile', 'C:\\Program Files\\Python313\\Lib\\tempfile.py', 'PYMODULE-2'),
  ('importlib._abc',
   'C:\\Program Files\\Python313\\Lib\\importlib\\_abc.py',
   'PYMODULE-2'),
  ('contextlib',
   'C:\\Program Files\\Python313\\Lib\\contextlib.py',
   'PYMODULE-2'),
  ('importlib.metadata._itertools',
   'C:\\Program Files\\Python313\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE-2'),
  ('importlib.metadata._functools',
   'C:\\Program Files\\Python313\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE-2'),
  ('importlib.metadata._collections',
   'C:\\Program Files\\Python313\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE-2'),
  ('importlib.metadata._meta',
   'C:\\Program Files\\Python313\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE-2'),
  ('textwrap', 'C:\\Program Files\\Python313\\Lib\\textwrap.py', 'PYMODULE-2'),
  ('zipfile',
   'C:\\Program Files\\Python313\\Lib\\zipfile\\__init__.py',
   'PYMODULE-2'),
  ('zipfile._path',
   'C:\\Program Files\\Python313\\Lib\\zipfile\\_path\\__init__.py',
   'PYMODULE-2'),
  ('zipfile._path.glob',
   'C:\\Program Files\\Python313\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE-2'),
  ('importlib.util',
   'C:\\Program Files\\Python313\\Lib\\importlib\\util.py',
   'PYMODULE-2'),
  ('pathlib',
   'C:\\Program Files\\Python313\\Lib\\pathlib\\__init__.py',
   'PYMODULE-2'),
  ('pathlib._local',
   'C:\\Program Files\\Python313\\Lib\\pathlib\\_local.py',
   'PYMODULE-2'),
  ('glob', 'C:\\Program Files\\Python313\\Lib\\glob.py', 'PYMODULE-2'),
  ('pathlib._abc',
   'C:\\Program Files\\Python313\\Lib\\pathlib\\_abc.py',
   'PYMODULE-2'),
  ('email',
   'C:\\Program Files\\Python313\\Lib\\email\\__init__.py',
   'PYMODULE-2'),
  ('email.parser',
   'C:\\Program Files\\Python313\\Lib\\email\\parser.py',
   'PYMODULE-2'),
  ('email.feedparser',
   'C:\\Program Files\\Python313\\Lib\\email\\feedparser.py',
   'PYMODULE-2'),
  ('json',
   'C:\\Program Files\\Python313\\Lib\\json\\__init__.py',
   'PYMODULE-2'),
  ('json.encoder',
   'C:\\Program Files\\Python313\\Lib\\json\\encoder.py',
   'PYMODULE-2'),
  ('json.decoder',
   'C:\\Program Files\\Python313\\Lib\\json\\decoder.py',
   'PYMODULE-2'),
  ('json.scanner',
   'C:\\Program Files\\Python313\\Lib\\json\\scanner.py',
   'PYMODULE-2'),
  ('__future__',
   'C:\\Program Files\\Python313\\Lib\\__future__.py',
   'PYMODULE-2'),
  ('importlib.readers',
   'C:\\Program Files\\Python313\\Lib\\importlib\\readers.py',
   'PYMODULE-2'),
  ('importlib.resources.readers',
   'C:\\Program Files\\Python313\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE-2'),
  ('importlib.resources._itertools',
   'C:\\Program Files\\Python313\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE-2'),
  ('importlib._bootstrap',
   'C:\\Program Files\\Python313\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE-2'),
  ('_pyi_rth_utils',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE-2'),
  ('subprocess',
   'C:\\Program Files\\Python313\\Lib\\subprocess.py',
   'PYMODULE-2'),
  ('signal', 'C:\\Program Files\\Python313\\Lib\\signal.py', 'PYMODULE-2'),
  ('multiprocessing.spawn',
   'C:\\Program Files\\Python313\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE-2'),
  ('multiprocessing.resource_tracker',
   'C:\\Program Files\\Python313\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE-2'),
  ('multiprocessing.util',
   'C:\\Program Files\\Python313\\Lib\\multiprocessing\\util.py',
   'PYMODULE-2'),
  ('multiprocessing.forkserver',
   'C:\\Program Files\\Python313\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE-2'),
  ('multiprocessing.connection',
   'C:\\Program Files\\Python313\\Lib\\multiprocessing\\connection.py',
   'PYMODULE-2'),
  ('multiprocessing.resource_sharer',
   'C:\\Program Files\\Python313\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE-2'),
  ('hmac', 'C:\\Program Files\\Python313\\Lib\\hmac.py', 'PYMODULE-2'),
  ('multiprocessing.context',
   'C:\\Program Files\\Python313\\Lib\\multiprocessing\\context.py',
   'PYMODULE-2'),
  ('multiprocessing.popen_spawn_win32',
   'C:\\Program Files\\Python313\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE-2'),
  ('multiprocessing.popen_forkserver',
   'C:\\Program Files\\Python313\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE-2'),
  ('multiprocessing.popen_spawn_posix',
   'C:\\Program Files\\Python313\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE-2'),
  ('multiprocessing.popen_fork',
   'C:\\Program Files\\Python313\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE-2'),
  ('multiprocessing.sharedctypes',
   'C:\\Program Files\\Python313\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE-2'),
  ('multiprocessing.heap',
   'C:\\Program Files\\Python313\\Lib\\multiprocessing\\heap.py',
   'PYMODULE-2'),
  ('ctypes',
   'C:\\Program Files\\Python313\\Lib\\ctypes\\__init__.py',
   'PYMODULE-2'),
  ('ctypes.util',
   'C:\\Program Files\\Python313\\Lib\\ctypes\\util.py',
   'PYMODULE-2'),
  ('ctypes._aix',
   'C:\\Program Files\\Python313\\Lib\\ctypes\\_aix.py',
   'PYMODULE-2'),
  ('ctypes.macholib.dyld',
   'C:\\Program Files\\Python313\\Lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE-2'),
  ('ctypes.macholib',
   'C:\\Program Files\\Python313\\Lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE-2'),
  ('ctypes.macholib.dylib',
   'C:\\Program Files\\Python313\\Lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE-2'),
  ('ctypes.macholib.framework',
   'C:\\Program Files\\Python313\\Lib\\ctypes\\macholib\\framework.py',
   'PYMODULE-2'),
  ('ctypes._endian',
   'C:\\Program Files\\Python313\\Lib\\ctypes\\_endian.py',
   'PYMODULE-2'),
  ('multiprocessing.pool',
   'C:\\Program Files\\Python313\\Lib\\multiprocessing\\pool.py',
   'PYMODULE-2'),
  ('multiprocessing.dummy',
   'C:\\Program Files\\Python313\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE-2'),
  ('multiprocessing.dummy.connection',
   'C:\\Program Files\\Python313\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE-2'),
  ('queue', 'C:\\Program Files\\Python313\\Lib\\queue.py', 'PYMODULE-2'),
  ('multiprocessing.queues',
   'C:\\Program Files\\Python313\\Lib\\multiprocessing\\queues.py',
   'PYMODULE-2'),
  ('multiprocessing.synchronize',
   'C:\\Program Files\\Python313\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE-2'),
  ('multiprocessing.managers',
   'C:\\Program Files\\Python313\\Lib\\multiprocessing\\managers.py',
   'PYMODULE-2'),
  ('multiprocessing.shared_memory',
   'C:\\Program Files\\Python313\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE-2'),
  ('secrets', 'C:\\Program Files\\Python313\\Lib\\secrets.py', 'PYMODULE-2'),
  ('multiprocessing.reduction',
   'C:\\Program Files\\Python313\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE-2'),
  ('multiprocessing.process',
   'C:\\Program Files\\Python313\\Lib\\multiprocessing\\process.py',
   'PYMODULE-2'),
  ('runpy', 'C:\\Program Files\\Python313\\Lib\\runpy.py', 'PYMODULE-2'),
  ('pkgutil', 'C:\\Program Files\\Python313\\Lib\\pkgutil.py', 'PYMODULE-2'),
  ('zipimport',
   'C:\\Program Files\\Python313\\Lib\\zipimport.py',
   'PYMODULE-2'),
  ('multiprocessing',
   'C:\\Program Files\\Python313\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE-2'),
  ('utils.smart_logger',
   'C:\\Users\\<USER>\\Desktop\\Project\\gg6\\source_code\\utils\\smart_logger.py',
   'PYMODULE-2'),
  ('utils',
   'C:\\Users\\<USER>\\Desktop\\Project\\gg6\\source_code\\utils\\__init__.py',
   'PYMODULE-2'),
  ('utils.windows_startup',
   'C:\\Users\\<USER>\\Desktop\\Project\\gg6\\source_code\\utils\\windows_startup.py',
   'PYMODULE-2'),
  ('utils.crash_fix',
   'C:\\Users\\<USER>\\Desktop\\Project\\gg6\\source_code\\utils\\crash_fix.py',
   'PYMODULE-2'),
  ('utils.lazy_import_manager',
   'C:\\Users\\<USER>\\Desktop\\Project\\gg6\\source_code\\utils\\lazy_import_manager.py',
   'PYMODULE-2'),
  ('utils.ui_optimizer',
   'C:\\Users\\<USER>\\Desktop\\Project\\gg6\\source_code\\utils\\ui_optimizer.py',
   'PYMODULE-2'),
  ('utils.startup_optimization',
   'C:\\Users\\<USER>\\Desktop\\Project\\gg6\\source_code\\utils\\startup_optimization.py',
   'PYMODULE-2'),
  ('utils.process_control',
   'C:\\Users\\<USER>\\Desktop\\Project\\gg6\\source_code\\utils\\process_control.py',
   'PYMODULE-2'),
  ('utils.sequential_executor',
   'C:\\Users\\<USER>\\Desktop\\Project\\gg6\\source_code\\utils\\sequential_executor.py',
   'PYMODULE-2'),
  ('utils.performance_optimizer',
   'C:\\Users\\<USER>\\Desktop\\Project\\gg6\\source_code\\utils\\performance_optimizer.py',
   'PYMODULE-2'),
  ('utils.pyqt6_threading',
   'C:\\Users\\<USER>\\Desktop\\Project\\gg6\\source_code\\utils\\pyqt6_threading.py',
   'PYMODULE-2'),
  ('services.file_processing_service',
   'C:\\Users\\<USER>\\Desktop\\Project\\gg6\\source_code\\services\\file_processing_service.py',
   'PYMODULE-2'),
  ('services',
   'C:\\Users\\<USER>\\Desktop\\Project\\gg6\\source_code\\services\\__init__.py',
   'PYMODULE-2'),
  ('concurrent.futures',
   'C:\\Program Files\\Python313\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE-2'),
  ('concurrent.futures.thread',
   'C:\\Program Files\\Python313\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE-2'),
  ('concurrent.futures.process',
   'C:\\Program Files\\Python313\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE-2'),
  ('concurrent.futures._base',
   'C:\\Program Files\\Python313\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE-2'),
  ('concurrent',
   'C:\\Program Files\\Python313\\Lib\\concurrent\\__init__.py',
   'PYMODULE-2'),
  ('services.configuration_service',
   'C:\\Users\\<USER>\\Desktop\\Project\\gg6\\source_code\\services\\configuration_service.py',
   'PYMODULE-2'),
  ('services.qt_tray_service',
   'C:\\Users\\<USER>\\Desktop\\Project\\gg6\\source_code\\services\\qt_tray_service.py',
   'PYMODULE-2'),
  ('services.qt_scheduler_service',
   'C:\\Users\\<USER>\\Desktop\\Project\\gg6\\source_code\\services\\qt_scheduler_service.py',
   'PYMODULE-2'),
  ('services.optimized_file_service',
   'C:\\Users\\<USER>\\Desktop\\Project\\gg6\\source_code\\services\\optimized_file_service.py',
   'PYMODULE-2'),
  ('configparser',
   'C:\\Program Files\\Python313\\Lib\\configparser.py',
   'PYMODULE-2'),
  ('services.unified_network_service',
   'C:\\Users\\<USER>\\Desktop\\Project\\gg6\\source_code\\services\\unified_network_service.py',
   'PYMODULE-2'),
  ('gui.custom_checkbox',
   'C:\\Users\\<USER>\\Desktop\\Project\\gg6\\source_code\\gui\\custom_checkbox.py',
   'PYMODULE-2'),
  ('gui',
   'C:\\Users\\<USER>\\Desktop\\Project\\gg6\\source_code\\gui\\__init__.py',
   'PYMODULE-2'),
  ('gui.ui_factory',
   'C:\\Users\\<USER>\\Desktop\\Project\\gg6\\source_code\\gui\\ui_factory.py',
   'PYMODULE-2'),
  ('gui.ui_constants',
   'C:\\Users\\<USER>\\Desktop\\Project\\gg6\\source_code\\gui\\ui_constants.py',
   'PYMODULE-2'),
  ('gui.time_input_widget',
   'C:\\Users\\<USER>\\Desktop\\Project\\gg6\\source_code\\gui\\time_input_widget.py',
   'PYMODULE-2'),
  ('gui.dialogs_pyqt6',
   'C:\\Users\\<USER>\\Desktop\\Project\\gg6\\source_code\\gui\\dialogs_pyqt6.py',
   'PYMODULE-2'),
  ('gui.components_pyqt6',
   'C:\\Users\\<USER>\\Desktop\\Project\\gg6\\source_code\\gui\\components_pyqt6.py',
   'PYMODULE-2'),
  ('core.upload_manager',
   'C:\\Users\\<USER>\\Desktop\\Project\\gg6\\source_code\\core\\upload_manager.py',
   'PYMODULE-2'),
  ('core',
   'C:\\Users\\<USER>\\Desktop\\Project\\gg6\\source_code\\core\\__init__.py',
   'PYMODULE-2'),
  ('core.ui_coordinator',
   'C:\\Users\\<USER>\\Desktop\\Project\\gg6\\source_code\\core\\ui_coordinator.py',
   'PYMODULE-2'),
  ('core.schedule_manager',
   'C:\\Users\\<USER>\\Desktop\\Project\\gg6\\source_code\\core\\schedule_manager.py',
   'PYMODULE-2'),
  ('core.file_manager',
   'C:\\Users\\<USER>\\Desktop\\Project\\gg6\\source_code\\core\\file_manager.py',
   'PYMODULE-2'),
  ('core.event_handler',
   'C:\\Users\\<USER>\\Desktop\\Project\\gg6\\source_code\\core\\event_handler.py',
   'PYMODULE-2'),
  ('core.constants',
   'C:\\Users\\<USER>\\Desktop\\Project\\gg6\\source_code\\core\\constants.py',
   'PYMODULE-2'),
  ('core.config',
   'C:\\Users\\<USER>\\Desktop\\Project\\gg6\\source_code\\core\\config.py',
   'PYMODULE-2'),
  ('PyQt6',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\PyQt6\\__init__.py',
   'PYMODULE-2'),
  ('pywintypes',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\win32\\lib\\pywintypes.py',
   'PYMODULE-2'),
  ('pywin32_system32', '-', 'PYMODULE-2'),
  ('win32con',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\win32\\lib\\win32con.py',
   'PYMODULE-2'),
  ('schedule',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\schedule\\__init__.py',
   'PYMODULE-2'),
  ('requests',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\requests\\__init__.py',
   'PYMODULE-2'),
  ('requests.status_codes',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\requests\\status_codes.py',
   'PYMODULE-2'),
  ('requests.structures',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\requests\\structures.py',
   'PYMODULE-2'),
  ('requests.compat',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\requests\\compat.py',
   'PYMODULE-2'),
  ('urllib.request',
   'C:\\Program Files\\Python313\\Lib\\urllib\\request.py',
   'PYMODULE-2'),
  ('getpass', 'C:\\Program Files\\Python313\\Lib\\getpass.py', 'PYMODULE-2'),
  ('nturl2path',
   'C:\\Program Files\\Python313\\Lib\\nturl2path.py',
   'PYMODULE-2'),
  ('urllib.response',
   'C:\\Program Files\\Python313\\Lib\\urllib\\response.py',
   'PYMODULE-2'),
  ('urllib.error',
   'C:\\Program Files\\Python313\\Lib\\urllib\\error.py',
   'PYMODULE-2'),
  ('http.client',
   'C:\\Program Files\\Python313\\Lib\\http\\client.py',
   'PYMODULE-2'),
  ('http.cookies',
   'C:\\Program Files\\Python313\\Lib\\http\\cookies.py',
   'PYMODULE-2'),
  ('http.cookiejar',
   'C:\\Program Files\\Python313\\Lib\\http\\cookiejar.py',
   'PYMODULE-2'),
  ('http',
   'C:\\Program Files\\Python313\\Lib\\http\\__init__.py',
   'PYMODULE-2'),
  ('requests.models',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\requests\\models.py',
   'PYMODULE-2'),
  ('idna',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\idna\\__init__.py',
   'PYMODULE-2'),
  ('idna.package_data',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\idna\\package_data.py',
   'PYMODULE-2'),
  ('idna.intranges',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\idna\\intranges.py',
   'PYMODULE-2'),
  ('idna.core',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\idna\\core.py',
   'PYMODULE-2'),
  ('idna.uts46data',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE-2'),
  ('idna.idnadata',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE-2'),
  ('requests.hooks',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\requests\\hooks.py',
   'PYMODULE-2'),
  ('requests.cookies',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\requests\\cookies.py',
   'PYMODULE-2'),
  ('requests.auth',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\requests\\auth.py',
   'PYMODULE-2'),
  ('requests._internal_utils',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE-2'),
  ('urllib3.util',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE-2'),
  ('urllib3.util.wait',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE-2'),
  ('urllib3.util.url',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE-2'),
  ('urllib3.util.util',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\urllib3\\util\\util.py',
   'PYMODULE-2'),
  ('urllib3.util.timeout',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE-2'),
  ('urllib3.util.ssl_',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE-2'),
  ('urllib3.util.ssltransport',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\urllib3\\util\\ssltransport.py',
   'PYMODULE-2'),
  ('typing_extensions',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\typing_extensions.py',
   'PYMODULE-2'),
  ('asyncio.coroutines',
   'C:\\Program Files\\Python313\\Lib\\asyncio\\coroutines.py',
   'PYMODULE-2'),
  ('asyncio',
   'C:\\Program Files\\Python313\\Lib\\asyncio\\__init__.py',
   'PYMODULE-2'),
  ('asyncio.unix_events',
   'C:\\Program Files\\Python313\\Lib\\asyncio\\unix_events.py',
   'PYMODULE-2'),
  ('asyncio.log',
   'C:\\Program Files\\Python313\\Lib\\asyncio\\log.py',
   'PYMODULE-2'),
  ('asyncio.windows_events',
   'C:\\Program Files\\Python313\\Lib\\asyncio\\windows_events.py',
   'PYMODULE-2'),
  ('asyncio.windows_utils',
   'C:\\Program Files\\Python313\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE-2'),
  ('asyncio.selector_events',
   'C:\\Program Files\\Python313\\Lib\\asyncio\\selector_events.py',
   'PYMODULE-2'),
  ('asyncio.proactor_events',
   'C:\\Program Files\\Python313\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE-2'),
  ('asyncio.base_subprocess',
   'C:\\Program Files\\Python313\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE-2'),
  ('asyncio.threads',
   'C:\\Program Files\\Python313\\Lib\\asyncio\\threads.py',
   'PYMODULE-2'),
  ('asyncio.taskgroups',
   'C:\\Program Files\\Python313\\Lib\\asyncio\\taskgroups.py',
   'PYMODULE-2'),
  ('asyncio.subprocess',
   'C:\\Program Files\\Python313\\Lib\\asyncio\\subprocess.py',
   'PYMODULE-2'),
  ('asyncio.streams',
   'C:\\Program Files\\Python313\\Lib\\asyncio\\streams.py',
   'PYMODULE-2'),
  ('asyncio.runners',
   'C:\\Program Files\\Python313\\Lib\\asyncio\\runners.py',
   'PYMODULE-2'),
  ('asyncio.base_events',
   'C:\\Program Files\\Python313\\Lib\\asyncio\\base_events.py',
   'PYMODULE-2'),
  ('asyncio.trsock',
   'C:\\Program Files\\Python313\\Lib\\asyncio\\trsock.py',
   'PYMODULE-2'),
  ('asyncio.staggered',
   'C:\\Program Files\\Python313\\Lib\\asyncio\\staggered.py',
   'PYMODULE-2'),
  ('asyncio.timeouts',
   'C:\\Program Files\\Python313\\Lib\\asyncio\\timeouts.py',
   'PYMODULE-2'),
  ('asyncio.tasks',
   'C:\\Program Files\\Python313\\Lib\\asyncio\\tasks.py',
   'PYMODULE-2'),
  ('asyncio.queues',
   'C:\\Program Files\\Python313\\Lib\\asyncio\\queues.py',
   'PYMODULE-2'),
  ('asyncio.base_tasks',
   'C:\\Program Files\\Python313\\Lib\\asyncio\\base_tasks.py',
   'PYMODULE-2'),
  ('asyncio.locks',
   'C:\\Program Files\\Python313\\Lib\\asyncio\\locks.py',
   'PYMODULE-2'),
  ('asyncio.mixins',
   'C:\\Program Files\\Python313\\Lib\\asyncio\\mixins.py',
   'PYMODULE-2'),
  ('asyncio.sslproto',
   'C:\\Program Files\\Python313\\Lib\\asyncio\\sslproto.py',
   'PYMODULE-2'),
  ('asyncio.transports',
   'C:\\Program Files\\Python313\\Lib\\asyncio\\transports.py',
   'PYMODULE-2'),
  ('asyncio.protocols',
   'C:\\Program Files\\Python313\\Lib\\asyncio\\protocols.py',
   'PYMODULE-2'),
  ('asyncio.futures',
   'C:\\Program Files\\Python313\\Lib\\asyncio\\futures.py',
   'PYMODULE-2'),
  ('asyncio.base_futures',
   'C:\\Program Files\\Python313\\Lib\\asyncio\\base_futures.py',
   'PYMODULE-2'),
  ('asyncio.exceptions',
   'C:\\Program Files\\Python313\\Lib\\asyncio\\exceptions.py',
   'PYMODULE-2'),
  ('asyncio.events',
   'C:\\Program Files\\Python313\\Lib\\asyncio\\events.py',
   'PYMODULE-2'),
  ('asyncio.format_helpers',
   'C:\\Program Files\\Python313\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE-2'),
  ('asyncio.constants',
   'C:\\Program Files\\Python313\\Lib\\asyncio\\constants.py',
   'PYMODULE-2'),
  ('urllib3.util.retry',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE-2'),
  ('urllib3.response',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\urllib3\\response.py',
   'PYMODULE-2'),
  ('urllib3.connection',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE-2'),
  ('urllib3.util.ssl_match_hostname',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\urllib3\\util\\ssl_match_hostname.py',
   'PYMODULE-2'),
  ('urllib3._version',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\urllib3\\_version.py',
   'PYMODULE-2'),
  ('urllib3.http2.probe',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\urllib3\\http2\\probe.py',
   'PYMODULE-2'),
  ('urllib3.http2',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\urllib3\\http2\\__init__.py',
   'PYMODULE-2'),
  ('urllib3.http2.connection',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\urllib3\\http2\\connection.py',
   'PYMODULE-2'),
  ('urllib3._collections',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE-2'),
  ('urllib3._base_connection',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\urllib3\\_base_connection.py',
   'PYMODULE-2'),
  ('urllib3.connectionpool',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE-2'),
  ('urllib3.util.proxy',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\urllib3\\util\\proxy.py',
   'PYMODULE-2'),
  ('urllib3._request_methods',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\urllib3\\_request_methods.py',
   'PYMODULE-2'),
  ('urllib3.util.response',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE-2'),
  ('urllib3.util.request',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE-2'),
  ('urllib3.util.connection',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE-2'),
  ('urllib3.filepost',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE-2'),
  ('urllib3.fields',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE-2'),
  ('requests.api',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\requests\\api.py',
   'PYMODULE-2'),
  ('requests.sessions',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\requests\\sessions.py',
   'PYMODULE-2'),
  ('requests.adapters',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\requests\\adapters.py',
   'PYMODULE-2'),
  ('urllib3.contrib.socks',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE-2'),
  ('urllib3.poolmanager',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE-2'),
  ('requests.__version__',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\requests\\__version__.py',
   'PYMODULE-2'),
  ('requests.utils',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\requests\\utils.py',
   'PYMODULE-2'),
  ('netrc', 'C:\\Program Files\\Python313\\Lib\\netrc.py', 'PYMODULE-2'),
  ('requests.certs',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\requests\\certs.py',
   'PYMODULE-2'),
  ('certifi',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE-2'),
  ('certifi.core',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\certifi\\core.py',
   'PYMODULE-2'),
  ('requests.packages',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\requests\\packages.py',
   'PYMODULE-2'),
  ('urllib3.exceptions',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE-2'),
  ('urllib3.contrib.pyopenssl',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE-2'),
  ('urllib3.contrib',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE-2'),
  ('charset_normalizer',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE-2'),
  ('charset_normalizer.version',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE-2'),
  ('charset_normalizer.utils',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE-2'),
  ('charset_normalizer.constant',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE-2'),
  ('charset_normalizer.models',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE-2'),
  ('charset_normalizer.cd',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE-2'),
  ('charset_normalizer.legacy',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE-2'),
  ('charset_normalizer.api',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE-2'),
  ('requests.exceptions',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\requests\\exceptions.py',
   'PYMODULE-2'),
  ('urllib3',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE-2'),
  ('urllib3.contrib.emscripten',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\__init__.py',
   'PYMODULE-2'),
  ('urllib3.contrib.emscripten.connection',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\connection.py',
   'PYMODULE-2'),
  ('urllib3.contrib.emscripten.response',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\response.py',
   'PYMODULE-2'),
  ('urllib3.contrib.emscripten.request',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\request.py',
   'PYMODULE-2'),
  ('urllib3.contrib.emscripten.fetch',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\fetch.py',
   'PYMODULE-2'),
  ('chardet',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\chardet\\__init__.py',
   'PYMODULE-2'),
  ('chardet.version',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\chardet\\version.py',
   'PYMODULE-2'),
  ('chardet.universaldetector',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\chardet\\universaldetector.py',
   'PYMODULE-2'),
  ('chardet.utf1632prober',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\chardet\\utf1632prober.py',
   'PYMODULE-2'),
  ('chardet.sbcsgroupprober',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\chardet\\sbcsgroupprober.py',
   'PYMODULE-2'),
  ('chardet.sbcharsetprober',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\chardet\\sbcharsetprober.py',
   'PYMODULE-2'),
  ('chardet.langturkishmodel',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\chardet\\langturkishmodel.py',
   'PYMODULE-2'),
  ('chardet.langthaimodel',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\chardet\\langthaimodel.py',
   'PYMODULE-2'),
  ('chardet.langrussianmodel',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\chardet\\langrussianmodel.py',
   'PYMODULE-2'),
  ('chardet.langhebrewmodel',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\chardet\\langhebrewmodel.py',
   'PYMODULE-2'),
  ('chardet.langgreekmodel',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\chardet\\langgreekmodel.py',
   'PYMODULE-2'),
  ('chardet.langbulgarianmodel',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\chardet\\langbulgarianmodel.py',
   'PYMODULE-2'),
  ('chardet.hebrewprober',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\chardet\\hebrewprober.py',
   'PYMODULE-2'),
  ('chardet.mbcsgroupprober',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\chardet\\mbcsgroupprober.py',
   'PYMODULE-2'),
  ('chardet.utf8prober',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\chardet\\utf8prober.py',
   'PYMODULE-2'),
  ('chardet.mbcssm',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\chardet\\mbcssm.py',
   'PYMODULE-2'),
  ('chardet.codingstatemachinedict',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\chardet\\codingstatemachinedict.py',
   'PYMODULE-2'),
  ('chardet.codingstatemachine',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\chardet\\codingstatemachine.py',
   'PYMODULE-2'),
  ('chardet.sjisprober',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\chardet\\sjisprober.py',
   'PYMODULE-2'),
  ('chardet.mbcharsetprober',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\chardet\\mbcharsetprober.py',
   'PYMODULE-2'),
  ('chardet.jpcntx',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\chardet\\jpcntx.py',
   'PYMODULE-2'),
  ('chardet.chardistribution',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\chardet\\chardistribution.py',
   'PYMODULE-2'),
  ('chardet.johabfreq',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\chardet\\johabfreq.py',
   'PYMODULE-2'),
  ('chardet.jisfreq',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\chardet\\jisfreq.py',
   'PYMODULE-2'),
  ('chardet.gb2312freq',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\chardet\\gb2312freq.py',
   'PYMODULE-2'),
  ('chardet.euctwfreq',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\chardet\\euctwfreq.py',
   'PYMODULE-2'),
  ('chardet.euckrfreq',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\chardet\\euckrfreq.py',
   'PYMODULE-2'),
  ('chardet.big5freq',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\chardet\\big5freq.py',
   'PYMODULE-2'),
  ('chardet.johabprober',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\chardet\\johabprober.py',
   'PYMODULE-2'),
  ('chardet.gb2312prober',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\chardet\\gb2312prober.py',
   'PYMODULE-2'),
  ('chardet.euctwprober',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\chardet\\euctwprober.py',
   'PYMODULE-2'),
  ('chardet.euckrprober',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\chardet\\euckrprober.py',
   'PYMODULE-2'),
  ('chardet.eucjpprober',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\chardet\\eucjpprober.py',
   'PYMODULE-2'),
  ('chardet.cp949prober',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\chardet\\cp949prober.py',
   'PYMODULE-2'),
  ('chardet.big5prober',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\chardet\\big5prober.py',
   'PYMODULE-2'),
  ('chardet.macromanprober',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\chardet\\macromanprober.py',
   'PYMODULE-2'),
  ('chardet.latin1prober',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\chardet\\latin1prober.py',
   'PYMODULE-2'),
  ('chardet.escprober',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\chardet\\escprober.py',
   'PYMODULE-2'),
  ('chardet.escsm',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\chardet\\escsm.py',
   'PYMODULE-2'),
  ('chardet.resultdict',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\chardet\\resultdict.py',
   'PYMODULE-2'),
  ('chardet.enums',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\chardet\\enums.py',
   'PYMODULE-2'),
  ('chardet.charsetprober',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\chardet\\charsetprober.py',
   'PYMODULE-2'),
  ('chardet.charsetgroupprober',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\chardet\\charsetgroupprober.py',
   'PYMODULE-2'),
  ('ntplib',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\ntplib.py',
   'PYMODULE-2'),
  ('psutil',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\psutil\\__init__.py',
   'PYMODULE-2'),
  ('psutil._pswindows',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\psutil\\_pswindows.py',
   'PYMODULE-2'),
  ('psutil._common',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\psutil\\_common.py',
   'PYMODULE-2'),
  ('_py_abc', 'C:\\Program Files\\Python313\\Lib\\_py_abc.py', 'PYMODULE-2'),
  ('tracemalloc',
   'C:\\Program Files\\Python313\\Lib\\tracemalloc.py',
   'PYMODULE-2'),
  ('stringprep',
   'C:\\Program Files\\Python313\\Lib\\stringprep.py',
   'PYMODULE-2'),
  ('_colorize',
   'C:\\Program Files\\Python313\\Lib\\_colorize.py',
   'PYMODULE-2'),
  ('utils.high_dpi_fix',
   'C:\\Users\\<USER>\\Desktop\\Project\\gg6\\source_code\\utils\\high_dpi_fix.py',
   'PYMODULE-2'),
  ('platform', 'C:\\Program Files\\Python313\\Lib\\platform.py', 'PYMODULE-2'),
  ('_ios_support',
   'C:\\Program Files\\Python313\\Lib\\_ios_support.py',
   'PYMODULE-2'),
  ('utils.dpi_adapter',
   'C:\\Users\\<USER>\\Desktop\\Project\\gg6\\source_code\\utils\\dpi_adapter.py',
   'PYMODULE-2'),
  ('gui.styles_pyqt6',
   'C:\\Users\\<USER>\\Desktop\\Project\\gg6\\source_code\\gui\\styles_pyqt6.py',
   'PYMODULE-2'),
  ('utils.system',
   'C:\\Users\\<USER>\\Desktop\\Project\\gg6\\source_code\\utils\\system.py',
   'PYMODULE-2'),
  ('ctypes.wintypes',
   'C:\\Program Files\\Python313\\Lib\\ctypes\\wintypes.py',
   'PYMODULE-2'),
  ('core.app_pyqt6',
   'C:\\Users\\<USER>\\Desktop\\Project\\gg6\\source_code\\core\\app_pyqt6.py',
   'PYMODULE-2'),
  ('core.startup_settings_manager',
   'C:\\Users\\<USER>\\Desktop\\Project\\gg6\\source_code\\core\\startup_settings_manager.py',
   'PYMODULE-2'),
  ('core.app_helpers',
   'C:\\Users\\<USER>\\Desktop\\Project\\gg6\\source_code\\core\\app_helpers.py',
   'PYMODULE-2')],
 [('python313.dll', 'C:\\Program Files\\Python313\\python313.dll', 'BINARY'),
  ('PyQt6\\Qt6\\plugins\\networkinformation\\qnetworklistmanager.dll',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\networkinformation\\qnetworklistmanager.dll',
   'BINARY'),
  ('libcrypto-3-x64.dll',
   'C:\\Program Files\\Git\\mingw64\\bin\\libcrypto-3-x64.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\tls\\qcertonlybackend.dll',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\tls\\qcertonlybackend.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\tls\\qopensslbackend.dll',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\tls\\qopensslbackend.dll',
   'BINARY'),
  ('libssl-3-x64.dll',
   'C:\\Program Files\\Git\\mingw64\\bin\\libssl-3-x64.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\tls\\qschannelbackend.dll',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\tls\\qschannelbackend.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\opengl32sw.dll',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\opengl32sw.dll',
   'BINARY'),
  ('pywin32_system32\\pywintypes313.dll',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\pywin32_system32\\pywintypes313.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qgif.dll',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qgif.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qicns.dll',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qicns.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\platforms\\qwindows.dll',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\platforms\\qwindows.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qpdf.dll',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qpdf.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qsvg.dll',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qsvg.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qtiff.dll',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qtiff.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\platforms\\qminimal.dll',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\platforms\\qminimal.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\platforms\\qoffscreen.dll',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\platforms\\qoffscreen.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\generic\\qtuiotouchplugin.dll',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\generic\\qtuiotouchplugin.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qtga.dll',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qtga.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qico.dll',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qico.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qwebp.dll',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qwebp.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qwbmp.dll',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qwbmp.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\iconengines\\qsvgicon.dll',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\iconengines\\qsvgicon.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qjpeg.dll',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qjpeg.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\styles\\qmodernwindowsstyle.dll',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\styles\\qmodernwindowsstyle.dll',
   'BINARY'),
  ('_lzma.pyd', 'C:\\Program Files\\Python313\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'C:\\Program Files\\Python313\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('_decimal.pyd',
   'C:\\Program Files\\Python313\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'C:\\Program Files\\Python313\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('select.pyd', 'C:\\Program Files\\Python313\\DLLs\\select.pyd', 'EXTENSION'),
  ('_socket.pyd',
   'C:\\Program Files\\Python313\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('unicodedata.pyd',
   'C:\\Program Files\\Python313\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('_multiprocessing.pyd',
   'C:\\Program Files\\Python313\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('_ctypes.pyd',
   'C:\\Program Files\\Python313\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('_queue.pyd', 'C:\\Program Files\\Python313\\DLLs\\_queue.pyd', 'EXTENSION'),
  ('PyQt6\\QtNetwork.pyd',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\PyQt6\\QtNetwork.pyd',
   'EXTENSION'),
  ('PyQt6\\sip.cp313-win_amd64.pyd',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\sip.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('win32\\_win32sysloader.pyd',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\win32\\_win32sysloader.pyd',
   'EXTENSION'),
  ('win32\\win32process.pyd',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\win32\\win32process.pyd',
   'EXTENSION'),
  ('win32\\win32gui.pyd',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\win32\\win32gui.pyd',
   'EXTENSION'),
  ('win32\\win32api.pyd',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\win32\\win32api.pyd',
   'EXTENSION'),
  ('_overlapped.pyd',
   'C:\\Program Files\\Python313\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'C:\\Program Files\\Python313\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp313-win_amd64.pyd',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\charset_normalizer\\md__mypyc.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md.cp313-win_amd64.pyd',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\charset_normalizer\\md.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('psutil\\_psutil_windows.pyd',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\psutil\\_psutil_windows.pyd',
   'EXTENSION'),
  ('_wmi.pyd', 'C:\\Program Files\\Python313\\DLLs\\_wmi.pyd', 'EXTENSION'),
  ('PyQt6\\QtGui.pyd',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\PyQt6\\QtGui.pyd',
   'EXTENSION'),
  ('PyQt6\\QtCore.pyd',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\PyQt6\\QtCore.pyd',
   'EXTENSION'),
  ('PyQt6\\QtWidgets.pyd',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\PyQt6\\QtWidgets.pyd',
   'EXTENSION'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll',
   'C:\\Program Files\\Python313\\VCRUNTIME140.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6Network.dll',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6Network.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\MSVCP140.dll',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\MSVCP140.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6Core.dll',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6Core.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll',
   'C:\\Program Files\\Python313\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6Gui.dll',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6Gui.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6Pdf.dll',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6Pdf.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6Svg.dll',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6Svg.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6Widgets.dll',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6Widgets.dll',
   'BINARY'),
  ('libcrypto-3.dll',
   'C:\\Program Files\\Python313\\DLLs\\libcrypto-3.dll',
   'BINARY'),
  ('libffi-8.dll',
   'C:\\Program Files\\Python313\\DLLs\\libffi-8.dll',
   'BINARY'),
  ('python3.dll', 'C:\\Program Files\\Python313\\python3.dll', 'BINARY'),
  ('PyQt6\\Qt6\\bin\\MSVCP140_2.dll',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\MSVCP140_2.dll',
   'BINARY'),
  ('ucrtbase.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\ucrtbase.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\VCRUNTIME140.dll',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\VCRUNTIME140.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\MSVCP140_1.dll',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\MSVCP140_1.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\VCRUNTIME140_1.dll',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-fibers-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-fibers-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY')],
 [],
 [],
 [('assets\\icon.ico',
   'C:\\Users\\<USER>\\Desktop\\Project\\gg6\\source_code\\assets\\icon.ico',
   'DATA'),
  ('certifi\\cacert.pem',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('certifi\\py.typed',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_it.qm',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_it.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_zh_CN.qm',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_zh_CN.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_hr.qm',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_hr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_hr.qm',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_hr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_nl.qm',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_nl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_gd.qm',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_gd.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_uk.qm',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_uk.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_fa.qm',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_fa.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_ca.qm',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_ca.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_bg.qm',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_bg.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_sl.qm',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_sl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_zh_TW.qm',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_zh_TW.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_uk.qm',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_uk.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_ru.qm',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_ru.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_da.qm',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_da.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_hu.qm',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_hu.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_lv.qm',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_lv.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_tr.qm',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_tr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_fr.qm',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_fr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_pl.qm',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_pl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_sk.qm',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_sk.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_sk.qm',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_sk.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_ca.qm',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_ca.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_pl.qm',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_pl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_sl.qm',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_sl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_it.qm',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_it.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_nl.qm',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_nl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_en.qm',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_en.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_ar.qm',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_ar.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_fa.qm',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_fa.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_nn.qm',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_nn.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_nn.qm',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_nn.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_bg.qm',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_bg.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_pt_BR.qm',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_pt_BR.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_en.qm',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_en.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_ja.qm',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_ja.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_tr.qm',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_tr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_fr.qm',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_fr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_lt.qm',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_lt.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_nl.qm',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_nl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_ja.qm',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_ja.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_hr.qm',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_hr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_lv.qm',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_lv.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_zh_CN.qm',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_zh_CN.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_bg.qm',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_bg.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_es.qm',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_es.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_he.qm',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_he.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_sk.qm',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_sk.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_nn.qm',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_nn.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_zh_TW.qm',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_zh_TW.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_hu.qm',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_hu.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_de.qm',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_de.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_fi.qm',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_fi.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_en.qm',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_en.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_pt_BR.qm',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_pt_BR.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_ru.qm',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_ru.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_gl.qm',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_gl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_zh_TW.qm',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_zh_TW.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_gl.qm',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_gl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_ko.qm',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_ko.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_ar.qm',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_ar.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_cs.qm',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_cs.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_ko.qm',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_ko.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_cs.qm',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_cs.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_es.qm',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_es.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_ka.qm',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_ka.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_ar.qm',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_ar.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_pt_BR.qm',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_pt_BR.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_ka.qm',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_ka.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_pt_PT.qm',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_pt_PT.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_de.qm',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_de.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_ko.qm',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_ko.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_ca.qm',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_ca.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_tr.qm',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_tr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_gd.qm',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_gd.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_uk.qm',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_uk.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_sv.qm',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_sv.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_es.qm',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_es.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_de.qm',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_de.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_fr.qm',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_fr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_zh_CN.qm',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_zh_CN.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_hu.qm',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_hu.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_ka.qm',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_ka.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_fi.qm',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_fi.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_pl.qm',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_pl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_cs.qm',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_cs.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_he.qm',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_he.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_ru.qm',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_ru.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_da.qm',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_da.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_ja.qm',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_ja.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_da.qm',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_da.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_it.qm',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_it.qm',
   'DATA'),
  ('base_library.zip',
   'C:\\Users\\<USER>\\Desktop\\Project\\gg6\\build\\GameServerChecker\\base_library.zip',
   'DATA')],
 [('posixpath', 'C:\\Program Files\\Python313\\Lib\\posixpath.py', 'PYMODULE'),
  ('codecs', 'C:\\Program Files\\Python313\\Lib\\codecs.py', 'PYMODULE'),
  ('ntpath', 'C:\\Program Files\\Python313\\Lib\\ntpath.py', 'PYMODULE'),
  ('reprlib', 'C:\\Program Files\\Python313\\Lib\\reprlib.py', 'PYMODULE'),
  ('re._parser',
   'C:\\Program Files\\Python313\\Lib\\re\\_parser.py',
   'PYMODULE'),
  ('re._constants',
   'C:\\Program Files\\Python313\\Lib\\re\\_constants.py',
   'PYMODULE'),
  ('re._compiler',
   'C:\\Program Files\\Python313\\Lib\\re\\_compiler.py',
   'PYMODULE'),
  ('re._casefix',
   'C:\\Program Files\\Python313\\Lib\\re\\_casefix.py',
   'PYMODULE'),
  ('re', 'C:\\Program Files\\Python313\\Lib\\re\\__init__.py', 'PYMODULE'),
  ('abc', 'C:\\Program Files\\Python313\\Lib\\abc.py', 'PYMODULE'),
  ('sre_parse', 'C:\\Program Files\\Python313\\Lib\\sre_parse.py', 'PYMODULE'),
  ('operator', 'C:\\Program Files\\Python313\\Lib\\operator.py', 'PYMODULE'),
  ('linecache', 'C:\\Program Files\\Python313\\Lib\\linecache.py', 'PYMODULE'),
  ('warnings', 'C:\\Program Files\\Python313\\Lib\\warnings.py', 'PYMODULE'),
  ('sre_constants',
   'C:\\Program Files\\Python313\\Lib\\sre_constants.py',
   'PYMODULE'),
  ('keyword', 'C:\\Program Files\\Python313\\Lib\\keyword.py', 'PYMODULE'),
  ('stat', 'C:\\Program Files\\Python313\\Lib\\stat.py', 'PYMODULE'),
  ('encodings.zlib_codec',
   'C:\\Program Files\\Python313\\Lib\\encodings\\zlib_codec.py',
   'PYMODULE'),
  ('encodings.uu_codec',
   'C:\\Program Files\\Python313\\Lib\\encodings\\uu_codec.py',
   'PYMODULE'),
  ('encodings.utf_8_sig',
   'C:\\Program Files\\Python313\\Lib\\encodings\\utf_8_sig.py',
   'PYMODULE'),
  ('encodings.utf_8',
   'C:\\Program Files\\Python313\\Lib\\encodings\\utf_8.py',
   'PYMODULE'),
  ('encodings.utf_7',
   'C:\\Program Files\\Python313\\Lib\\encodings\\utf_7.py',
   'PYMODULE'),
  ('encodings.utf_32_le',
   'C:\\Program Files\\Python313\\Lib\\encodings\\utf_32_le.py',
   'PYMODULE'),
  ('encodings.utf_32_be',
   'C:\\Program Files\\Python313\\Lib\\encodings\\utf_32_be.py',
   'PYMODULE'),
  ('encodings.utf_32',
   'C:\\Program Files\\Python313\\Lib\\encodings\\utf_32.py',
   'PYMODULE'),
  ('encodings.utf_16_le',
   'C:\\Program Files\\Python313\\Lib\\encodings\\utf_16_le.py',
   'PYMODULE'),
  ('encodings.utf_16_be',
   'C:\\Program Files\\Python313\\Lib\\encodings\\utf_16_be.py',
   'PYMODULE'),
  ('encodings.utf_16',
   'C:\\Program Files\\Python313\\Lib\\encodings\\utf_16.py',
   'PYMODULE'),
  ('encodings.unicode_escape',
   'C:\\Program Files\\Python313\\Lib\\encodings\\unicode_escape.py',
   'PYMODULE'),
  ('encodings.undefined',
   'C:\\Program Files\\Python313\\Lib\\encodings\\undefined.py',
   'PYMODULE'),
  ('encodings.tis_620',
   'C:\\Program Files\\Python313\\Lib\\encodings\\tis_620.py',
   'PYMODULE'),
  ('encodings.shift_jisx0213',
   'C:\\Program Files\\Python313\\Lib\\encodings\\shift_jisx0213.py',
   'PYMODULE'),
  ('encodings.shift_jis_2004',
   'C:\\Program Files\\Python313\\Lib\\encodings\\shift_jis_2004.py',
   'PYMODULE'),
  ('encodings.shift_jis',
   'C:\\Program Files\\Python313\\Lib\\encodings\\shift_jis.py',
   'PYMODULE'),
  ('encodings.rot_13',
   'C:\\Program Files\\Python313\\Lib\\encodings\\rot_13.py',
   'PYMODULE'),
  ('encodings.raw_unicode_escape',
   'C:\\Program Files\\Python313\\Lib\\encodings\\raw_unicode_escape.py',
   'PYMODULE'),
  ('encodings.quopri_codec',
   'C:\\Program Files\\Python313\\Lib\\encodings\\quopri_codec.py',
   'PYMODULE'),
  ('encodings.punycode',
   'C:\\Program Files\\Python313\\Lib\\encodings\\punycode.py',
   'PYMODULE'),
  ('encodings.ptcp154',
   'C:\\Program Files\\Python313\\Lib\\encodings\\ptcp154.py',
   'PYMODULE'),
  ('encodings.palmos',
   'C:\\Program Files\\Python313\\Lib\\encodings\\palmos.py',
   'PYMODULE'),
  ('encodings.oem',
   'C:\\Program Files\\Python313\\Lib\\encodings\\oem.py',
   'PYMODULE'),
  ('encodings.mbcs',
   'C:\\Program Files\\Python313\\Lib\\encodings\\mbcs.py',
   'PYMODULE'),
  ('encodings.mac_turkish',
   'C:\\Program Files\\Python313\\Lib\\encodings\\mac_turkish.py',
   'PYMODULE'),
  ('encodings.mac_romanian',
   'C:\\Program Files\\Python313\\Lib\\encodings\\mac_romanian.py',
   'PYMODULE'),
  ('encodings.mac_roman',
   'C:\\Program Files\\Python313\\Lib\\encodings\\mac_roman.py',
   'PYMODULE'),
  ('encodings.mac_latin2',
   'C:\\Program Files\\Python313\\Lib\\encodings\\mac_latin2.py',
   'PYMODULE'),
  ('encodings.mac_iceland',
   'C:\\Program Files\\Python313\\Lib\\encodings\\mac_iceland.py',
   'PYMODULE'),
  ('encodings.mac_greek',
   'C:\\Program Files\\Python313\\Lib\\encodings\\mac_greek.py',
   'PYMODULE'),
  ('encodings.mac_farsi',
   'C:\\Program Files\\Python313\\Lib\\encodings\\mac_farsi.py',
   'PYMODULE'),
  ('encodings.mac_cyrillic',
   'C:\\Program Files\\Python313\\Lib\\encodings\\mac_cyrillic.py',
   'PYMODULE'),
  ('encodings.mac_croatian',
   'C:\\Program Files\\Python313\\Lib\\encodings\\mac_croatian.py',
   'PYMODULE'),
  ('encodings.mac_arabic',
   'C:\\Program Files\\Python313\\Lib\\encodings\\mac_arabic.py',
   'PYMODULE'),
  ('encodings.latin_1',
   'C:\\Program Files\\Python313\\Lib\\encodings\\latin_1.py',
   'PYMODULE'),
  ('encodings.kz1048',
   'C:\\Program Files\\Python313\\Lib\\encodings\\kz1048.py',
   'PYMODULE'),
  ('encodings.koi8_u',
   'C:\\Program Files\\Python313\\Lib\\encodings\\koi8_u.py',
   'PYMODULE'),
  ('encodings.koi8_t',
   'C:\\Program Files\\Python313\\Lib\\encodings\\koi8_t.py',
   'PYMODULE'),
  ('encodings.koi8_r',
   'C:\\Program Files\\Python313\\Lib\\encodings\\koi8_r.py',
   'PYMODULE'),
  ('encodings.johab',
   'C:\\Program Files\\Python313\\Lib\\encodings\\johab.py',
   'PYMODULE'),
  ('encodings.iso8859_9',
   'C:\\Program Files\\Python313\\Lib\\encodings\\iso8859_9.py',
   'PYMODULE'),
  ('encodings.iso8859_8',
   'C:\\Program Files\\Python313\\Lib\\encodings\\iso8859_8.py',
   'PYMODULE'),
  ('encodings.iso8859_7',
   'C:\\Program Files\\Python313\\Lib\\encodings\\iso8859_7.py',
   'PYMODULE'),
  ('encodings.iso8859_6',
   'C:\\Program Files\\Python313\\Lib\\encodings\\iso8859_6.py',
   'PYMODULE'),
  ('encodings.iso8859_5',
   'C:\\Program Files\\Python313\\Lib\\encodings\\iso8859_5.py',
   'PYMODULE'),
  ('encodings.iso8859_4',
   'C:\\Program Files\\Python313\\Lib\\encodings\\iso8859_4.py',
   'PYMODULE'),
  ('encodings.iso8859_3',
   'C:\\Program Files\\Python313\\Lib\\encodings\\iso8859_3.py',
   'PYMODULE'),
  ('encodings.iso8859_2',
   'C:\\Program Files\\Python313\\Lib\\encodings\\iso8859_2.py',
   'PYMODULE'),
  ('encodings.iso8859_16',
   'C:\\Program Files\\Python313\\Lib\\encodings\\iso8859_16.py',
   'PYMODULE'),
  ('encodings.iso8859_15',
   'C:\\Program Files\\Python313\\Lib\\encodings\\iso8859_15.py',
   'PYMODULE'),
  ('encodings.iso8859_14',
   'C:\\Program Files\\Python313\\Lib\\encodings\\iso8859_14.py',
   'PYMODULE'),
  ('encodings.iso8859_13',
   'C:\\Program Files\\Python313\\Lib\\encodings\\iso8859_13.py',
   'PYMODULE'),
  ('encodings.iso8859_11',
   'C:\\Program Files\\Python313\\Lib\\encodings\\iso8859_11.py',
   'PYMODULE'),
  ('encodings.iso8859_10',
   'C:\\Program Files\\Python313\\Lib\\encodings\\iso8859_10.py',
   'PYMODULE'),
  ('encodings.iso8859_1',
   'C:\\Program Files\\Python313\\Lib\\encodings\\iso8859_1.py',
   'PYMODULE'),
  ('encodings.iso2022_kr',
   'C:\\Program Files\\Python313\\Lib\\encodings\\iso2022_kr.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_ext',
   'C:\\Program Files\\Python313\\Lib\\encodings\\iso2022_jp_ext.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_3',
   'C:\\Program Files\\Python313\\Lib\\encodings\\iso2022_jp_3.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2004',
   'C:\\Program Files\\Python313\\Lib\\encodings\\iso2022_jp_2004.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2',
   'C:\\Program Files\\Python313\\Lib\\encodings\\iso2022_jp_2.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_1',
   'C:\\Program Files\\Python313\\Lib\\encodings\\iso2022_jp_1.py',
   'PYMODULE'),
  ('encodings.iso2022_jp',
   'C:\\Program Files\\Python313\\Lib\\encodings\\iso2022_jp.py',
   'PYMODULE'),
  ('encodings.idna',
   'C:\\Program Files\\Python313\\Lib\\encodings\\idna.py',
   'PYMODULE'),
  ('encodings.hz',
   'C:\\Program Files\\Python313\\Lib\\encodings\\hz.py',
   'PYMODULE'),
  ('encodings.hp_roman8',
   'C:\\Program Files\\Python313\\Lib\\encodings\\hp_roman8.py',
   'PYMODULE'),
  ('encodings.hex_codec',
   'C:\\Program Files\\Python313\\Lib\\encodings\\hex_codec.py',
   'PYMODULE'),
  ('encodings.gbk',
   'C:\\Program Files\\Python313\\Lib\\encodings\\gbk.py',
   'PYMODULE'),
  ('encodings.gb2312',
   'C:\\Program Files\\Python313\\Lib\\encodings\\gb2312.py',
   'PYMODULE'),
  ('encodings.gb18030',
   'C:\\Program Files\\Python313\\Lib\\encodings\\gb18030.py',
   'PYMODULE'),
  ('encodings.euc_kr',
   'C:\\Program Files\\Python313\\Lib\\encodings\\euc_kr.py',
   'PYMODULE'),
  ('encodings.euc_jp',
   'C:\\Program Files\\Python313\\Lib\\encodings\\euc_jp.py',
   'PYMODULE'),
  ('encodings.euc_jisx0213',
   'C:\\Program Files\\Python313\\Lib\\encodings\\euc_jisx0213.py',
   'PYMODULE'),
  ('encodings.euc_jis_2004',
   'C:\\Program Files\\Python313\\Lib\\encodings\\euc_jis_2004.py',
   'PYMODULE'),
  ('encodings.cp950',
   'C:\\Program Files\\Python313\\Lib\\encodings\\cp950.py',
   'PYMODULE'),
  ('encodings.cp949',
   'C:\\Program Files\\Python313\\Lib\\encodings\\cp949.py',
   'PYMODULE'),
  ('encodings.cp932',
   'C:\\Program Files\\Python313\\Lib\\encodings\\cp932.py',
   'PYMODULE'),
  ('encodings.cp875',
   'C:\\Program Files\\Python313\\Lib\\encodings\\cp875.py',
   'PYMODULE'),
  ('encodings.cp874',
   'C:\\Program Files\\Python313\\Lib\\encodings\\cp874.py',
   'PYMODULE'),
  ('encodings.cp869',
   'C:\\Program Files\\Python313\\Lib\\encodings\\cp869.py',
   'PYMODULE'),
  ('encodings.cp866',
   'C:\\Program Files\\Python313\\Lib\\encodings\\cp866.py',
   'PYMODULE'),
  ('encodings.cp865',
   'C:\\Program Files\\Python313\\Lib\\encodings\\cp865.py',
   'PYMODULE'),
  ('encodings.cp864',
   'C:\\Program Files\\Python313\\Lib\\encodings\\cp864.py',
   'PYMODULE'),
  ('encodings.cp863',
   'C:\\Program Files\\Python313\\Lib\\encodings\\cp863.py',
   'PYMODULE'),
  ('encodings.cp862',
   'C:\\Program Files\\Python313\\Lib\\encodings\\cp862.py',
   'PYMODULE'),
  ('encodings.cp861',
   'C:\\Program Files\\Python313\\Lib\\encodings\\cp861.py',
   'PYMODULE'),
  ('encodings.cp860',
   'C:\\Program Files\\Python313\\Lib\\encodings\\cp860.py',
   'PYMODULE'),
  ('encodings.cp858',
   'C:\\Program Files\\Python313\\Lib\\encodings\\cp858.py',
   'PYMODULE'),
  ('encodings.cp857',
   'C:\\Program Files\\Python313\\Lib\\encodings\\cp857.py',
   'PYMODULE'),
  ('encodings.cp856',
   'C:\\Program Files\\Python313\\Lib\\encodings\\cp856.py',
   'PYMODULE'),
  ('encodings.cp855',
   'C:\\Program Files\\Python313\\Lib\\encodings\\cp855.py',
   'PYMODULE'),
  ('encodings.cp852',
   'C:\\Program Files\\Python313\\Lib\\encodings\\cp852.py',
   'PYMODULE'),
  ('encodings.cp850',
   'C:\\Program Files\\Python313\\Lib\\encodings\\cp850.py',
   'PYMODULE'),
  ('encodings.cp775',
   'C:\\Program Files\\Python313\\Lib\\encodings\\cp775.py',
   'PYMODULE'),
  ('encodings.cp737',
   'C:\\Program Files\\Python313\\Lib\\encodings\\cp737.py',
   'PYMODULE'),
  ('encodings.cp720',
   'C:\\Program Files\\Python313\\Lib\\encodings\\cp720.py',
   'PYMODULE'),
  ('encodings.cp500',
   'C:\\Program Files\\Python313\\Lib\\encodings\\cp500.py',
   'PYMODULE'),
  ('encodings.cp437',
   'C:\\Program Files\\Python313\\Lib\\encodings\\cp437.py',
   'PYMODULE'),
  ('encodings.cp424',
   'C:\\Program Files\\Python313\\Lib\\encodings\\cp424.py',
   'PYMODULE'),
  ('encodings.cp273',
   'C:\\Program Files\\Python313\\Lib\\encodings\\cp273.py',
   'PYMODULE'),
  ('encodings.cp1258',
   'C:\\Program Files\\Python313\\Lib\\encodings\\cp1258.py',
   'PYMODULE'),
  ('encodings.cp1257',
   'C:\\Program Files\\Python313\\Lib\\encodings\\cp1257.py',
   'PYMODULE'),
  ('encodings.cp1256',
   'C:\\Program Files\\Python313\\Lib\\encodings\\cp1256.py',
   'PYMODULE'),
  ('encodings.cp1255',
   'C:\\Program Files\\Python313\\Lib\\encodings\\cp1255.py',
   'PYMODULE'),
  ('encodings.cp1254',
   'C:\\Program Files\\Python313\\Lib\\encodings\\cp1254.py',
   'PYMODULE'),
  ('encodings.cp1253',
   'C:\\Program Files\\Python313\\Lib\\encodings\\cp1253.py',
   'PYMODULE'),
  ('encodings.cp1252',
   'C:\\Program Files\\Python313\\Lib\\encodings\\cp1252.py',
   'PYMODULE'),
  ('encodings.cp1251',
   'C:\\Program Files\\Python313\\Lib\\encodings\\cp1251.py',
   'PYMODULE'),
  ('encodings.cp1250',
   'C:\\Program Files\\Python313\\Lib\\encodings\\cp1250.py',
   'PYMODULE'),
  ('encodings.cp1140',
   'C:\\Program Files\\Python313\\Lib\\encodings\\cp1140.py',
   'PYMODULE'),
  ('encodings.cp1125',
   'C:\\Program Files\\Python313\\Lib\\encodings\\cp1125.py',
   'PYMODULE'),
  ('encodings.cp1026',
   'C:\\Program Files\\Python313\\Lib\\encodings\\cp1026.py',
   'PYMODULE'),
  ('encodings.cp1006',
   'C:\\Program Files\\Python313\\Lib\\encodings\\cp1006.py',
   'PYMODULE'),
  ('encodings.cp037',
   'C:\\Program Files\\Python313\\Lib\\encodings\\cp037.py',
   'PYMODULE'),
  ('encodings.charmap',
   'C:\\Program Files\\Python313\\Lib\\encodings\\charmap.py',
   'PYMODULE'),
  ('encodings.bz2_codec',
   'C:\\Program Files\\Python313\\Lib\\encodings\\bz2_codec.py',
   'PYMODULE'),
  ('encodings.big5hkscs',
   'C:\\Program Files\\Python313\\Lib\\encodings\\big5hkscs.py',
   'PYMODULE'),
  ('encodings.big5',
   'C:\\Program Files\\Python313\\Lib\\encodings\\big5.py',
   'PYMODULE'),
  ('encodings.base64_codec',
   'C:\\Program Files\\Python313\\Lib\\encodings\\base64_codec.py',
   'PYMODULE'),
  ('encodings.ascii',
   'C:\\Program Files\\Python313\\Lib\\encodings\\ascii.py',
   'PYMODULE'),
  ('encodings.aliases',
   'C:\\Program Files\\Python313\\Lib\\encodings\\aliases.py',
   'PYMODULE'),
  ('encodings',
   'C:\\Program Files\\Python313\\Lib\\encodings\\__init__.py',
   'PYMODULE'),
  ('functools', 'C:\\Program Files\\Python313\\Lib\\functools.py', 'PYMODULE'),
  ('locale', 'C:\\Program Files\\Python313\\Lib\\locale.py', 'PYMODULE'),
  ('sre_compile',
   'C:\\Program Files\\Python313\\Lib\\sre_compile.py',
   'PYMODULE'),
  ('weakref', 'C:\\Program Files\\Python313\\Lib\\weakref.py', 'PYMODULE'),
  ('collections',
   'C:\\Program Files\\Python313\\Lib\\collections\\__init__.py',
   'PYMODULE'),
  ('copyreg', 'C:\\Program Files\\Python313\\Lib\\copyreg.py', 'PYMODULE'),
  ('genericpath',
   'C:\\Program Files\\Python313\\Lib\\genericpath.py',
   'PYMODULE'),
  ('enum', 'C:\\Program Files\\Python313\\Lib\\enum.py', 'PYMODULE'),
  ('heapq', 'C:\\Program Files\\Python313\\Lib\\heapq.py', 'PYMODULE'),
  ('types', 'C:\\Program Files\\Python313\\Lib\\types.py', 'PYMODULE'),
  ('_weakrefset',
   'C:\\Program Files\\Python313\\Lib\\_weakrefset.py',
   'PYMODULE'),
  ('io', 'C:\\Program Files\\Python313\\Lib\\io.py', 'PYMODULE'),
  ('_collections_abc',
   'C:\\Program Files\\Python313\\Lib\\_collections_abc.py',
   'PYMODULE'),
  ('traceback', 'C:\\Program Files\\Python313\\Lib\\traceback.py', 'PYMODULE'),
  ('os', 'C:\\Program Files\\Python313\\Lib\\os.py', 'PYMODULE')])
