"""
基于PyQt6的调度服务模块
使用QTimer替换schedule库，提供原生PyQt6定时任务支持
"""

from typing import Callable, Dict, Any, Optional
from datetime import datetime, time as dt_time
from PyQt6.QtCore import QObject, QTimer, QTime, pyqtSignal


class QtSchedulerService(QObject):
    """基于PyQt6的调度服务类，使用QTimer管理所有定时任务"""
    
    # 信号定义
    jobExecuted = pyqtSignal(str, str)      # tag, function_name
    jobError = pyqtSignal(str, str, str)    # tag, function_name, error
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self._timers = {}           # 存储QTimer对象
        self._jobs = {}             # 存储任务信息
        self._is_running = False
        
        # 心跳定时器，用于状态监控
        self._heartbeat_timer = QTimer(self)
        self._heartbeat_timer.timeout.connect(self._heartbeat)
        
    def start(self):
        """启动调度器"""
        if self._is_running:
            print("PyQt6调度器已在运行")
            return
        
        self._is_running = True
        
        # 启动心跳监控（每5分钟）
        self._heartbeat_timer.start(5 * 60 * 1000)
        
        print("PyQt6调度器已启动")
    
    def stop(self):
        """停止调度器"""
        if not self._is_running:
            return
        
        # 停止所有定时器
        for timer in self._timers.values():
            timer.stop()
        
        self._heartbeat_timer.stop()
        self._is_running = False
        
        print("PyQt6调度器已停止")
    
    def _heartbeat(self):
        """心跳监控"""
        active_jobs = len([t for t in self._timers.values() if t.isActive()])
        print(f"[PyQt6调度器心跳] 当前活跃任务数: {active_jobs}")
        
        # 检查任务状态
        self._check_tasks_status()
    
    def _check_tasks_status(self):
        """检查任务状态"""
        print(f"[PyQt6调度器] 任务状态检查 - 总任务数: {len(self._jobs)}")
        
        for tag, job_info in self._jobs.items():
            timer = self._timers.get(tag)
            if timer:
                remaining_ms = timer.remainingTime()
                if remaining_ms > 0:
                    remaining_hours = remaining_ms // (1000 * 60 * 60)
                    remaining_minutes = (remaining_ms % (1000 * 60 * 60)) // (1000 * 60)
                    print(f"[PyQt6调度器] 任务: {job_info['function']}, "
                          f"剩余时间: {remaining_hours}h {remaining_minutes}m")
    
    def add_daily_job(self, time_str: str, func: Callable, tag: str, 
                     *args, **kwargs) -> bool:
        """
        添加每日定时任务
        
        Args:
            time_str: 时间字符串，格式为 "HH:MM"
            func: 要执行的函数
            tag: 任务标签
            *args: 函数参数
            **kwargs: 函数关键字参数
            
        Returns:
            添加是否成功
        """
        try:
            # 验证时间格式
            hour, minute = map(int, time_str.split(':'))
            if not (0 <= hour <= 23 and 0 <= minute <= 59):
                raise ValueError("时间值超出范围")
            
            # 清除同标签的旧任务
            self.clear_jobs_by_tag(tag)
            
            # 计算到目标时间的毫秒数
            target_time = QTime(hour, minute)
            current_time = QTime.currentTime()
            
            # 计算时间差
            msecs_to_target = current_time.msecsTo(target_time)
            
            # 如果目标时间已过，设置为明天
            if msecs_to_target <= 0:
                msecs_to_target += 24 * 60 * 60 * 1000  # 加一天
            
            # 创建定时器
            timer = QTimer(self)
            timer.setSingleShot(True)  # 单次触发
            
            # 创建任务包装函数
            def job_wrapper():
                try:
                    print(f"[PyQt6调度器] 执行任务: {func.__name__} (标签: {tag})")
                    result = func(*args, **kwargs)
                    print(f"[PyQt6调度器] 任务完成: {func.__name__}")
                    
                    # 发射成功信号
                    self.jobExecuted.emit(tag, func.__name__)
                    
                    # 重新安排明天的任务
                    self._reschedule_daily_job(time_str, func, tag, *args, **kwargs)
                    
                    return result
                    
                except Exception as e:
                    error_msg = f"任务执行出错: {func.__name__}, 错误: {e}"
                    print(f"[PyQt6调度器] {error_msg}")
                    
                    # 发射错误信号
                    self.jobError.emit(tag, func.__name__, str(e))
                    
                    # 即使出错也要重新安排任务
                    self._reschedule_daily_job(time_str, func, tag, *args, **kwargs)
            
            # 连接定时器
            timer.timeout.connect(job_wrapper)
            
            # 启动定时器
            timer.start(msecs_to_target)
            
            # 存储定时器和任务信息
            self._timers[tag] = timer
            self._jobs[tag] = {
                'time': time_str,
                'function': func.__name__,
                'args': args,
                'kwargs': kwargs,
                'created_at': datetime.now(),
                'next_run': datetime.now().replace(hour=hour, minute=minute, second=0, microsecond=0)
            }
            
            # 如果是明天，调整next_run
            if msecs_to_target > 23 * 60 * 60 * 1000:  # 超过23小时说明是明天
                from datetime import timedelta
                self._jobs[tag]['next_run'] += timedelta(days=1)
            
            print(f"[PyQt6调度器] 已添加每日任务: {func.__name__} 在 {time_str} (标签: {tag})")
            print(f"[PyQt6调度器] 下次执行时间: {self._jobs[tag]['next_run']}")
            
            return True
            
        except Exception as e:
            print(f"[PyQt6调度器] 添加任务失败: {e}")
            return False
    
    def _reschedule_daily_job(self, time_str: str, func: Callable, tag: str, 
                             *args, **kwargs):
        """重新安排每日任务（用于循环执行）"""
        # 24小时后再次执行
        timer = QTimer(self)
        timer.setSingleShot(True)
        
        def job_wrapper():
            try:
                print(f"[PyQt6调度器] 执行任务: {func.__name__} (标签: {tag})")
                result = func(*args, **kwargs)
                print(f"[PyQt6调度器] 任务完成: {func.__name__}")
                
                self.jobExecuted.emit(tag, func.__name__)
                
                # 继续重新安排
                self._reschedule_daily_job(time_str, func, tag, *args, **kwargs)
                
                return result
                
            except Exception as e:
                error_msg = f"任务执行出错: {func.__name__}, 错误: {e}"
                print(f"[PyQt6调度器] {error_msg}")
                
                self.jobError.emit(tag, func.__name__, str(e))
                
                # 即使出错也要重新安排任务
                self._reschedule_daily_job(time_str, func, tag, *args, **kwargs)
        
        timer.timeout.connect(job_wrapper)
        timer.start(24 * 60 * 60 * 1000)  # 24小时
        
        # 更新存储
        self._timers[tag] = timer
        if tag in self._jobs:
            from datetime import timedelta
            self._jobs[tag]['next_run'] += timedelta(days=1)
    
    def add_interval_job(self, interval_seconds: int, func: Callable, tag: str,
                        *args, **kwargs) -> bool:
        """
        添加间隔执行任务
        
        Args:
            interval_seconds: 执行间隔（秒）
            func: 要执行的函数
            tag: 任务标签
            *args: 函数参数
            **kwargs: 函数关键字参数
            
        Returns:
            添加是否成功
        """
        try:
            # 清除同标签的旧任务
            self.clear_jobs_by_tag(tag)
            
            # 创建定时器
            timer = QTimer(self)
            timer.setInterval(interval_seconds * 1000)  # 转换为毫秒
            
            # 创建任务包装函数
            def job_wrapper():
                try:
                    print(f"[PyQt6调度器] 执行间隔任务: {func.__name__} (标签: {tag})")
                    result = func(*args, **kwargs)
                    self.jobExecuted.emit(tag, func.__name__)
                    return result
                    
                except Exception as e:
                    error_msg = f"间隔任务执行出错: {func.__name__}, 错误: {e}"
                    print(f"[PyQt6调度器] {error_msg}")
                    self.jobError.emit(tag, func.__name__, str(e))
            
            # 连接定时器
            timer.timeout.connect(job_wrapper)
            
            # 启动定时器
            timer.start()
            
            # 存储定时器和任务信息
            self._timers[tag] = timer
            self._jobs[tag] = {
                'interval': interval_seconds,
                'function': func.__name__,
                'args': args,
                'kwargs': kwargs,
                'created_at': datetime.now(),
                'type': 'interval'
            }
            
            print(f"[PyQt6调度器] 已添加间隔任务: {func.__name__} 每 {interval_seconds} 秒 (标签: {tag})")
            return True
            
        except Exception as e:
            print(f"[PyQt6调度器] 添加间隔任务失败: {e}")
            return False
    
    def clear_jobs_by_tag(self, tag: str):
        """根据标签清除任务"""
        if tag in self._timers:
            self._timers[tag].stop()
            del self._timers[tag]
            print(f"[PyQt6调度器] 已清除定时器: {tag}")
        
        if tag in self._jobs:
            del self._jobs[tag]
            print(f"[PyQt6调度器] 已清除任务信息: {tag}")
    
    def get_jobs(self) -> Dict[str, Dict[str, Any]]:
        """获取所有任务信息"""
        return self._jobs.copy()
    
    def get_job_count(self) -> int:
        """获取任务数量"""
        return len(self._jobs)
    
    def is_job_active(self, tag: str) -> bool:
        """检查任务是否活跃"""
        timer = self._timers.get(tag)
        return timer is not None and timer.isActive()
    
    def cleanup(self):
        """清理资源"""
        self.stop()
        
        # 清理所有定时器
        for timer in self._timers.values():
            timer.deleteLater()
        
        self._timers.clear()
        self._jobs.clear()
        
        print("PyQt6调度服务已清理")
    
    def __del__(self):
        """析构函数"""
        self.cleanup()


# 兼容性别名，保持与原SchedulerService的接口一致
class SchedulerService(QtSchedulerService):
    """兼容性别名"""
    pass
