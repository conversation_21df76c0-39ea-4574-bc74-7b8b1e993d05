"""
顺序执行器模块
用于按顺序执行多个步骤，每个步骤完成后才执行下一步
"""

import threading
import time
from typing import List, Callable, Optional, Dict, Any
from enum import Enum
from PyQt6.QtCore import QObject, pyqtSignal, QThread, QTimer


class StepStatus(Enum):
    """步骤状态枚举"""
    PENDING = "pending"      # 等待执行
    RUNNING = "running"      # 正在执行
    COMPLETED = "completed"  # 已完成
    FAILED = "failed"        # 执行失败
    CANCELLED = "cancelled"  # 已取消


class ExecutionStep:
    """执行步骤类"""
    
    def __init__(self, name: str, description: str, func: Callable, 
                 args: tuple = (), kwargs: dict = None):
        self.name = name
        self.description = description
        self.func = func
        self.args = args or ()
        self.kwargs = kwargs or {}
        self.status = StepStatus.PENDING
        self.result = None
        self.error = None
        self.start_time = None
        self.end_time = None
    
    def execute(self) -> bool:
        """执行步骤"""
        try:
            self.status = StepStatus.RUNNING
            self.start_time = time.time()
            
            # 执行函数
            self.result = self.func(*self.args, **self.kwargs)
            
            self.end_time = time.time()
            self.status = StepStatus.COMPLETED
            return True
            
        except Exception as e:
            self.end_time = time.time()
            self.error = str(e)
            self.status = StepStatus.FAILED
            return False
    
    def get_duration(self) -> Optional[float]:
        """获取执行时长"""
        if self.start_time and self.end_time:
            return self.end_time - self.start_time
        return None


class SequentialExecutorWorker(QThread):
    """顺序执行器工作线程"""

    # 信号定义
    step_started = pyqtSignal(str, str)  # step_name, description
    step_completed = pyqtSignal(str, bool, str)  # step_name, success, message
    step_progress = pyqtSignal(str, str)  # step_name, progress_message
    execution_completed = pyqtSignal(bool, str)  # success, summary_message
    execution_cancelled = pyqtSignal()

    def __init__(self, steps: List['ExecutionStep'], parent=None):
        super().__init__(parent)
        self.steps = steps
        self.current_step_index = 0
        self.is_cancelled = False
        self.cancel_event = threading.Event()

    def cancel_execution(self):
        """取消执行"""
        self.is_cancelled = True
        self.cancel_event.set()
        self.requestInterruption()

    def run(self):
        """执行所有步骤（在QThread中运行）"""
        try:
            success_count = 0
            total_steps = len(self.steps)

            for i, step in enumerate(self.steps):
                # 检查是否被取消或中断
                if self.isInterruptionRequested() or self.cancel_event.is_set():
                    break

                self.current_step_index = i

                # 发送步骤开始信号
                self.step_started.emit(step.name, step.description)

                # 执行步骤
                success = step.execute()

                # 检查是否被取消
                if self.isInterruptionRequested() or self.cancel_event.is_set():
                    break

                # 发送步骤完成信号
                if success:
                    success_count += 1
                    duration = step.get_duration()
                    duration_str = f" (耗时: {duration:.2f}秒)" if duration else ""
                    message = f"步骤完成{duration_str}"
                    self.step_completed.emit(step.name, True, message)
                else:
                    error_msg = step.error or "未知错误"
                    self.step_completed.emit(step.name, False, f"步骤失败: {error_msg}")

                    # 如果步骤失败，停止后续执行
                    break

                # 短暂延迟，让UI有时间更新
                self.msleep(100)  # 使用QThread的msleep而不是time.sleep

            # 发送执行完成信号
            if self.isInterruptionRequested() or self.cancel_event.is_set():
                summary = "执行已取消"
                self.execution_completed.emit(False, summary)
                self.execution_cancelled.emit()
            elif success_count == total_steps:
                summary = f"所有步骤执行成功 ({success_count}/{total_steps})"
                self.execution_completed.emit(True, summary)
            else:
                summary = f"执行中断，成功: {success_count}/{total_steps}"
                self.execution_completed.emit(False, summary)

        except Exception as e:
            error_msg = f"执行器内部错误: {e}"
            self.execution_completed.emit(False, error_msg)


class SequentialExecutor(QObject):
    """顺序执行器"""

    # 信号定义
    step_started = pyqtSignal(str, str)  # step_name, description
    step_completed = pyqtSignal(str, bool, str)  # step_name, success, message
    step_progress = pyqtSignal(str, str)  # step_name, progress_message
    execution_completed = pyqtSignal(bool, str)  # success, summary_message
    execution_cancelled = pyqtSignal()

    def __init__(self, parent=None):
        super().__init__(parent)
        self.steps: List[ExecutionStep] = []
        self.current_step_index = 0
        self.is_running = False
        self.worker_thread = None
    
    def add_step(self, name: str, description: str, func: Callable, 
                 args: tuple = (), kwargs: dict = None):
        """添加执行步骤"""
        step = ExecutionStep(name, description, func, args, kwargs)
        self.steps.append(step)
    
    def clear_steps(self):
        """清空所有步骤"""
        self.steps.clear()
        self.current_step_index = 0
    
    def start_execution(self):
        """开始执行所有步骤"""
        if self.is_running:
            return False

        if not self.steps:
            return False

        self.is_running = True
        self.current_step_index = 0

        # 重置所有步骤状态
        for step in self.steps:
            step.status = StepStatus.PENDING
            step.result = None
            step.error = None
            step.start_time = None
            step.end_time = None

        # 创建工作线程
        self.worker_thread = SequentialExecutorWorker(self.steps, self)

        # 连接信号
        self.worker_thread.step_started.connect(self.step_started.emit)
        self.worker_thread.step_completed.connect(self.step_completed.emit)
        self.worker_thread.step_progress.connect(self.step_progress.emit)
        self.worker_thread.execution_completed.connect(self._on_execution_completed)
        self.worker_thread.execution_cancelled.connect(self.execution_cancelled.emit)

        # 启动线程
        self.worker_thread.start()

        return True
    
    def cancel_execution(self):
        """取消执行"""
        if not self.is_running or not self.worker_thread:
            return

        # 请求工作线程取消
        self.worker_thread.cancel_execution()

        # 标记当前步骤为已取消
        if self.current_step_index < len(self.steps):
            current_step = self.steps[self.current_step_index]
            if current_step.status == StepStatus.RUNNING:
                current_step.status = StepStatus.CANCELLED

    def _on_execution_completed(self, success: bool, summary: str):
        """执行完成处理"""
        self.is_running = False

        # 清理工作线程
        if self.worker_thread:
            self.worker_thread.quit()
            self.worker_thread.wait(2000)  # 等待2秒
            self.worker_thread = None

        # 转发信号
        self.execution_completed.emit(success, summary)
    
    def get_execution_summary(self) -> Dict[str, Any]:
        """获取执行摘要"""
        summary = {
            'total_steps': len(self.steps),
            'completed_steps': 0,
            'failed_steps': 0,
            'cancelled_steps': 0,
            'total_duration': 0.0,
            'steps_detail': []
        }
        
        for step in self.steps:
            step_info = {
                'name': step.name,
                'description': step.description,
                'status': step.status.value,
                'duration': step.get_duration(),
                'error': step.error
            }
            summary['steps_detail'].append(step_info)
            
            if step.status == StepStatus.COMPLETED:
                summary['completed_steps'] += 1
                if step_info['duration']:
                    summary['total_duration'] += step_info['duration']
            elif step.status == StepStatus.FAILED:
                summary['failed_steps'] += 1
            elif step.status == StepStatus.CANCELLED:
                summary['cancelled_steps'] += 1
        
        return summary
    
    def is_execution_running(self) -> bool:
        """检查是否正在执行"""
        return self.is_running
    
    def get_current_step(self) -> Optional[ExecutionStep]:
        """获取当前步骤"""
        if 0 <= self.current_step_index < len(self.steps):
            return self.steps[self.current_step_index]
        return None
    
    def get_progress_percentage(self) -> float:
        """获取执行进度百分比"""
        if not self.steps:
            return 0.0
        
        completed = sum(1 for step in self.steps if step.status == StepStatus.COMPLETED)
        return (completed / len(self.steps)) * 100.0
