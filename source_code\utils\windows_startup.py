"""
Windows开机启动管理模块
负责管理应用程序在Windows系统的开机自启动功能
"""

import os
import sys
import winreg
from typing import Optional
from pathlib import Path


class WindowsStartupManager:
    """Windows开机启动管理器"""
    
    # Windows注册表启动项路径
    STARTUP_REG_PATH = r"Software\Microsoft\Windows\CurrentVersion\Run"
    
    def __init__(self, app_name: str = "ServerCheckerTool"):
        self.app_name = app_name
        self.executable_path = self._get_executable_path()
    
    def _get_executable_path(self) -> str:
        """获取应用程序可执行文件路径"""
        try:
            if getattr(sys, 'frozen', False):
                # 如果是打包后的exe文件
                return sys.executable
            else:
                # 如果是Python脚本，返回Python解释器+脚本路径
                script_path = os.path.abspath(sys.argv[0])
                python_path = sys.executable
                return f'"{python_path}" "{script_path}"'
        except Exception as e:
            print(f"获取可执行文件路径失败: {e}")
            return ""
    
    def is_startup_enabled(self) -> bool:
        """检查是否已设置开机启动"""
        try:
            with winreg.OpenKey(winreg.HKEY_CURRENT_USER, self.STARTUP_REG_PATH, 0, winreg.KEY_READ) as key:
                try:
                    value, _ = winreg.QueryValueEx(key, self.app_name)
                    return bool(value)
                except FileNotFoundError:
                    return False
        except Exception as e:
            print(f"检查开机启动状态失败: {e}")
            return False
    
    def enable_startup(self) -> bool:
        """启用开机启动"""
        try:
            if not self.executable_path:
                print("无法获取可执行文件路径，启用开机启动失败")
                return False
            
            with winreg.OpenKey(winreg.HKEY_CURRENT_USER, self.STARTUP_REG_PATH, 0, 
                              winreg.KEY_SET_VALUE) as key:
                winreg.SetValueEx(key, self.app_name, 0, winreg.REG_SZ, self.executable_path)
                print(f"✅ 已启用开机启动: {self.app_name}")
                return True
                
        except Exception as e:
            print(f"启用开机启动失败: {e}")
            return False
    
    def disable_startup(self) -> bool:
        """禁用开机启动"""
        try:
            with winreg.OpenKey(winreg.HKEY_CURRENT_USER, self.STARTUP_REG_PATH, 0, 
                              winreg.KEY_SET_VALUE) as key:
                try:
                    winreg.DeleteValue(key, self.app_name)
                    print(f"✅ 已禁用开机启动: {self.app_name}")
                    return True
                except FileNotFoundError:
                    print("开机启动项不存在，无需删除")
                    return True
                    
        except Exception as e:
            print(f"禁用开机启动失败: {e}")
            return False
    
    def toggle_startup(self, enable: bool) -> bool:
        """切换开机启动状态"""
        if enable:
            return self.enable_startup()
        else:
            return self.disable_startup()
    
    def get_startup_info(self) -> dict:
        """获取开机启动信息"""
        return {
            'enabled': self.is_startup_enabled(),
            'app_name': self.app_name,
            'executable_path': self.executable_path,
            'registry_path': f"HKEY_CURRENT_USER\\{self.STARTUP_REG_PATH}"
        }


def create_startup_manager(app_name: str = "ServerCheckerTool") -> Optional[WindowsStartupManager]:
    """创建启动管理器（仅在Windows平台）"""
    if sys.platform != 'win32':
        print("开机启动功能仅支持Windows平台")
        return None
    
    try:
        return WindowsStartupManager(app_name)
    except Exception as e:
        print(f"创建启动管理器失败: {e}")
        return None