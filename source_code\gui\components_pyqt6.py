"""
GUI组件模块 - PyQt6版本
包含自定义的GUI组件和工具类
"""

from typing import Optional
from PyQt6.QtWidgets import (
    QWidget, QLabel, QFrame, QScrollArea, QVBoxLayout, QHBoxLayout,
    QDialog, QProgressBar, QPushButton, QMessageBox, QStatusBar,
    QApplication, QToolTip
)
from PyQt6.QtCore import Qt, QTimer, QPoint, QRect, pyqtSignal
from PyQt6.QtGui import QFont, QPalette, QEnterEvent, QMouseEvent


class Tooltip(QWidget):
    """工具提示组件，当鼠标悬停在小部件上时显示提示信息"""
    
    def __init__(self, widget: QWidget, text: str, scale_factor: float = 1.0):
        super().__init__()
        self.target_widget = widget
        self.text = text
        self.scale_factor = scale_factor
        self.tooltip_timer = QTimer()
        self.tooltip_timer.setSingleShot(True)
        self.tooltip_timer.timeout.connect(self.show_tooltip)
        
        # Install event filter on target widget
        self.target_widget.installEventFilter(self)
        
        # Setup tooltip window
        self.setWindowFlags(Qt.WindowType.ToolTip | Qt.WindowType.FramelessWindowHint)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        
        # Create layout and label
        layout = QVBoxLayout(self)
        layout.setContentsMargins(8, 4, 8, 4)
        
        self.label = QLabel(text)
        self.label.setWordWrap(True)
        
        # Calculate scaled font size
        base_font_size = 9
        scaled_font_size = int(base_font_size * scale_factor)
        font = QFont()
        font.setPointSize(scaled_font_size)
        self.label.setFont(font)
        
        # Style the tooltip
        self.label.setStyleSheet("""
            QLabel {
                background-color: #FFFFE0;
                color: black;
                border: 1px solid #999999;
                border-radius: 3px;
                padding: 4px;
            }
        """)
        
        layout.addWidget(self.label)
        self.hide()
    
    def eventFilter(self, obj, event):
        """Event filter to handle mouse enter/leave events"""
        if obj == self.target_widget:
            if event.type() == QEnterEvent.Type.Enter:
                self.tooltip_timer.start(500)  # Show after 500ms delay
            elif event.type() == QMouseEvent.Type.Leave:
                self.tooltip_timer.stop()
                self.hide()
        return super().eventFilter(obj, event)
    
    def show_tooltip(self):
        """Show the tooltip near the mouse cursor"""
        if not self.target_widget.isVisible():
            return
        
        # Get global mouse position
        cursor_pos = QApplication.instance().primaryScreen().availableGeometry().topLeft()
        cursor_pos = self.target_widget.mapToGlobal(QPoint(0, self.target_widget.height()))
        
        # Adjust size to content
        self.adjustSize()
        
        # Position tooltip
        self.move(cursor_pos.x() + 10, cursor_pos.y() + 5)
        self.show()
        self.raise_()
    
    def set_text(self, text: str):
        """Update tooltip text"""
        self.text = text
        self.label.setText(text)


class ScrollableFrame(QScrollArea):
    """可滚动的框架组件"""
    
    def __init__(self, parent=None, **kwargs):
        super().__init__(parent)
        
        # Create scroll widget and layout
        self.scroll_widget = QWidget()
        self.scroll_layout = QVBoxLayout(self.scroll_widget)
        self.scroll_layout.setAlignment(Qt.AlignmentFlag.AlignTop)
        
        # Configure scroll area
        self.setWidget(self.scroll_widget)
        self.setWidgetResizable(True)
        self.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        self.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        
        # Track items
        self.items = []
    
    def add_item(self, item_widget: QWidget):
        """添加项目到滚动框架"""
        self.items.append(item_widget)
        self.scroll_layout.addWidget(item_widget)
    
    def remove_item(self, item_widget: QWidget):
        """从滚动框架移除项目"""
        if item_widget in self.items:
            self.items.remove(item_widget)
            self.scroll_layout.removeWidget(item_widget)
            item_widget.setParent(None)
    
    def clear_items(self):
        """清空所有项目"""
        for item in self.items:
            self.scroll_layout.removeWidget(item)
            item.setParent(None)
        self.items.clear()
    
    def get_item_count(self) -> int:
        """获取项目数量"""
        return len(self.items)


class ProgressDialog(QDialog):
    """进度对话框"""
    
    cancelled = pyqtSignal()
    
    def __init__(self, parent=None, title: str = "处理中...", message: str = "请稍候..."):
        super().__init__(parent)
        
        self.setWindowTitle(title)
        self.setFixedSize(400, 150)
        self.setModal(True)
        
        # Center on parent
        if parent:
            self.center_on_parent(parent)
        
        # Create UI
        self.create_widgets(message)
        
        # Progress tracking
        self._cancelled = False
    
    def center_on_parent(self, parent):
        """在父窗口中居中显示"""
        parent_rect = parent.geometry()
        dialog_rect = self.geometry()
        
        x = parent_rect.x() + (parent_rect.width() - dialog_rect.width()) // 2
        y = parent_rect.y() + (parent_rect.height() - dialog_rect.height()) // 2
        
        self.move(x, y)
    
    def create_widgets(self, message: str):
        """创建界面组件"""
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # Status label
        self.status_label = QLabel(message)
        self.status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        font = QFont()
        font.setPointSize(11)
        self.status_label.setFont(font)
        layout.addWidget(self.status_label)
        
        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        layout.addWidget(self.progress_bar)
        
        # Cancel button
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        self.cancel_button = QPushButton("取消")
        self.cancel_button.setMinimumWidth(100)
        self.cancel_button.clicked.connect(self.cancel)
        button_layout.addWidget(self.cancel_button)
        
        button_layout.addStretch()
        layout.addLayout(button_layout)
    
    def update_progress(self, value: float, status: Optional[str] = None):
        """更新进度 (value: 0.0-1.0)"""
        progress_value = int(value * 100)
        self.progress_bar.setValue(progress_value)
        
        if status:
            self.status_label.setText(status)
        
        # Process events to update UI
        QApplication.processEvents()
    
    def cancel(self):
        """取消操作"""
        self._cancelled = True
        self.cancelled.emit()
        self.reject()
    
    def is_cancelled(self) -> bool:
        """检查是否被取消"""
        return self._cancelled


class ConfirmDialog(QMessageBox):
    """确认对话框"""
    
    def __init__(self, parent=None, title: str = "确认", message: str = "", 
                 confirm_text: str = "确定", cancel_text: str = "取消"):
        super().__init__(parent)
        
        self.setWindowTitle(title)
        self.setText(message)
        self.setIcon(QMessageBox.Icon.Question)
        
        # Add custom buttons
        self.confirm_button = self.addButton(confirm_text, QMessageBox.ButtonRole.AcceptRole)
        self.cancel_button = self.addButton(cancel_text, QMessageBox.ButtonRole.RejectRole)
        
        # Set default button
        self.setDefaultButton(self.confirm_button)
    
    def get_result(self) -> bool:
        """获取结果"""
        result = self.exec()
        return result == QMessageBox.StandardButton.Ok or self.clickedButton() == self.confirm_button


class StatusBar(QStatusBar):
    """状态栏组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # Status message
        self.status_message = "就绪"
        self.showMessage(self.status_message)
        
        # Progress bar (initially hidden)
        self.progress_bar = QProgressBar()
        self.progress_bar.setMaximumWidth(200)
        self.progress_bar.setVisible(False)
        self.addPermanentWidget(self.progress_bar)
    
    def set_status(self, status: str):
        """设置状态文本"""
        self.status_message = status
        self.showMessage(status)
    
    def set_progress(self, value: float):
        """设置进度值 (0.0 - 1.0)"""
        progress_value = int(max(0.0, min(1.0, value)) * 100)
        self.progress_bar.setValue(progress_value)
        
        if not self.progress_bar.isVisible():
            self.progress_bar.setVisible(True)
    
    def reset_progress(self):
        """重置进度"""
        self.progress_bar.setValue(0)
        self.progress_bar.setVisible(False)
    
    def show_progress(self, show: bool = True):
        """显示或隐藏进度条"""
        self.progress_bar.setVisible(show)
