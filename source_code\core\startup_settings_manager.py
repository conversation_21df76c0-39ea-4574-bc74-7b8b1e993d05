"""
启动设置管理器
集成Windows启动管理和配置管理
"""

import sys
from typing import Optional
from PyQt6.QtCore import QObject, pyqtSignal

from utils.windows_startup import create_startup_manager
from utils.smart_logger import create_debug_logger_for_class


@create_debug_logger_for_class
class StartupSettingsManager(QObject):
    """启动设置管理器 - 统一管理开机启动功能"""
    
    # 信号定义
    startup_status_changed = pyqtSignal(bool)  # 启动状态改变
    
    def __init__(self, app_instance):
        super().__init__()
        self.app = app_instance
        self.windows_startup_manager = None
        
        # 初始化Windows启动管理器
        if sys.platform == 'win32':
            self.windows_startup_manager = create_startup_manager("ServerCheckerTool")
            if self.windows_startup_manager:
                self.debug_log("Windows启动管理器初始化成功")
            else:
                self.debug_log("Windows启动管理器初始化失败")
        else:
            self.debug_log("非Windows平台，跳过启动管理器初始化")
    
    def is_startup_supported(self) -> bool:
        """检查是否支持开机启动"""
        return self.windows_startup_manager is not None
    
    def is_startup_enabled(self) -> bool:
        """检查开机启动是否已启用"""
        if not self.is_startup_supported():
            return False
        
        try:
            # 检查注册表中的实际状态
            registry_enabled = self.windows_startup_manager.is_startup_enabled()
            
            # 检查配置文件中的状态
            config_enabled = self.app.config.get_startup_enabled()
            
            self.debug_log(f"注册表状态: {registry_enabled}, 配置状态: {config_enabled}")
            
            # 如果状态不一致，以注册表为准并更新配置
            if registry_enabled != config_enabled:
                self.debug_log("启动状态不一致，同步配置")
                self.app.config.set_startup_enabled(registry_enabled)
                self.app.config.save_config()
            
            return registry_enabled
            
        except Exception as e:
            self.debug_log(f"检查启动状态失败: {e}")
            return False
    
    def set_startup_enabled(self, enabled: bool) -> bool:
        """设置开机启动状态"""
        if not self.is_startup_supported():
            self.debug_log("当前平台不支持开机启动")
            return False
        
        try:
            # 更新注册表
            success = self.windows_startup_manager.toggle_startup(enabled)
            
            if success:
                # 更新配置文件
                self.app.config.set_startup_enabled(enabled)
                self.app.config.save_config()
                
                # 发射状态改变信号
                self.startup_status_changed.emit(enabled)
                
                action = "启用" if enabled else "禁用"
                self.debug_log(f"成功{action}开机启动")
                
                # 记录到应用日志
                if hasattr(self.app, 'log'):
                    self.app.log(f"✅ 开机启动已{action}", "SUCCESS")
                
                return True
            else:
                self.debug_log("设置启动状态失败")
                return False
                
        except Exception as e:
            self.debug_log(f"设置启动状态时出错: {e}")
            if hasattr(self.app, 'log'):
                self.app.log(f"❌ 设置开机启动失败: {e}", "ERROR")
            return False
    
    def sync_startup_status(self):
        """同步启动状态（检查并修复配置与注册表的不一致）"""
        if not self.is_startup_supported():
            return
        
        try:
            current_status = self.is_startup_enabled()
            self.debug_log(f"当前启动状态: {current_status}")
            
            # 发射当前状态信号以更新UI
            self.startup_status_changed.emit(current_status)
            
        except Exception as e:
            self.debug_log(f"同步启动状态时出错: {e}")
    
    def get_startup_info(self) -> dict:
        """获取详细的启动状态信息"""
        info = {
            'supported': self.is_startup_supported(),
            'enabled': False,
            'platform': sys.platform,
            'error': None
        }
        
        if self.is_startup_supported():
            try:
                info.update(self.windows_startup_manager.get_startup_info())
                info['config_enabled'] = self.app.config.get_startup_enabled()
            except Exception as e:
                info['error'] = str(e)
        
        return info
    
    def restore_startup_state(self):
        """恢复启动状态（从配置文件恢复）"""
        if not self.is_startup_supported():
            return
        
        try:
            config_enabled = self.app.config.get_startup_enabled()
            actual_enabled = self.windows_startup_manager.is_startup_enabled()
            
            # 如果配置和实际状态不一致，以配置为准进行修复
            if config_enabled != actual_enabled:
                self.debug_log(f"启动状态不一致，修复为配置状态: {config_enabled}")
                self.set_startup_enabled(config_enabled)
            else:
                self.debug_log(f"启动状态一致: {config_enabled}")
                
        except Exception as e:
            self.debug_log(f"恢复启动状态时出错: {e}")
    
    def _log(self, message: str, level: str = "INFO"):
        """内部日志方法"""
        if hasattr(self.app, 'log'):
            self.app.log(message, level)